// src/components/hooks/useCrewContext.ts
import { usePathname, useSearchParams } from 'next/navigation'
import { useCrewMemberName } from '@/app/hooks/use-crew-member-name'

export function useCrewContext() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  let crewMemberID = 0

  // Get crew member ID from URL parameters when on crew info page
  if (searchParams.get('id') && pathname.includes('/crew/info')) {
    crewMemberID = +searchParams.get('id')!
  }

  const { crewMemberName } = useCrewMemberName(crewMemberID)

  return { crewMemberID, crewMemberName, pathname }
}

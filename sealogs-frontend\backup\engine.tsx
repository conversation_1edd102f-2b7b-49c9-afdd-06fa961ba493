'use client'
import React, { useEffect, useState } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import {
    UPDATE_ENGINE,
    UpdateFuelTank,
    UpdateVesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/mutation'
import {
    AlertDialog,
    CustomDailyCheckField,
    DailyCheckField,
    DailyCheckGroupField,
    FooterWrapper,
    SeaLogsButton,
} from '@/app/components/Components'
import {
    GET_ENGINES,
    GET_FUELLOGS,
    GET_FUELTANKS,
    GET_LOGBOOK_ENTRY_BY_ID,
    GET_SECTION_MEMBER_COMMENTS,
    LogBookSignOff_LogBookEntrySection,
    TripReport_LogBookEntrySection,
    VesselDailyCheck_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import { useSearchParams } from 'next/navigation'
import toast, { Toaster } from 'react-hot-toast'
import {
    getDailyCheckNotification,
    displayDescription,
    composeField,
    displayField,
    getFilteredFields,
    getFieldLabel,
} from '@/app/ui/daily-checks/actions'
import { classes } from '@/app/components/GlobalClasses'
import VesselFuelStatus from '../vessels/logbookFuelStatus'
import { getLogBookEntryByID, getVesselByID } from '@/app/lib/actions'
import dayjs from 'dayjs'
import { Button, Heading } from 'react-aria-components'
import SlidingPanel from 'react-sliding-side-panel'
import { XMarkIcon } from '@heroicons/react/24/outline'
import 'react-quill/dist/quill.snow.css'
import VesselModel from '@/app/offline/models/vessel'
import EngineModel from '@/app/offline/models/engine'
import FuelTankModel from '@/app/offline/models/fuelTank'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import LogBookSignOff_LogBookEntrySectionModel from '@/app/offline/models/logBookSignOff_LogBookEntrySection'
import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { tabClasses } from '../daily-checks/checks'
import {
    getDriveShaftsChecksFields,
    getEngrElectronicsFields,
    getEngrGeneratorFields,
    getEngrMechanicalFields,
    getEngrTowlineWinchFields,
    getOtherEngineFieldFields,
    getOtherEngineRoomVisualInspectionFields,
    getOtherFuelSystemsFields,
    getOtherMainEngineCheckFields,
    getOtherPropulsionCheckFields,
    getPostElectricalFields,
    getPostEngineFields,
    getPostEngineStrainersFields,
    getPostSteeringFields,
    getPreElectricalFields,
    getPreElectricalVisualFields,
    getPreEngineFields,
    getPreEngineMountFields,
    getPreEngineOilFields,
    getPreFields,
} from '@/app/lib/dailyCheckFields'
import { useMediaQuery } from '@reactuses/core'
import CrewChecker from './crew-checker'

export default function DailyEngineChecks({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    setComments,
    setTab,
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    setComments: any
    setTab: any
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: boolean
}) {
    const router = useRouter()
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const [saving, setSaving] = useState(false)
    const [loaded, setLoaded] = useState(true)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    const [sectionComment, setSectionComment] = useState<any>('')
    const [sectionEngineComment, setSectionEngineComment] = useState<any>('')
    const [sectionFuelComment, setSectionFuelComment] = useState<any>('')
    const [vessel, setVessel] = useState<any>(false)
    const [prevReport, setPrevReport] = useState<any>(null)
    const [prevSignOff, setPrevSignOff] = useState<any>(null)
    const [fuelTankList, setFuelTankList] = useState<any>(null)
    const [fuelLevel, setFuelLevel] = useState<any>(false)
    const [prevFuelLevel, setPrevFuelLevel] = useState<any>(false)
    const [currentReport, setCurrentReport] = useState<any>(null)
    const [engineList, setEngineList] = useState<any>(null)
    const [signOff, setSignOff] = useState<any>(null)
    const [entryLastCreated, setEntryLastCreated] = useState<any>(false)
    const [tab, changeTab] = useState<any>('pre-startup')
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')
    const [fuelLogs, setFuelLogs] = useState<any>([])
    const [additionalComment, setAdditionalComment] = useState<Boolean>(false)
    const vesselModel = new VesselModel()
    const engineModel = new EngineModel()
    const fuelTankModel = new FuelTankModel()
    const logbookModel = new LogBookEntryModel()
    const commentModel = new SectionMemberCommentModel()
    const signOffModel = new LogBookSignOff_LogBookEntrySectionModel()
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()
    const isWide = useMediaQuery('(min-width: 640px)')
    const [preCrewResponsible, setPreCrewResponsible] = useState<any>(
        vesselDailyCheck?.preCrewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )
    const [preCheckTime, setPreCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.preCheckTime ?? new Date()),
    )
    const [postCrewResponsible, setPostCrewResponsible] = useState<any>(
        vesselDailyCheck?.postCrewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )
    const [postCheckTime, setPostCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.postCheckTime ?? new Date()),
    )
    const [otherEngineCrewResponsible, setOtherEngineCrewResponsible] =
        useState<any>(
            vesselDailyCheck?.otherEngineCrewResponsible?.nodes?.map(
                (member: any) => ({
                    label: member.firstName + ' ' + member.surname,
                    value: member.id,
                }),
            ),
        )
    const [otherEngineCheckTime, setOtherEngineCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.otherEngineCheckTime ?? new Date()),
    )
    const [engrCrewResponsible, setEngrCrewResponsible] = useState<any>(
        vesselDailyCheck?.engrCrewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )
    const [engrCheckTime, setEngrCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.engrCheckTime ?? new Date()),
    )
    const handleSetLogbook = (logbook: any) => {
        setFuelLogs(logbook.fuelLog.nodes)
        const sectionTypes = Array.from(
            new Set(
                logbook.logBookEntrySections.nodes.map(
                    (sec: any) => sec.className,
                ),
            ),
        ).map((type) => ({
            className: type,
            ids: logbook.logBookEntrySections.nodes
                .filter((sec: any) => sec.className === type)
                .map((sec: any) => sec.id),
        }))
        sectionTypes.forEach(async (section: any) => {
            if (
                section.className === 'SeaLogs\\TripReport_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await tripReportModel.getByIds(section.ids)
                    setCurrentReport(data)
                } else {
                    getSectionTripReport_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
            if (
                section.className ===
                'SeaLogs\\LogBookSignOff_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await signOffModel.getByIds(section.ids)
                    setSignOff(data)
                } else {
                    getLogBookSignOff_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
        })
    }

    const [getLogBookSignOff_LogBookEntrySection] = useLazyQuery(
        LogBookSignOff_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readLogBookSignOff_LogBookEntrySections.nodes
                setSignOff(data)
            },
            onError: (error: any) => {
                console.error('LogBookSignOff_LogBookEntrySection error', error)
            },
        },
    )

    const [getSectionTripReport_LogBookEntrySection] = useLazyQuery(
        TripReport_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTripReport_LogBookEntrySections.nodes
                setCurrentReport(data)
            },
            onError: (error: any) => {
                console.error('TripReport_LogBookEntrySection error', error)
            },
        },
    )

    const loadLogBookEntry = async (id: any) => {
        if (offline) {
            const data = await logbookModel.getById(id)
            if (data) {
                handleSetLogbook(data)
            }
        } else {
            await queryCurrentLogBookEntry({
                variables: {
                    logbookEntryId: +id,
                },
            })
        }
    }

    const [queryCurrentLogBookEntry] = useLazyQuery(GET_LOGBOOK_ENTRY_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneLogBookEntry
            if (data) {
                handleSetLogbook(data)
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookEntry error', error)
        },
    })

    const handleSetVesselInfo = async (vesselInfo: any) => {
        const prevLockedEntry = vesselInfo?.logBookEntries.nodes.find(
            (entry: any) => entry.state === 'Locked',
        )

        if (prevLockedEntry) {
            if (offline) {
                const data = await logbookModel.getById(prevLockedEntry.id)
                if (data) {
                    handleSetPrevLogbook(data)
                }
            } else {
                queryLogBookEntry({
                    variables: {
                        logbookEntryId: +prevLockedEntry.id,
                    },
                })
            }
        }
        if (vesselInfo?.logBookEntries?.nodes[0].id) {
            loadLogBookEntry(vesselInfo?.logBookEntries?.nodes[0].id)
        }
        const fuelTankIds = vesselInfo?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'FuelTank',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        fuelTankIds?.length > 0 && getFuelTanks(fuelTankIds)
        const engineIds =
            vesselInfo?.parentComponent_Components?.nodes
                .filter(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'Engine',
                )
                .map((item: any) => {
                    return item.basicComponent.id
                }) ?? []
        engineIds.length > 0 && getEngines(engineIds)
        setVessel(vesselInfo)
    }

    const getEngines = async (engineIds: any) => {
        if (offline) {
            let engines = await engineModel.getByIds(engineIds)
            engines = engines.map((engine: any) => {
                const filteredNodes = engine.engineStartStops.nodes.filter(
                    (node: any) =>
                        node.logBookEntrySection.logBookEntryID ===
                        `${logentryID}`,
                )
                return {
                    ...engine,
                    engineStartStops: filteredNodes,
                }
            })
            setEngineList(engines)
        } else {
            await queryGetEngines({
                variables: {
                    id: engineIds,
                    filter: {
                        logBookEntrySection: {
                            logBookEntryID: { eq: +logentryID },
                        },
                    },
                },
            })
        }
    }

    const [queryGetEngines] = useLazyQuery(GET_ENGINES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readEngines.nodes
            setEngineList(data)
        },
        onError: (error: any) => {
            console.error('getEngines error', error)
        },
    })

    const getFuelTanks = async (fuelTankIds: any) => {
        if (offline) {
            const data = await fuelTankModel.getByIds(fuelTankIds)
            setFuelTankList(data)
        } else {
            await queryGetFuelTanks({
                variables: {
                    id: fuelTankIds,
                },
            })
        }
    }

    const [queryGetFuelTanks] = useLazyQuery(GET_FUELTANKS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelTanks.nodes
            setFuelTankList(data)
        },
        onError: (error: any) => {
            console.error('getFuelTanks error', error)
        },
    })

    const getFuelLogs = async (fuelLogIds: any) => {
        if (offline) {
            const data = await fuelTankModel.getByIds(fuelLogIds)
            setFuelTankList(data)
        } else {
            await queryGetFuelLogs({
                variables: {
                    id: fuelLogIds,
                },
            })
        }
    }

    const [queryGetFuelLogs] = useLazyQuery(GET_FUELLOGS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelLogs.nodes
            setFuelLogs(data)
        },
        onError: (error: any) => {
            console.error('getFuelLogs error', error)
        },
    })

    const handleSetPrevLogbook = (logbook: any) => {
        const sectionTypes = Array.from(
            new Set(
                logbook.logBookEntrySections.nodes.map(
                    (sec: any) => sec.className,
                ),
            ),
        ).map((type) => ({
            className: type,
            ids: logbook.logBookEntrySections.nodes
                .filter((sec: any) => sec.className === type)
                .map((sec: any) => sec.id),
        }))
        sectionTypes.forEach(async (section: any) => {
            if (
                section.className === 'SeaLogs\\TripReport_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await tripReportModel.getByIds(section.ids)
                    setPrevReport(data)
                } else {
                    getSectionPrevTripReport_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
            if (
                section.className ===
                'SeaLogs\\LogBookSignOff_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await signOffModel.getByIds(section.ids)
                    setPrevSignOff(data)
                } else {
                    getLogBookPrevSignOff_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
        })
    }

    const [getSectionPrevTripReport_LogBookEntrySection] = useLazyQuery(
        TripReport_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTripReport_LogBookEntrySections.nodes
                setPrevReport(data)
            },
            onError: (error: any) => {
                console.error('TripReport_LogBookEntrySection error', error)
            },
        },
    )

    const [getLogBookPrevSignOff_LogBookEntrySection] = useLazyQuery(
        LogBookSignOff_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readLogBookSignOff_LogBookEntrySections.nodes
                setPrevSignOff(data)
            },
            onError: (error: any) => {
                console.error('LogBookSignOff_LogBookEntrySection error', error)
            },
        },
    )

    const [queryLogBookEntry] = useLazyQuery(GET_LOGBOOK_ENTRY_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneLogBookEntry
            if (data) {
                handleSetPrevLogbook(data)
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookEntry error', error)
        },
    })

    if (!offline) {
        getVesselByID(+vesselID, handleSetVesselInfo)
    }

    // const loadVessel = async () => {
    //     const vessel = await vesselModel.getById(vesselID)
    //     handleSetVesselInfo(vessel)
    // }
    // useEffect(() => {
    //     loadVessel()
    // }, [])
    useEffect(() => {
        if (currentReport) {
            var fuelLevel = 0
            var created = 0
            currentReport.forEach((report: any) => {
                report.tripEvents.nodes?.forEach((event: any) => {
                    if (event.eventCategory === 'PassengerDropFacility') {
                        fuelLevel =
                            +event.eventType_PassengerDropFacility.fuelLevel > 0
                                ? event.eventType_PassengerDropFacility
                                      .fuelLevel
                                : fuelLevel
                        created = dayjs(event.created).isAfter(
                            dayjs(entryLastCreated),
                        )
                            ? event.created
                            : created
                    }
                    if (event.eventCategory === 'Tasking') {
                        fuelLevel =
                            +event.eventType_Tasking.fuelLevel > 0
                                ? event.eventType_Tasking.fuelLevel
                                : fuelLevel
                        created = dayjs(event.created).isAfter(
                            dayjs(entryLastCreated),
                        )
                            ? event.created
                            : created
                    }
                    if (event.eventCategory === 'PassengerDropFacility') {
                        fuelLevel =
                            +event.eventType_PassengerDropFacility.fuelLevel > 0
                                ? event.eventType_PassengerDropFacility
                                      .fuelLevel
                                : fuelLevel
                        created = dayjs(event.created).isAfter(
                            dayjs(entryLastCreated),
                        )
                            ? event.created
                            : created
                    }
                })
            })
            setEntryLastCreated(created)
            setFuelLevel(fuelLevel)
        }
    }, [currentReport])

    useEffect(() => {
        if (prevSignOff) {
            var fuelLevel = 0
            {
                prevSignOff[0].fuelStart > 0
                    ? setPrevFuelLevel(prevSignOff[0].fuelStart)
                    : (prevReport?.forEach((report: any) => {
                          report.tripEvents.nodes?.forEach((event: any) => {
                              if (
                                  event.eventCategory ===
                                  'PassengerDropFacility'
                              ) {
                                  fuelLevel =
                                      event.eventType_PassengerDropFacility
                                          .fuelLevel > 0
                                          ? event
                                                .eventType_PassengerDropFacility
                                                .fuelLevel
                                          : fuelLevel
                              }
                              if (event.eventCategory === 'Tasking') {
                                  fuelLevel =
                                      event.eventType_Tasking.fuelLevel > 0
                                          ? event.eventType_Tasking.fuelLevel
                                          : fuelLevel
                              }
                              if (
                                  event.eventCategory ===
                                  'PassengerDropFacility'
                              ) {
                                  fuelLevel =
                                      event.eventType_PassengerDropFacility
                                          .fuelLevel > 0
                                          ? event
                                                .eventType_PassengerDropFacility
                                                .fuelLevel
                                          : fuelLevel
                              }
                          })
                      }),
                      setPrevFuelLevel(fuelLevel))
            }
        }
    }, [prevSignOff])

    const handleEngineChecks = async (check: Boolean, value: any) => {
        if (+vesselDailyCheck?.id > 0) {
            const data = {
                id: vesselDailyCheck.id,
                [value]: check ? 'Ok' : 'Not_Ok',
                logBookEntryID: logentryID,
            }
            if (offline) {
                const response = await dailyCheckModel.save(data)
                setVesselDailyCheck([response])
                // setSaving(true)
                setSaving(false)
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        [value]: check ? 'Ok' : 'Not_Ok',
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: data,
                    },
                })
            }
        }
    }

    // Previously propulsion

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onCompleted: (response) => {
                console.log('Engine check completed')
            },
            onError: (error) => {
                console.error('Error completing engine check', error)
            },
        },
    )

    const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
        VesselDailyCheck_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readVesselDailyCheck_LogBookEntrySections.nodes
                setVesselDailyCheck(data)
                setSaving(true)
                let notificationFields: any = []
                if (displayTab('Pre-startup checks', logBookConfig)) {
                    notificationFields = [
                        ...preEngineFields,
                        ...preEngineOilFields,
                        ...preEngineMountFields,
                        ...preElectricalFields,
                        ...preElectricalVisualFields,
                        ...preFields,
                    ]
                }
                if (displayTab('Post-startup checks', logBookConfig)) {
                    notificationFields = [
                        ...postEngineFields,
                        ...postEngineStrainersFields,
                        ...postElectricalFields,
                        ...postSteeringFields,
                    ]
                }
                if (displayTab('Other engine checks', logBookConfig)) {
                    notificationFields = [
                        ...otherDriveShaftFields,
                        ...otherEngineFieldFields,
                        ...otherMainEngineCheckFields,
                        ...otherEngineRoomVisualInspectionFields,
                        ...otherFuelSystemsFields,
                        ...otherPropulsionCheckFields,
                    ]
                }
                if (displayTab('Engineering', logBookConfig)) {
                    notificationFields = [
                        ...engrMechanicalFields,
                        ...engrGeneratorFields,
                        ...engrElectronicsFields,
                        ...engrTowlineWinchFields,
                    ]
                }
                if (vesselDailyCheck === data[0]) {
                    toast.dismiss()
                    // toast.custom((t) =>
                    //     getDailyCheckNotification(
                    //         notificationFields,
                    //         logBookConfig,
                    //         vesselDailyCheck,
                    //         'Engine Checks',
                    //         handleSetTab,
                    //     ),
                    // )
                }
            },
            onError: (error: any) => {
                console.error(
                    'VesselDailyCheck_LogBookEntrySection error',
                    error,
                )
            },
        },
    )

    const handleSetTab = (tab: any) => {
        toast.remove()
        setTab(tab)
    }

    useEffect(() => {
        let notificationFields: any = []
        if (displayTab('Pre-startup checks', logBookConfig)) {
            notificationFields = [
                ...preEngineFields,
                ...preEngineOilFields,
                ...preEngineMountFields,
                ...preElectricalFields,
                ...preElectricalVisualFields,
                ...preFields,
            ]
        }
        if (displayTab('Post-startup checks', logBookConfig)) {
            notificationFields = [
                ...postEngineFields,
                ...postEngineStrainersFields,
                ...postElectricalFields,
                ...postSteeringFields,
            ]
        }
        if (displayTab('Other engine checks', logBookConfig)) {
            notificationFields = [
                ...otherDriveShaftFields,
                ...otherEngineFieldFields,
                ...otherMainEngineCheckFields,
                ...otherEngineRoomVisualInspectionFields,
                ...otherFuelSystemsFields,
                ...otherPropulsionCheckFields,
            ]
        }
        if (displayTab('Engineering', logBookConfig)) {
            notificationFields = [
                ...engrMechanicalFields,
                ...engrGeneratorFields,
                ...engrElectronicsFields,
                ...engrTowlineWinchFields,
            ]
        }
        if (saving) {
            toast.dismiss()
            // toast.custom((t) =>
            //     getDailyCheckNotification(
            //         notificationFields,
            //         logBookConfig,
            //         vesselDailyCheck,
            //         'Engine Checks',
            //         handleSetTab,
            //     ),
            // )
        }
    }, [vesselDailyCheck])

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            comments?.length > 0
                ? comments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const showCommentPopup = (comment: string, field: any) => {
        setCurrentComment(comment ? comment : '')
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const handleSaveComment = async () => {
        setOpenCommentAlert(false)
        const comment = (document.getElementById('comment') as HTMLInputElement)
            ?.value
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: currentField[0]?.fieldName,
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'FieldComment',
        }
        const offlineID = currentComment?.id
            ? currentComment?.id
            : generateUniqueId()
        if (currentComment) {
            if (offline) {
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: (response) => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: (response) => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        GET_SECTION_MEMBER_COMMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const preEngineFields = getPreEngineFields(logBookConfig, vesselDailyCheck)
    const preEngineOilFields = getPreEngineOilFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const preEngineMountFields = getPreEngineMountFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const preElectricalFields = getPreElectricalFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const preElectricalVisualFields = getPreElectricalVisualFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const preFields = getPreFields(logBookConfig, vesselDailyCheck)
    const postEngineFields = getPostEngineFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const postEngineStrainersFields = getPostEngineStrainersFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const postElectricalFields = getPostElectricalFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const postSteeringFields = getPostSteeringFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const otherDriveShaftFields = getDriveShaftsChecksFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const otherEngineFieldFields = getOtherEngineFieldFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const otherMainEngineCheckFields = getOtherMainEngineCheckFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const otherEngineRoomVisualInspectionFields =
        getOtherEngineRoomVisualInspectionFields(
            logBookConfig,
            vesselDailyCheck,
        )
    const otherFuelSystemsFields = getOtherFuelSystemsFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const otherPropulsionCheckFields = getOtherPropulsionCheckFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const engrMechanicalFields = getEngrMechanicalFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const engrGeneratorFields = getEngrGeneratorFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const engrElectronicsFields = getEngrElectronicsFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const engrTowlineWinchFields = getEngrTowlineWinchFields(
        logBookConfig,
        vesselDailyCheck,
    )
    const handleSave = async () => {
        if (offline) {
            const data = await dailyCheckModel.getByIds([vesselDailyCheck.id])
            setVesselDailyCheck(data)
            setSaving(true)
            let notificationFields: any = []
            if (displayTab('Pre-startup checks', logBookConfig)) {
                notificationFields = [
                    ...preEngineFields,
                    ...preEngineOilFields,
                    ...preEngineMountFields,
                    ...preElectricalFields,
                    ...preElectricalVisualFields,
                    ...preFields,
                ]
            }
            if (displayTab('Post-startup checks', logBookConfig)) {
                notificationFields = [
                    ...postEngineFields,
                    ...postEngineStrainersFields,
                    ...postElectricalFields,
                    ...postSteeringFields,
                ]
            }
            if (displayTab('Other engine checks', logBookConfig)) {
                notificationFields = [
                    ...otherDriveShaftFields,
                    ...otherEngineFieldFields,
                    ...otherMainEngineCheckFields,
                    ...otherEngineRoomVisualInspectionFields,
                    ...otherFuelSystemsFields,
                    ...otherPropulsionCheckFields,
                ]
            }
            if (displayTab('Engineering', logBookConfig)) {
                notificationFields = [
                    ...engrMechanicalFields,
                    ...engrGeneratorFields,
                    ...engrElectronicsFields,
                    ...engrTowlineWinchFields,
                ]
            }
            if (vesselDailyCheck === data[0]) {
                toast.dismiss()
                // toast.custom((t) =>
                //     getDailyCheckNotification(
                //         notificationFields,
                //         logBookConfig,
                //         vesselDailyCheck,
                //         'Engine Checks',
                //         handleSetTab,
                //     ),
                // )
            }
        } else {
            getSectionVesselDailyCheck_LogBookEntrySection({
                variables: {
                    id: [vesselDailyCheck.id],
                },
            })
        }
        toast.loading('Saving Engine Checks...')
        const variablesEngine = {
            id: getComment('DailyCheckEngine', 'Section')
                ? getComment('DailyCheckEngine', 'Section').id
                : 0,
            fieldName: 'DailyCheckEngine',
            comment: sectionEngineComment
                ? sectionEngineComment
                : getComment('DailyCheckEngine', 'Section').comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }
        const variablesFuel = {
            id: getComment('DailyCheckFuel', 'Section')
                ? getComment('DailyCheckFuel', 'Section').id
                : 0,
            fieldName: 'DailyCheckFuel',
            comment: sectionFuelComment
                ? sectionFuelComment
                : getComment('DailyCheckFuel', 'Section').comment,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }

        const fuelStart = (
            document.getElementById('fuel-start') as HTMLInputElement
        )?.value
        if (fuelStart !== fuelLevel ? fuelLevel : prevFuelLevel) {
            Promise.all(
                fuelTankList.map(async (fuelTank: any, index: number) => {
                    const fuelData = {
                        id: fuelTank.id,
                        currentLevel:
                            +fuelStart - fuelTank.capacity * index >
                            fuelTank.capacity
                                ? fuelTank.capacity
                                : +fuelStart - fuelTank.capacity * index > -1
                                  ? +fuelStart - fuelTank.capacity * index
                                  : 0,
                    }
                    if (offline) {
                        await fuelTankModel.save(fuelData)
                        setLoaded(true)
                    } else {
                        updateFuelTank({
                            variables: {
                                input: fuelData,
                            },
                        })
                    }
                }),
            )
        }
        if (getComment('DailyCheckEngine', 'Section')?.id) {
            if (offline) {
                await commentModel.save(variablesEngine)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variablesEngine },
                })
            }
        } else {
            if (offline) {
                const id = getComment('DailyCheckEngine', 'Section')
                    ? getComment('DailyCheckEngine', 'Section').id
                    : generateUniqueId()
                await commentModel.save({ ...variablesEngine, id: id })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variablesEngine },
                })
            }
        }
        if (getComment('DailyCheckFuel', 'Section')?.id) {
            if (offline) {
                await commentModel.save(variablesFuel)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variablesFuel },
                })
            }
        } else {
            if (offline) {
                const id = getComment('DailyCheckFuel', 'Section')
                    ? getComment('DailyCheckFuel', 'Section').id
                    : generateUniqueId()
                await commentModel.save({
                    ...variablesFuel,
                    id: id,
                })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variablesFuel },
                })
            }
        }
    }

    const [updateFuelTank] = useMutation(UpdateFuelTank, {
        onCompleted: (response) => {
            setLoaded(true)
        },
        onError: (error) => {
            console.error('Error updating fuel tank', error)
        },
    })

    const [updateEngineHours] = useMutation(UPDATE_ENGINE, {
        onCompleted: (response) => {},
        onError: (error) => {
            console.error('Error updating engine hours', error)
        },
    })

    const maxCapacity = fuelTankList?.reduce(
        (total: number, tank: any) => total + tank.capacity,
        0,
    )

    const handleGroupYesChange = async (
        groupField: any,
        groupFieldParent: any,
    ) => {
        await handleEngineChecks(
            true,
            [
                ...preEngineFields,
                ...preEngineOilFields,
                ...preEngineMountFields,
                ...preElectricalFields,
                ...preElectricalVisualFields,
                ...preFields,
                ...postEngineFields,
                ...postEngineStrainersFields,
                ...postElectricalFields,
                ...postSteeringFields,
                ...otherDriveShaftFields,
                ...otherEngineFieldFields,
                ...otherMainEngineCheckFields,
                ...otherEngineRoomVisualInspectionFields,
                ...otherFuelSystemsFields,
                ...otherPropulsionCheckFields,
                ...engrMechanicalFields,
                ...engrGeneratorFields,
                ...engrElectronicsFields,
                ...engrTowlineWinchFields,
            ].find((field: any) => field.name === groupFieldParent.name)?.value,
        )
        groupField.map((field: any) => handleEngineChecks(true, field.value))
    }

    const handleGroupNoChange = async (
        groupField: any,
        groupFieldParent: any,
    ) => {
        await handleEngineChecks(
            false,
            [
                ...preEngineFields,
                ...preEngineOilFields,
                ...preEngineMountFields,
                ...preElectricalFields,
                ...preElectricalVisualFields,
                ...preFields,
                ...postEngineFields,
                ...postEngineStrainersFields,
                ...postElectricalFields,
                ...postSteeringFields,
                ...otherDriveShaftFields,
                ...otherEngineFieldFields,
                ...otherMainEngineCheckFields,
                ...otherEngineRoomVisualInspectionFields,
                ...otherFuelSystemsFields,
                ...otherPropulsionCheckFields,
                ...engrMechanicalFields,
                ...engrGeneratorFields,
                ...engrElectronicsFields,
                ...engrTowlineWinchFields,
            ].find((field: any) => field.name === groupFieldParent.name)?.value,
        )
        groupField.map((field: any) => handleEngineChecks(false, field.value))
    }

    const calculatedFuelLevel = fuelTankList?.find((tank: any) =>
        dayjs(tank.lastEdited).isAfter(dayjs(entryLastCreated)),
    )
        ? fuelTankList.reduce(
              (total: number, tank: any) => total + tank.currentLevel,
              0,
          )
        : fuelLevel
          ? fuelLevel
          : prevFuelLevel

    const displayTab = (tab: string, logBookConfig: any) => {
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'VesselDailyCheck_LogBookComponent',
            )
        return dailyChecks[0]?.subFields?.split('||').includes(tab)
            ? true
            : false
    }

    const handlePreCheckTime = async (date: any) => {
        setPreCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                preCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log('1082 offline')
                /* const newVesselDailyCheck =
                        await dailyCheckModel.save(variables)
                    // setSaving(true)
                    setSaving(false)
                    setVesselDailyCheck([newVesselDailyCheck])
                    const sections = logbook.logBookEntrySections.nodes
                    const section = {
                        className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                        id: `${vesselDailyCheck.id}`,
                        logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                        __typename: 'VesselDailyCheck_LogBookEntrySection',
                    }
                    if (
                        !sections.some(
                            (s: any) =>
                                JSON.stringify(s) === JSON.stringify(section),
                        )
                    ) {
                        sections.push(section)
                    }
                    const lb = {
                        ...logbook,
                        logBookEntrySections: { nodes: sections },
                    }
                    await logBookModel.save(lb)
                    getOfflineLogBookEntry() */
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        preCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }
    const handlePostCheckTime = async (date: any) => {
        setPostCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                postCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log('1082 offline')
                /* const newVesselDailyCheck =
                        await dailyCheckModel.save(variables)
                    // setSaving(true)
                    setSaving(false)
                    setVesselDailyCheck([newVesselDailyCheck])
                    const sections = logbook.logBookEntrySections.nodes
                    const section = {
                        className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                        id: `${vesselDailyCheck.id}`,
                        logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                        __typename: 'VesselDailyCheck_LogBookEntrySection',
                    }
                    if (
                        !sections.some(
                            (s: any) =>
                                JSON.stringify(s) === JSON.stringify(section),
                        )
                    ) {
                        sections.push(section)
                    }
                    const lb = {
                        ...logbook,
                        logBookEntrySections: { nodes: sections },
                    }
                    await logBookModel.save(lb)
                    getOfflineLogBookEntry() */
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        postCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }
    const handleOtherEngineCheckTime = async (date: any) => {
        setOtherEngineCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                otherEngineCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log('1082 offline')
                /* const newVesselDailyCheck =
                        await dailyCheckModel.save(variables)
                    // setSaving(true)
                    setSaving(false)
                    setVesselDailyCheck([newVesselDailyCheck])
                    const sections = logbook.logBookEntrySections.nodes
                    const section = {
                        className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                        id: `${vesselDailyCheck.id}`,
                        logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                        __typename: 'VesselDailyCheck_LogBookEntrySection',
                    }
                    if (
                        !sections.some(
                            (s: any) =>
                                JSON.stringify(s) === JSON.stringify(section),
                        )
                    ) {
                        sections.push(section)
                    }
                    const lb = {
                        ...logbook,
                        logBookEntrySections: { nodes: sections },
                    }
                    await logBookModel.save(lb)
                    getOfflineLogBookEntry() */
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        otherEngineCheckTime: dayjs(date).format(
                            'YYYY-MM-DD HH:mm',
                        ),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }
    const handleEngrCheckTime = async (date: any) => {
        setEngrCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                engrCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log('1082 offline')
                /* const newVesselDailyCheck =
                        await dailyCheckModel.save(variables)
                    // setSaving(true)
                    setSaving(false)
                    setVesselDailyCheck([newVesselDailyCheck])
                    const sections = logbook.logBookEntrySections.nodes
                    const section = {
                        className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                        id: `${vesselDailyCheck.id}`,
                        logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                        __typename: 'VesselDailyCheck_LogBookEntrySection',
                    }
                    if (
                        !sections.some(
                            (s: any) =>
                                JSON.stringify(s) === JSON.stringify(section),
                        )
                    ) {
                        sections.push(section)
                    }
                    const lb = {
                        ...logbook,
                        logBookEntrySections: { nodes: sections },
                    }
                    await logBookModel.save(lb)
                    getOfflineLogBookEntry() */
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        engrCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }
    const handlePreCrewResponsible = async (crews: any) => {
        setPreCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                preCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log('1127 offline')
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        preCrewResponsible: crewResponsibleIDs.join(','),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }
    const handlePostCrewResponsible = async (crews: any) => {
        setPostCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                postCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log('1127 offline')
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        postCrewResponsible: crewResponsibleIDs.join(','),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }
    const handleOtherEngineCrewResponsible = async (crews: any) => {
        setOtherEngineCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                otherEngineCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log('1127 offline')
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        otherEngineCrewResponsible: crewResponsibleIDs.join(','),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const handleEngrCrewResponsible = async (crews: any) => {
        setEngrCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                engrCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log('1127 offline')
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        engrCrewResponsible: crewResponsibleIDs.join(','),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [])

    return (
        <div className="pb-16">
            <ul className="flex-row text-2xs uppercase gap-1 font-inter text-center mb-3 hidden lg:flex">
                {displayTab('Pre-startup checks', logBookConfig) && (
                    <li className={`${classes.label} !w-auto`}>
                        <Button
                            className={`${tab === 'pre-startup' ? tabClasses.active : tabClasses.inactive} uppercase whitespace-nowrap`}
                            onPress={() => {
                                tab === 'pre-startup'
                                    ? changeTab('')
                                    : changeTab('pre-startup')
                            }}>
                            Pre-startup checks
                        </Button>
                    </li>
                )}
                {displayTab('Post-startup checks', logBookConfig) && (
                    <li className={`${classes.label} !w-auto `}>
                        <Button
                            className={`${tab === 'post-startup' ? tabClasses.active : tabClasses.inactive} uppercase whitespace-nowrap`}
                            onPress={() => {
                                tab === 'post-startup'
                                    ? changeTab('')
                                    : changeTab('post-startup')
                            }}>
                            Post-startup checks
                        </Button>
                    </li>
                )}
                {displayTab('Other engine checks', logBookConfig) && (
                    <li className={`${classes.label} !w-auto `}>
                        <Button
                            className={`${tab === 'other-engine' ? tabClasses.active : tabClasses.inactive} uppercase whitespace-nowrap`}
                            onPress={() => {
                                tab === 'other-engine'
                                    ? changeTab('')
                                    : changeTab('other-engine')
                            }}>
                            Other engine checks
                        </Button>
                    </li>
                )}
                {displayTab('Engineering', logBookConfig) && (
                    <li className={`${classes.label} !w-auto `}>
                        <Button
                            className={`${tab === 'engineering' ? tabClasses.active : tabClasses.inactive} uppercase whitespace-nowrap`}
                            onPress={() => {
                                tab === 'engineering'
                                    ? changeTab('')
                                    : changeTab('engineering')
                            }}>
                            Engineering
                        </Button>
                    </li>
                )}
            </ul>
            <Button
                className={`${tab === 'pre-startup' ? tabClasses.active : tabClasses.inactive} uppercase whitespace-nowrap block lg:hidden`}
                onPress={() => {
                    tab === 'pre-startup'
                        ? changeTab('')
                        : changeTab('pre-startup')
                }}>
                Pre-startup checks
            </Button>
            <div className={`${tab === 'pre-startup' ? '' : 'hidden'}`}>
                {displayField('PreFuelLevelStart', logBookConfig) && (
                    <>
                        <div className="flex flex-row">
                            <div className="mt-6 text-sm font-semibold uppercase">
                                Fuel levels
                            </div>
                            <div
                                className={`flex flex-wrap ${locked || !edit_logBookEntry ? 'pointer-events-none opacity-60' : ''}`}>
                                {fuelTankList && loaded && (
                                    <VesselFuelStatus
                                        offline={offline}
                                        fuelTankList={fuelTankList}
                                        updateFuelLogList={getFuelLogs}
                                        updateFuelTankList={getFuelTanks}
                                        fuelLogs={fuelLogs}
                                    />
                                )}
                            </div>
                        </div>
                        <p className="text-xs font-inter max-w-[40rem] leading-loose">
                            Click the gauge to change the fuel level.
                        </p>
                        <div className="my-4">
                            <textarea
                                id={`section_comment`}
                                placeholder="Comment if the fuel start value differs from the fuel end value in the previous logbook entry."
                                readOnly={locked || !edit_logBookEntry}
                                rows={4}
                                className={`${classes.textarea} mt-4`}
                                onChange={(e) =>
                                    setSectionFuelComment(e.target.value)
                                }
                                onBlur={(e) => {
                                    getComment('DailyCheckFuel', 'Section')
                                        ?.id > 0
                                        ? updateSectionMemberComment({
                                              variables: {
                                                  input: {
                                                      id: getComment(
                                                          'DailyCheckFuel',
                                                          'Section',
                                                      ).id,
                                                      comment: e.target.value,
                                                  },
                                              },
                                          })
                                        : createSectionMemberComment({
                                              variables: {
                                                  input: {
                                                      fieldName:
                                                          'DailyCheckFuel',
                                                      comment: e.target.value,
                                                      logBookEntrySectionID:
                                                          vesselDailyCheck.id,
                                                      commentType: 'Section',
                                                  },
                                              },
                                          })
                                }}
                                defaultValue={
                                    getComment('DailyCheckFuel', 'Section')
                                        ?.comment
                                }></textarea>
                        </div>
                    </>
                )}
                {engineList?.length > 0 && (
                    <>
                        <div className="mt-6 text-sm font-semibold uppercase text-left">
                            Engine hours
                        </div>
                        <div className="">
                            {engineList?.map((engine: any) => (
                                <div
                                    className="my-4 flex items-center"
                                    key={engine.id}>
                                    <label className={classes.label}>
                                        {engine.title}
                                    </label>
                                    <input
                                        id={`engine-hours-${engine.id}`}
                                        type="number"
                                        defaultValue={engine.currentHours}
                                        name="start"
                                        placeholder="Engine Hours"
                                        className={classes.input}
                                        disabled={locked || !edit_logBookEntry}
                                        onBlur={(e: any) => {
                                            const engineData = {
                                                id: engine.id,
                                                currentHours: +e.target.value,
                                            }
                                            if (offline) {
                                                engineModel.save(engineData)
                                            } else {                                                
                                                updateEngineHours({
                                                    variables: {
                                                        input: engineData,
                                                    },
                                                })
                                            }
                                        }}
                                    />
                                </div>
                            ))}
                            <div className="my-4">
                                <textarea
                                    placeholder="Comment IF Engine hours are different to engine hours at end of previous logbook entry"
                                    readOnly={locked || !edit_logBookEntry}
                                    rows={4}
                                    className={`${classes.textarea} mt-4`}
                                    onChange={(e) =>
                                        setSectionEngineComment(e.target.value)
                                    }
                                    onBlur={(e) =>
                                        getComment(
                                            'DailyCheckEngine',
                                            'Section',
                                        )?.id > 0
                                            ? updateSectionMemberComment({
                                                  variables: {
                                                      input: {
                                                          id: getComment(
                                                              'DailyCheckEngine',
                                                              'Section',
                                                          ).id,
                                                          comment:
                                                              e.target.value,
                                                      },
                                                  },
                                              })
                                            : createSectionMemberComment({
                                                  variables: {
                                                      input: {
                                                          fieldName:
                                                              'DailyCheckEngine',
                                                          comment:
                                                              e.target.value,
                                                          logBookEntrySectionID:
                                                              vesselDailyCheck.id,
                                                          commentType:
                                                              'Section',
                                                      },
                                                  },
                                              })
                                    }
                                    defaultValue={
                                        getComment(
                                            'DailyCheckEngine',
                                            'Section',
                                        )?.comment
                                    }>
                                    {/* {getComment('Engine', 'Section')?.comment} */}
                                </textarea>
                            </div>
                        </div>
                    </>
                )}
                {logBookConfig && vesselDailyCheck && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 items-start">
                        <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                            {getFilteredFields(
                                preEngineFields,
                                true,
                                logBookConfig,
                            )
                                ?.filter((groupField: any) =>
                                    displayField(
                                        groupField.name,
                                        logBookConfig,
                                    ),
                                )
                                ?.map((groupField: any) => (
                                    <div
                                        key={groupField.name}
                                        className="w-full">
                                        <div className="w-full">
                                            <div className="mt-6 text-sm font-semibold uppercase text-left">
                                                {getFieldLabel(
                                                    groupField.name,
                                                    logBookConfig,
                                                )}
                                            </div>
                                            <div className="w-full">
                                                {groupField?.items
                                                    ?.filter((field: any) =>
                                                        displayField(
                                                            field.name,
                                                            logBookConfig,
                                                        ),
                                                    )
                                                    ?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                setDescriptionPanelContent={
                                                                    setDescriptionPanelContent
                                                                }
                                                                setOpenDescriptionPanel={
                                                                    setOpenDescriptionPanel
                                                                }
                                                                setDescriptionPanelHeading={
                                                                    setDescriptionPanelHeading
                                                                }
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    // field.handleChange(
                                                                    //     false,
                                                                    // )
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    // field.handleChange(
                                                                    //     true,
                                                                    // )
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                {displayDescription(
                                                    groupField.name,
                                                    logBookConfig,
                                                ) && (
                                                    <SeaLogsButton
                                                        icon="alert"
                                                        className="w-6 h-6 sup -mt-2 ml-0.5"
                                                        action={() => {
                                                            setDescriptionPanelContent(
                                                                displayDescription(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                            setOpenDescriptionPanel(
                                                                true,
                                                            )
                                                            setDescriptionPanelHeading(
                                                                groupField.name,
                                                            )
                                                        }}
                                                    />
                                                )}
                                            </div>
                                            {/* <div className="flex flex-row items-center">
                                                <DailyCheckGroupField
                                                    locked={
                                                        locked ||
                                                        !edit_logBookEntry
                                                    }
                                                    groupField={groupField?.items?.filter(
                                                        (field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                    )}
                                                    handleYesChange={() =>
                                                        handleGroupYesChange(
                                                            groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            ),
                                                            groupField,
                                                        )
                                                    }
                                                    handleNoChange={() =>
                                                        handleGroupNoChange(
                                                            groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            ),
                                                            groupField,
                                                        )
                                                    }
                                                    defaultNoChecked={groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.every(
                                                            (field: any) =>
                                                                field.checked ===
                                                                'Not_Ok',
                                                        )}
                                                    defaultYesChecked={groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.every(
                                                            (field: any) =>
                                                                field.checked ===
                                                                'Ok',
                                                        )}
                                                    commentAction={() =>
                                                        showCommentPopup(
                                                            getComment(
                                                                groupField.name,
                                                            ),
                                                            composeField(
                                                                groupField.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                    }
                                                    comment={
                                                        getComment(
                                                            groupField.name,
                                                        )?.comment
                                                    }
                                                />
                                                {groupField?.items?.map(
                                                    (
                                                        field: any,
                                                        index: number,
                                                    ) => (
                                                        <DailyCheckField
                                                            locked={
                                                                locked ||
                                                                !edit_logBookEntry
                                                            }
                                                            className={`lg:!grid-cols-2 hidden`}
                                                            innerWrapperClassName={`lg:!col-span-1`}
                                                            key={index}
                                                            displayField={displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            displayDescription={displayDescription(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            displayLabel={getFieldLabel(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            inputId={
                                                                field.value
                                                            }
                                                            handleNoChange={() =>
                                                                handleEngineChecks(
                                                                    false,
                                                                    field.value,
                                                                )
                                                            }
                                                            defaultNoChecked={
                                                                field.checked ===
                                                                'Not_Ok'
                                                            }
                                                            handleYesChange={() =>
                                                                handleEngineChecks(
                                                                    true,
                                                                    field.value,
                                                                )
                                                            }
                                                            defaultYesChecked={
                                                                field.checked ===
                                                                'Ok'
                                                            }
                                                            commentAction={() =>
                                                                showCommentPopup(
                                                                    getComment(
                                                                        field.name,
                                                                    ),
                                                                    composeField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                            }
                                                            comment={
                                                                getComment(
                                                                    field.name,
                                                                )?.comment
                                                            }
                                                        />
                                                    ),
                                                )}
                                            </div> */}
                                        </div>
                                    </div>
                                ))}
                            <hr className="my-6" />
                            {/* <div className="flex flex-row gap-2">
                                <CustomDailyCheckField
                                    locked={locked || !edit_logBookEntry}
                                    displayField={displayField(
                                        'EngineCheckPropellers',
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        'EngineCheckPropellers',
                                        logBookConfig,
                                    )}
                                    setDescriptionPanelContent={
                                        setDescriptionPanelContent
                                    }
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={
                                        'Check for damage on the props'
                                    }
                                    className={'flex'}
                                    inputId={'engineCheckPropellers'}
                                    handleNoChange={() =>
                                        handleEngineChecks(
                                            false,
                                            'engineCheckPropellers',
                                        )
                                    }
                                    defaultNoChecked={
                                        vesselDailyCheck?.engineCheckPropellers ===
                                        'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleEngineChecks(
                                            true,
                                            'engineCheckPropellers',
                                        )
                                    }
                                    defaultYesChecked={
                                        vesselDailyCheck?.engineCheckPropellers ===
                                        'Ok'
                                    }
                                    commentAction={() =>
                                        showCommentPopup(
                                            getComment('EngineCheckPropellers'),
                                            composeField(
                                                'EngineCheckPropellers',
                                                logBookConfig,
                                            ),
                                        )
                                    }
                                    comment={
                                        getComment('EngineCheckPropellers')
                                            ?.comment
                                    }
                                />
                            </div>
                            <hr className="my-6" /> */}
                            {getFilteredFields(
                                preEngineOilFields,
                                true,
                                logBookConfig,
                            )
                                ?.filter((groupField: any) =>
                                    displayField(
                                        groupField.name,
                                        logBookConfig,
                                    ),
                                )
                                ?.map((groupField: any) => (
                                    <div
                                        key={groupField.name}
                                        className="w-full">
                                        <div className="w-full">
                                            <div className="mt-6 text-sm font-semibold uppercase text-left">
                                                {getFieldLabel(
                                                    groupField.name,
                                                    logBookConfig,
                                                )}
                                            </div>
                                            <div className="">
                                                {groupField?.items
                                                    ?.filter((field: any) =>
                                                        displayField(
                                                            field.name,
                                                            logBookConfig,
                                                        ),
                                                    )
                                                    ?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                setDescriptionPanelContent={
                                                                    setDescriptionPanelContent
                                                                }
                                                                setOpenDescriptionPanel={
                                                                    setOpenDescriptionPanel
                                                                }
                                                                setDescriptionPanelHeading={
                                                                    setDescriptionPanelHeading
                                                                }
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    // field.handleChange(
                                                                    //     false,
                                                                    // )
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    // field.handleChange(
                                                                    //     true,
                                                                    // )
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                {displayDescription(
                                                    groupField.name,
                                                    logBookConfig,
                                                ) && (
                                                    <SeaLogsButton
                                                        icon="alert"
                                                        className="w-6 h-6 sup -mt-2 ml-0.5"
                                                        action={() => {
                                                            setDescriptionPanelContent(
                                                                displayDescription(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                            setOpenDescriptionPanel(
                                                                true,
                                                            )
                                                            setDescriptionPanelHeading(
                                                                groupField.name,
                                                            )
                                                        }}
                                                    />
                                                )}
                                            </div>
                                            {/* <div className="flex flex-row">
                                                <DailyCheckGroupField
                                                    locked={
                                                        locked ||
                                                        !edit_logBookEntry
                                                    }
                                                    groupField={groupField?.items?.filter(
                                                        (field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                    )}
                                                    handleYesChange={() =>
                                                        handleGroupYesChange(
                                                            groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            ),
                                                            groupField,
                                                        )
                                                    }
                                                    handleNoChange={() =>
                                                        handleGroupNoChange(
                                                            groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            ),
                                                            groupField,
                                                        )
                                                    }
                                                    defaultNoChecked={groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.every(
                                                            (field: any) =>
                                                                field.checked ===
                                                                'Not_Ok',
                                                        )}
                                                    defaultYesChecked={groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.every(
                                                            (field: any) =>
                                                                field.checked ===
                                                                'Ok',
                                                        )}
                                                    commentAction={() =>
                                                        showCommentPopup(
                                                            getComment(
                                                                groupField.name,
                                                            ),
                                                            composeField(
                                                                groupField.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                    }
                                                    comment={
                                                        getComment(
                                                            groupField.name,
                                                        )?.comment
                                                    }
                                                />
                                                {groupField?.items?.map(
                                                    (
                                                        field: any,
                                                        index: number,
                                                    ) => (
                                                        <DailyCheckField
                                                            locked={
                                                                locked ||
                                                                !edit_logBookEntry
                                                            }
                                                            className={`lg:!grid-cols-2 hidden`}
                                                            innerWrapperClassName={`lg:!col-span-1`}
                                                            key={index}
                                                            displayField={displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            displayDescription={displayDescription(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            displayLabel={getFieldLabel(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            inputId={
                                                                field.value
                                                            }
                                                            handleNoChange={() =>
                                                                handleEngineChecks(
                                                                    false,
                                                                    field.value,
                                                                )
                                                            }
                                                            defaultNoChecked={
                                                                field.checked ===
                                                                'Not_Ok'
                                                            }
                                                            handleYesChange={() =>
                                                                handleEngineChecks(
                                                                    true,
                                                                    field.value,
                                                                )
                                                            }
                                                            defaultYesChecked={
                                                                field.checked ===
                                                                'Ok'
                                                            }
                                                            commentAction={() =>
                                                                showCommentPopup(
                                                                    getComment(
                                                                        field.name,
                                                                    ),
                                                                    composeField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                            }
                                                            comment={
                                                                getComment(
                                                                    field.name,
                                                                )?.comment
                                                            }
                                                        />
                                                    ),
                                                )}
                                            </div> */}
                                        </div>
                                    </div>
                                ))}
                            <hr className="my-6" />
                            {getFilteredFields(
                                preEngineMountFields,
                                true,
                                logBookConfig,
                            )
                                ?.filter((groupField: any) =>
                                    displayField(
                                        groupField.name,
                                        logBookConfig,
                                    ),
                                )
                                ?.map((groupField: any) => (
                                    <div
                                        key={groupField.name}
                                        className="w-full">
                                        <div className="w-full">
                                            <div className="mt-6 text-sm font-semibold uppercase text-left">
                                                {getFieldLabel(
                                                    groupField.name,
                                                    logBookConfig,
                                                )}
                                            </div>
                                            <div className="">
                                                {groupField?.items
                                                    ?.filter((field: any) =>
                                                        displayField(
                                                            field.name,
                                                            logBookConfig,
                                                        ),
                                                    )
                                                    ?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            // <span
                                                            //     key={`${field.label}-${index}`}
                                                            //     className="text-sm lg:text-base">
                                                            //     {index <
                                                            //     groupField.items
                                                            //         .length -
                                                            //         1
                                                            //         ? field.label +
                                                            //           ' -'
                                                            //         : field.label}
                                                            //     {displayDescription(
                                                            //         field.name,
                                                            //         logBookConfig,
                                                            //     ) && (
                                                            //         <SeaLogsButton
                                                            //             icon="alert"
                                                            //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            //             action={() => {
                                                            //                 setDescriptionPanelContent(
                                                            //                     displayDescription(
                                                            //                         field.name,
                                                            //                         logBookConfig,
                                                            //                     ),
                                                            //                 )
                                                            //                 setOpenDescriptionPanel(
                                                            //                     true,
                                                            //                 )
                                                            //                 setDescriptionPanelHeading(
                                                            //                     field.name,
                                                            //                 )
                                                            //             }}
                                                            //         />
                                                            //     )}{' '}
                                                            // </span>
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                setDescriptionPanelContent={
                                                                    setDescriptionPanelContent
                                                                }
                                                                setOpenDescriptionPanel={
                                                                    setOpenDescriptionPanel
                                                                }
                                                                setDescriptionPanelHeading={
                                                                    setDescriptionPanelHeading
                                                                }
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    // field.handleChange(
                                                                    //     false,
                                                                    // )
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    // field.handleChange(
                                                                    //     true,
                                                                    // )
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                {displayDescription(
                                                    groupField.name,
                                                    logBookConfig,
                                                ) && (
                                                    <SeaLogsButton
                                                        icon="alert"
                                                        className="w-6 h-6 sup -mt-2 ml-0.5"
                                                        action={() => {
                                                            setDescriptionPanelContent(
                                                                displayDescription(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                            setOpenDescriptionPanel(
                                                                true,
                                                            )
                                                            setDescriptionPanelHeading(
                                                                groupField.name,
                                                            )
                                                        }}
                                                    />
                                                )}
                                            </div>
                                            {/* <div className="flex flex-row">
                                                <DailyCheckGroupField
                                                    locked={
                                                        locked ||
                                                        !edit_logBookEntry
                                                    }
                                                    groupField={groupField?.items?.filter(
                                                        (field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                    )}
                                                    handleYesChange={() =>
                                                        handleGroupYesChange(
                                                            groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            ),
                                                            groupField,
                                                        )
                                                    }
                                                    handleNoChange={() =>
                                                        handleGroupNoChange(
                                                            groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            ),
                                                            groupField,
                                                        )
                                                    }
                                                    defaultNoChecked={groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.every(
                                                            (field: any) =>
                                                                field.checked ===
                                                                'Not_Ok',
                                                        )}
                                                    defaultYesChecked={groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.every(
                                                            (field: any) =>
                                                                field.checked ===
                                                                'Ok',
                                                        )}
                                                    commentAction={() =>
                                                        showCommentPopup(
                                                            getComment(
                                                                groupField.name,
                                                            ),
                                                            composeField(
                                                                groupField.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                    }
                                                    comment={
                                                        getComment(
                                                            groupField.name,
                                                        )?.comment
                                                    }
                                                />
                                                {groupField?.items?.map(
                                                    (
                                                        field: any,
                                                        index: number,
                                                    ) => (
                                                        <DailyCheckField
                                                            locked={
                                                                locked ||
                                                                !edit_logBookEntry
                                                            }
                                                            className={`lg:!grid-cols-2 hidden`}
                                                            innerWrapperClassName={`lg:!col-span-1`}
                                                            key={index}
                                                            displayField={displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            displayDescription={displayDescription(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            displayLabel={getFieldLabel(
                                                                field.name,
                                                                logBookConfig,
                                                            )}
                                                            inputId={
                                                                field.value
                                                            }
                                                            handleNoChange={() =>
                                                                handleEngineChecks(
                                                                    false,
                                                                    field.value,
                                                                )
                                                            }
                                                            defaultNoChecked={
                                                                field.checked ===
                                                                'Not_Ok'
                                                            }
                                                            handleYesChange={() =>
                                                                handleEngineChecks(
                                                                    true,
                                                                    field.value,
                                                                )
                                                            }
                                                            defaultYesChecked={
                                                                field.checked ===
                                                                'Ok'
                                                            }
                                                            commentAction={() =>
                                                                showCommentPopup(
                                                                    getComment(
                                                                        field.name,
                                                                    ),
                                                                    composeField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                            }
                                                            comment={
                                                                getComment(
                                                                    field.name,
                                                                )?.comment
                                                            }
                                                        />
                                                    ),
                                                )}
                                            </div> */}
                                        </div>
                                    </div>
                                ))}
                        </div>
                    </div>
                )}
                {getFilteredFields(
                    preElectricalFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 ||
                getFilteredFields(
                    preElectricalVisualFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 ||
                displayField('Generator', logBookConfig) ||
                displayField('ShorePower', logBookConfig) ? (
                    <>
                        <hr className="my-6" />
                        <div className="mt-6 text-sm font-semibold uppercase text-left">
                            Electrical Checks
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    preElectricalFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )
                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="w-full">
                                                <div className="">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                // <span
                                                                //     key={`${field.label}-${index}`}
                                                                //     className="text-sm lg:text-base">
                                                                //     {index <
                                                                //     groupField
                                                                //         .items
                                                                //         .length -
                                                                //         1
                                                                //         ? field.label +
                                                                //           ' -'
                                                                //         : field.label}
                                                                //     {displayDescription(
                                                                //         field.name,
                                                                //         logBookConfig,
                                                                //     ) && (
                                                                //         <SeaLogsButton
                                                                //             icon="alert"
                                                                //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                                                //             action={() => {
                                                                //                 setDescriptionPanelContent(
                                                                //                     displayDescription(
                                                                //                         field.name,
                                                                //                         logBookConfig,
                                                                //                     ),
                                                                //                 )
                                                                //                 setOpenDescriptionPanel(
                                                                //                     true,
                                                                //                 )
                                                                //                 setDescriptionPanelHeading(
                                                                //                     field.name,
                                                                //                 )
                                                                //             }}
                                                                //         />
                                                                //     )}{' '}
                                                                // </span>
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        // field.handleChange(
                                                                        //     false,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        // field.handleChange(
                                                                        //     true,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                                {/* <div className="flex flex-row">
                                                    <DailyCheckGroupField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        groupField={groupField?.items?.filter(
                                                            (field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                        )}
                                                        handleYesChange={() =>
                                                            handleGroupYesChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        handleNoChange={() =>
                                                            handleGroupNoChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        defaultNoChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Not_Ok',
                                                            )}
                                                        defaultYesChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Ok',
                                                            )}
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    groupField.name,
                                                                ),
                                                                composeField(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                groupField.name,
                                                            )?.comment
                                                        }
                                                    />
                                                    {groupField?.items?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                className={`lg:!grid-cols-2 hidden`}
                                                                innerWrapperClassName={`lg:!col-span-1`}
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div> */}
                                            </div>
                                        </div>
                                    ))}
                                <hr className="my-6" />
                                {getFilteredFields(
                                    preElectricalVisualFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )
                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="mt-6 text-sm font-semibold uppercase text-left">
                                                {getFieldLabel(
                                                    groupField.name,
                                                    logBookConfig,
                                                )}
                                            </div>
                                            <div className="w-full">
                                                <div className="">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                // <span
                                                                //     key={`${field.label}-${index}`}
                                                                //     className="text-sm lg:text-base">
                                                                //     {index <
                                                                //     groupField
                                                                //         .items
                                                                //         .length -
                                                                //         1
                                                                //         ? field.label +
                                                                //           ' -'
                                                                //         : field.label}
                                                                //     {displayDescription(
                                                                //         field.name,
                                                                //         logBookConfig,
                                                                //     ) && (
                                                                //         <SeaLogsButton
                                                                //             icon="alert"
                                                                //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                                                //             action={() => {
                                                                //                 setDescriptionPanelContent(
                                                                //                     displayDescription(
                                                                //                         field.name,
                                                                //                         logBookConfig,
                                                                //                     ),
                                                                //                 )
                                                                //                 setOpenDescriptionPanel(
                                                                //                     true,
                                                                //                 )
                                                                //                 setDescriptionPanelHeading(
                                                                //                     field.name,
                                                                //                 )
                                                                //             }}
                                                                //         />
                                                                //     )}{' '}
                                                                // </span>
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        // field.handleChange(
                                                                        //     false,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        // field.handleChange(
                                                                        //     true,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                                {/* <div className="md:col-span-2">
                                                    <DailyCheckGroupField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        groupField={groupField?.items?.filter(
                                                            (field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                        )}
                                                        handleYesChange={() =>
                                                            handleGroupYesChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        handleNoChange={() =>
                                                            handleGroupNoChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        defaultNoChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Not_Ok',
                                                            )}
                                                        defaultYesChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Ok',
                                                            )}
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    groupField.name,
                                                                ),
                                                                composeField(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                groupField.name,
                                                            )?.comment
                                                        }
                                                    />
                                                    {groupField?.items?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                className={`lg:!grid-cols-2 hidden`}
                                                                innerWrapperClassName={`lg:!col-span-1`}
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div> */}
                                            </div>
                                        </div>
                                    ))}
                                <hr className="my-6" />
                                <CustomDailyCheckField
                                    locked={locked || !edit_logBookEntry}
                                    displayField={displayField(
                                        'Generator',
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        'Generator',
                                        logBookConfig,
                                    )}
                                    setDescriptionPanelContent={
                                        setDescriptionPanelContent
                                    }
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={
                                        // 'Generator is working as expected'
                                        getFieldLabel(
                                            'Generator',
                                            logBookConfig,
                                        )
                                    }
                                    className="flex"
                                    inputId={'generator'}
                                    handleNoChange={() =>
                                        handleEngineChecks(false, 'generator')
                                    }
                                    defaultNoChecked={
                                        vesselDailyCheck?.generator === 'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleEngineChecks(true, 'generator')
                                    }
                                    defaultYesChecked={
                                        vesselDailyCheck?.generator === 'Ok'
                                    }
                                    commentAction={() =>
                                        showCommentPopup(
                                            getComment('Generator'),
                                            composeField(
                                                'Generator',
                                                logBookConfig,
                                            ),
                                        )
                                    }
                                    comment={getComment('Generator')?.comment}
                                />
                                <CustomDailyCheckField
                                    locked={locked || !edit_logBookEntry}
                                    displayField={displayField(
                                        'ShorePower',
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        'ShorePower',
                                        logBookConfig,
                                    )}
                                    setDescriptionPanelContent={
                                        setDescriptionPanelContent
                                    }
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={
                                        // 'Shore power is disconnected'
                                        getFieldLabel(
                                            'ShorePower',
                                            logBookConfig,
                                        )
                                    }
                                    className={'flex'}
                                    inputId={'shorePower'}
                                    handleNoChange={() =>
                                        handleEngineChecks(false, 'shorePower')
                                    }
                                    defaultNoChecked={
                                        vesselDailyCheck?.shorePower ===
                                        'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleEngineChecks(true, 'shorePower')
                                    }
                                    defaultYesChecked={
                                        vesselDailyCheck?.shorePower === 'Ok'
                                    }
                                    commentAction={() =>
                                        showCommentPopup(
                                            getComment('ShorePower'),
                                            composeField(
                                                'ShorePower',
                                                logBookConfig,
                                            ),
                                        )
                                    }
                                    comment={getComment('ShorePower')?.comment}
                                />
                            </div>
                        )}
                    </>
                ) : (
                    <></>
                )}
                {getFilteredFields(preFields, true, logBookConfig)?.filter(
                    (groupField: any) =>
                        displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="mt-6 text-sm font-semibold uppercase text-left">
                            Steering checks
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col">
                                {getFilteredFields(
                                    preFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )
                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="w-full">
                                                <div className="flex-wrap">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                // <span
                                                                //     key={`${field.label}-${index}`}
                                                                //     className="text-sm lg:text-base">
                                                                //     {index <
                                                                //     groupField
                                                                //         .items
                                                                //         .length -
                                                                //         1
                                                                //         ? field.label +
                                                                //           ' -'
                                                                //         : field.label}
                                                                //     {displayDescription(
                                                                //         field.name,
                                                                //         logBookConfig,
                                                                //     ) && (
                                                                //         <SeaLogsButton
                                                                //             icon="alert"
                                                                //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                                                //             action={() => {
                                                                //                 setDescriptionPanelContent(
                                                                //                     displayDescription(
                                                                //                         field.name,
                                                                //                         logBookConfig,
                                                                //                     ),
                                                                //                 )
                                                                //                 setOpenDescriptionPanel(
                                                                //                     true,
                                                                //                 )
                                                                //                 setDescriptionPanelHeading(
                                                                //                     field.name,
                                                                //                 )
                                                                //             }}
                                                                //         />
                                                                //     )}{' '}
                                                                // </span>
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        // field.handleChange(
                                                                        //     false,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        // field.handleChange(
                                                                        //     true,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                                {/* <div className="flex flex-row">
                                                    <DailyCheckGroupField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        groupField={groupField?.items?.filter(
                                                            (field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                        )}
                                                        handleYesChange={() =>
                                                            handleGroupYesChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        handleNoChange={() =>
                                                            handleGroupNoChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        defaultNoChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Not_Ok',
                                                            )}
                                                        defaultYesChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Ok',
                                                            )}
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    groupField.name,
                                                                ),
                                                                composeField(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                groupField.name,
                                                            )?.comment
                                                        }
                                                    />
                                                    {groupField?.items?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                className={`lg:!grid-cols-2 hidden`}
                                                                innerWrapperClassName={`lg:!col-span-1`}
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div> */}
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                        <hr className="my-6" />
                        <CrewChecker
                            vesselDailyCheckID={vesselDailyCheck.id}
                            crewKey="PreCrewResponsible"
                            timeKey="PreCheckTime"
                            logBookConfig={logBookConfig}
                            locked={locked}
                            offline={offline}
                            edit_logBookEntry={edit_logBookEntry}
                            setCrewResponsible={handlePreCrewResponsible}
                            crewResponsible={preCrewResponsible}
                            checkTime={preCheckTime}
                            handleCheckTime={handlePreCheckTime}
                            setCheckTime={setPreCheckTime}
                        />
                    </>
                )}
            </div>
            <Button
                className={`${tab === 'post-startup' ? classes.active : classes.inactive} uppercase whitespace-nowrap block lg:hidden`}
                onPress={() => {
                    tab === 'post-startup'
                        ? changeTab('')
                        : changeTab('post-startup')
                }}>
                Post-startup checks
            </Button>
            <div className={`${tab === 'post-startup' ? '' : 'hidden'} `}>
                {getFilteredFields(
                    postEngineFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 ||
                getFilteredFields(
                    postEngineStrainersFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 ||
                displayField('ForwardReverse', logBookConfig) ? (
                    <>
                        <div className="mt-6 text-sm font-semibold uppercase text-left">
                            Engine and propulsion
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col">
                                {getFilteredFields(
                                    postEngineFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )
                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="w-full">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                // <span
                                                                //     key={`${field.label}-${index}`}
                                                                //     className="text-sm lg:text-base">
                                                                //     {index <
                                                                //     groupField
                                                                //         .items
                                                                //         .length -
                                                                //         1
                                                                //         ? field.label +
                                                                //           ' -'
                                                                //         : field.label}
                                                                //     {displayDescription(
                                                                //         field.name,
                                                                //         logBookConfig,
                                                                //     ) && (
                                                                //         <SeaLogsButton
                                                                //             icon="alert"
                                                                //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                                                //             action={() => {
                                                                //                 setDescriptionPanelContent(
                                                                //                     displayDescription(
                                                                //                         field.name,
                                                                //                         logBookConfig,
                                                                //                     ),
                                                                //                 )
                                                                //                 setOpenDescriptionPanel(
                                                                //                     true,
                                                                //                 )
                                                                //                 setDescriptionPanelHeading(
                                                                //                     field.name,
                                                                //                 )
                                                                //             }}
                                                                //         />
                                                                //     )}{' '}
                                                                // </span>
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        // field.handleChange(
                                                                        //     false,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        // field.handleChange(
                                                                        //     true,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                                {/* <div className="flex">
                                                    <DailyCheckGroupField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        groupField={groupField?.items?.filter(
                                                            (field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                        )}
                                                        handleYesChange={() =>
                                                            handleGroupYesChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        handleNoChange={() =>
                                                            handleGroupNoChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        defaultNoChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Not_Ok',
                                                            )}
                                                        defaultYesChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Ok',
                                                            )}
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    groupField.name,
                                                                ),
                                                                composeField(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                groupField.name,
                                                            )?.comment
                                                        }
                                                    />
                                                    {groupField?.items?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                className={`lg:!grid-cols-2 hidden`}
                                                                innerWrapperClassName={`lg:!col-span-1`}
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div> */}
                                            </div>
                                        </div>
                                    ))}
                                <hr className="my-6" />
                                {getFilteredFields(
                                    postEngineStrainersFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )
                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="w-full">
                                                <div className="mt-6 text-sm font-semibold uppercase text-left">
                                                    {getFieldLabel(
                                                        groupField.name,
                                                        logBookConfig,
                                                    )}
                                                </div>
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                // <span
                                                                //     key={`${field.label}-${index}`}
                                                                //     className="text-sm lg:text-base">
                                                                //     {index <
                                                                //     groupField
                                                                //         .items
                                                                //         .length -
                                                                //         1
                                                                //         ? field.label +
                                                                //           ' -'
                                                                //         : field.label}
                                                                //     {displayDescription(
                                                                //         field.name,
                                                                //         logBookConfig,
                                                                //     ) && (
                                                                //         <SeaLogsButton
                                                                //             icon="alert"
                                                                //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                                                //             action={() => {
                                                                //                 setDescriptionPanelContent(
                                                                //                     displayDescription(
                                                                //                         field.name,
                                                                //                         logBookConfig,
                                                                //                     ),
                                                                //                 )
                                                                //                 setOpenDescriptionPanel(
                                                                //                     true,
                                                                //                 )
                                                                //                 setDescriptionPanelHeading(
                                                                //                     field.name,
                                                                //                 )
                                                                //             }}
                                                                //         />
                                                                //     )}{' '}
                                                                // </span>
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        // field.handleChange(
                                                                        //     false,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        // field.handleChange(
                                                                        //     true,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                                {/* <div className="flex text-left">
                                                    <DailyCheckGroupField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        groupField={groupField?.items?.filter(
                                                            (field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                        )}
                                                        handleYesChange={() =>
                                                            handleGroupYesChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        handleNoChange={() =>
                                                            handleGroupNoChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        defaultNoChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Not_Ok',
                                                            )}
                                                        defaultYesChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Ok',
                                                            )}
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    groupField.name,
                                                                ),
                                                                composeField(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                groupField.name,
                                                            )?.comment
                                                        }
                                                    />
                                                    {groupField?.items?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                className={`lg:!grid-cols-2 hidden`}
                                                                innerWrapperClassName={`lg:!col-span-1`}
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div> */}
                                            </div>
                                        </div>
                                    ))}
                                <hr className="my-6" />
                                <CustomDailyCheckField
                                    locked={locked || !edit_logBookEntry}
                                    displayField={displayField(
                                        'ForwardReverse',
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        'ForwardReverse',
                                        logBookConfig,
                                    )}
                                    setDescriptionPanelContent={
                                        setDescriptionPanelContent
                                    }
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={getFieldLabel(
                                        'ForwardReverse',
                                        logBookConfig,
                                    )}
                                    inputId={'forwardReverse'}
                                    handleNoChange={() =>
                                        handleEngineChecks(
                                            false,
                                            'forwardReverse',
                                        )
                                    }
                                    defaultNoChecked={
                                        vesselDailyCheck?.forwardReverse ===
                                        'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleEngineChecks(
                                            true,
                                            'forwardReverse',
                                        )
                                    }
                                    defaultYesChecked={
                                        vesselDailyCheck?.forwardReverse ===
                                        'Ok'
                                    }
                                    commentAction={() =>
                                        showCommentPopup(
                                            getComment('ForwardReverse'),
                                            composeField(
                                                'ForwardReverse',
                                                logBookConfig,
                                            ),
                                        )
                                    }
                                    comment={
                                        getComment('ForwardReverse')?.comment
                                    }
                                />
                            </div>
                        )}
                    </>
                ) : (
                    <></>
                )}
                {getFilteredFields(
                    postElectricalFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Electrical
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    postElectricalFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                // <span
                                                                //     key={`${field.label}-${index}`}
                                                                //     className="text-sm lg:text-base">
                                                                //     {index <
                                                                //     groupField
                                                                //         .items
                                                                //         .length -
                                                                //         1
                                                                //         ? field.label +
                                                                //           ' -'
                                                                //         : field.label}
                                                                //     {displayDescription(
                                                                //         field.name,
                                                                //         logBookConfig,
                                                                //     ) && (
                                                                //         <SeaLogsButton
                                                                //             icon="alert"
                                                                //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                                                //             action={() => {
                                                                //                 setDescriptionPanelContent(
                                                                //                     displayDescription(
                                                                //                         field.name,
                                                                //                         logBookConfig,
                                                                //                     ),
                                                                //                 )
                                                                //                 setOpenDescriptionPanel(
                                                                //                     true,
                                                                //                 )
                                                                //                 setDescriptionPanelHeading(
                                                                //                     field.name,
                                                                //                 )
                                                                //             }}
                                                                //         />
                                                                //     )}{' '}
                                                                // </span>
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        // field.handleChange(
                                                                        //     false,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        // field.handleChange(
                                                                        //     true,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                                {/* <div className="">
                                                    <DailyCheckGroupField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        groupField={groupField?.items?.filter(
                                                            (field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                        )}
                                                        handleYesChange={() =>
                                                            handleGroupYesChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        handleNoChange={() =>
                                                            handleGroupNoChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        defaultNoChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Not_Ok',
                                                            )}
                                                        defaultYesChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Ok',
                                                            )}
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    groupField.name,
                                                                ),
                                                                composeField(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                groupField.name,
                                                            )?.comment
                                                        }
                                                    />
                                                    {groupField?.items?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                className={`lg:!grid-cols-2 hidden`}
                                                                innerWrapperClassName={`lg:!col-span-1`}
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div> */}
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                    </>
                )}
                {getFilteredFields(
                    postSteeringFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Steering
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col">
                                {getFilteredFields(
                                    postSteeringFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )
                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="w-full">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                // <span
                                                                //     key={`${field.label}-${index}`}
                                                                //     className="text-sm lg:text-base">
                                                                //     {index <
                                                                //     groupField
                                                                //         .items
                                                                //         .length -
                                                                //         1
                                                                //         ? field.label +
                                                                //           ' -'
                                                                //         : field.label}
                                                                //     {displayDescription(
                                                                //         field.name,
                                                                //         logBookConfig,
                                                                //     ) && (
                                                                //         <SeaLogsButton
                                                                //             icon="alert"
                                                                //             className="w-6 h-6 sup -mt-2 ml-0.5"
                                                                //             action={() => {
                                                                //                 setDescriptionPanelContent(
                                                                //                     displayDescription(
                                                                //                         field.name,
                                                                //                         logBookConfig,
                                                                //                     ),
                                                                //                 )
                                                                //                 setOpenDescriptionPanel(
                                                                //                     true,
                                                                //                 )
                                                                //                 setDescriptionPanelHeading(
                                                                //                     field.name,
                                                                //                 )
                                                                //             }}
                                                                //         />
                                                                //     )}{' '}
                                                                // </span>
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        // field.handleChange(
                                                                        //     false,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        // field.handleChange(
                                                                        //     true,
                                                                        // )
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {/* <SeaLogsButton
                                                    icon="alert"
                                                    className="w-6 h-6 sup -mt-2 ml-0.5"
                                                    action={() => {å
                                                        setDescriptionPanelContent(
                                                            displayDescription(
                                                                groupField.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        setOpenDescriptionPanel(
                                                            true,
                                                        )
                                                        setDescriptionPanelHeading(
                                                            groupField.name,
                                                        )
                                                    }}
                                                /> */}
                                                </div>
                                                {/* <div className="flex">
                                                    <DailyCheckGroupField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        groupField={groupField?.items?.filter(
                                                            (field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                        )}
                                                        handleYesChange={() =>
                                                            handleGroupYesChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        handleNoChange={() =>
                                                            handleGroupNoChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        defaultNoChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Not_Ok',
                                                            )}
                                                        defaultYesChecked={groupField?.items
                                                            ?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Ok',
                                                            )}
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    groupField.name,
                                                                ),
                                                                composeField(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                groupField.name,
                                                            )?.comment
                                                        }
                                                    />
                                                    {groupField?.items?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                className={`lg:!grid-cols-2 hidden`}
                                                                innerWrapperClassName={`lg:!col-span-1`}
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div> */}
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}

                        <hr className="my-6" />
                        <CrewChecker
                            vesselDailyCheckID={vesselDailyCheck.id}
                            crewKey="PostCrewResponsible"
                            timeKey="PostCheckTime"
                            logBookConfig={logBookConfig}
                            locked={locked}
                            offline={offline}
                            edit_logBookEntry={edit_logBookEntry}
                            setCrewResponsible={handlePostCrewResponsible}
                            crewResponsible={postCrewResponsible}
                            checkTime={postCheckTime}
                            handleCheckTime={handlePostCheckTime}
                            setCheckTime={setPostCheckTime}
                        />
                    </>
                )}
            </div>
            <Button
                className={`${tab === 'other-engine' ? classes.active : classes.inactive} uppercase whitespace-nowrap block lg:hidden`}
                onPress={() => {
                    tab === 'other-engine'
                        ? changeTab('')
                        : changeTab('other-engine')
                }}>
                Other engine checks
            </Button>
            <div className={`${tab === 'other-engine' ? '' : 'hidden'} `}>
                {getFilteredFields(
                    otherDriveShaftFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Drive Shaft Checks
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    otherDriveShaftFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                    </>
                )}
                {getFilteredFields(
                    otherEngineFieldFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Other Engine Fields
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    otherEngineFieldFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                    </>
                )}
                {getFilteredFields(
                    otherMainEngineCheckFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Main engine(s) checks
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    otherMainEngineCheckFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                    </>
                )}
                {getFilteredFields(
                    otherEngineRoomVisualInspectionFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Engine room visual inspection
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    otherEngineRoomVisualInspectionFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                    </>
                )}
                {getFilteredFields(
                    otherFuelSystemsFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Fuel systems
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    otherFuelSystemsFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                    </>
                )}
                {getFilteredFields(
                    otherPropulsionCheckFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Propulsion Check
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    otherPropulsionCheckFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                        <hr className="my-6" />
                        <CrewChecker
                            vesselDailyCheckID={vesselDailyCheck.id}
                            crewKey="OtherEngineCrewResponsible"
                            timeKey="OtherEngineCheckTime"
                            logBookConfig={logBookConfig}
                            locked={locked}
                            offline={offline}
                            edit_logBookEntry={edit_logBookEntry}
                            setCrewResponsible={
                                handleOtherEngineCrewResponsible
                            }
                            crewResponsible={otherEngineCrewResponsible}
                            checkTime={otherEngineCheckTime}
                            handleCheckTime={handleOtherEngineCheckTime}
                            setCheckTime={setOtherEngineCheckTime}
                        />
                    </>
                )}
            </div>
            <Button
                className={`${tab === 'engineering' ? classes.active : classes.inactive} uppercase whitespace-nowrap block lg:hidden`}
                onPress={() => {
                    tab === 'engineering'
                        ? changeTab('')
                        : changeTab('engineering')
                }}>
                Engineering
            </Button>
            <div className={`${tab === 'engineering' ? '' : 'hidden'} `}>
                {getFilteredFields(
                    engrMechanicalFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Mechanical
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    engrMechanicalFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                    </>
                )}
                {getFilteredFields(
                    engrGeneratorFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Generator
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    engrGeneratorFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                    </>
                )}
                {getFilteredFields(
                    engrElectronicsFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Electronics
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    engrElectronicsFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                    </>
                )}
                {getFilteredFields(
                    engrTowlineWinchFields,
                    true,
                    logBookConfig,
                )?.filter((groupField: any) =>
                    displayField(groupField.name, logBookConfig),
                ).length > 0 && (
                    <>
                        <hr className="my-6" />
                        <div className="my-4 text-sm font-semibold uppercase text-left">
                            Towline & Winch
                        </div>
                        {logBookConfig && vesselDailyCheck && (
                            <div className="flex flex-col text-left">
                                {getFilteredFields(
                                    engrTowlineWinchFields,
                                    true,
                                    logBookConfig,
                                )
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )

                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="w-full">
                                            <div className="flex flex-row gap-2 items-center justify-between">
                                                <div className="w-full">
                                                    {groupField?.items
                                                        ?.filter((field: any) =>
                                                            displayField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                        ?.map(
                                                            (
                                                                field: any,
                                                                index: number,
                                                            ) => (
                                                                <DailyCheckField
                                                                    locked={
                                                                        locked ||
                                                                        !edit_logBookEntry
                                                                    }
                                                                    key={index}
                                                                    displayField={displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    displayDescription={displayDescription(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    setDescriptionPanelContent={
                                                                        setDescriptionPanelContent
                                                                    }
                                                                    setOpenDescriptionPanel={
                                                                        setOpenDescriptionPanel
                                                                    }
                                                                    setDescriptionPanelHeading={
                                                                        setDescriptionPanelHeading
                                                                    }
                                                                    displayLabel={getFieldLabel(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    )}
                                                                    inputId={
                                                                        field.value
                                                                    }
                                                                    handleNoChange={() =>
                                                                        handleEngineChecks(
                                                                            false,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultNoChecked={
                                                                        field.checked ===
                                                                        'Not_Ok'
                                                                    }
                                                                    handleYesChange={() =>
                                                                        handleEngineChecks(
                                                                            true,
                                                                            field.value,
                                                                        )
                                                                    }
                                                                    defaultYesChecked={
                                                                        field.checked ===
                                                                        'Ok'
                                                                    }
                                                                    commentAction={() =>
                                                                        showCommentPopup(
                                                                            getComment(
                                                                                field.name,
                                                                            ),
                                                                            composeField(
                                                                                field.name,
                                                                                logBookConfig,
                                                                            ),
                                                                        )
                                                                    }
                                                                    comment={
                                                                        getComment(
                                                                            field.name,
                                                                        )
                                                                            ?.comment
                                                                    }
                                                                />
                                                            ),
                                                        )}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        )}
                    </>
                )}
                <hr className="my-6" />
                <CrewChecker
                    vesselDailyCheckID={vesselDailyCheck.id}
                    crewKey="EngrCrewResponsible"
                    timeKey="EngrCheckTime"
                    logBookConfig={logBookConfig}
                    locked={locked}
                    offline={offline}
                    edit_logBookEntry={edit_logBookEntry}
                    setCrewResponsible={handleEngrCrewResponsible}
                    crewResponsible={engrCrewResponsible}
                    checkTime={engrCheckTime}
                    handleCheckTime={handleEngrCheckTime}
                    setCheckTime={setEngrCheckTime}
                />
            </div>
            {/* {(!locked || edit_logBookEntry) && (
                <FooterWrapper>
                    {' '}
                    <SeaLogsButton
                        text="Cancel"
                        type="text"
                        action={() => router.back()}
                    />{' '}
                    <SeaLogsButton
                        text="Create Task"
                        type="secondary"
                        color="slblue"
                        icon="check"
                        action={handleCreateTask}
                        isDisabled={createMaintenanceCheckLoading}
                    />
                    <SeaLogsButton
                        text="Save"
                        type="primary"
                        color="sky"
                        icon="check"
                        action={handleSave}
                    />{' '}
                </FooterWrapper>
            )} */}
            <AlertDialog
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={handleSaveComment}
                noButton={locked}
                actionText="Save">
                <div
                    className={`flex flex-col ${locked || !edit_logBookEntry ? 'pointer-events-none' : ''}`}>
                    <textarea
                        id="comment"
                        readOnly={locked || !edit_logBookEntry}
                        rows={4}
                        className="block p-2.5 w-full mt-4 text-sm text-slblue-900 bg-slblue-50 rounded-lg border border-slblue-300 focus:ring-blue-500 focus:border-blue-500    "
                        placeholder="Comment"
                        defaultValue={
                            currentComment ? currentComment.comment : ''
                        }></textarea>
                </div>
            </AlertDialog>
            <SlidingPanel
                type={'left'}
                isOpen={openDescriptionPanel}
                size={isWide ? 60 : 90}
                backdropClicked={() => {
                    setOpenDescriptionPanel(false)
                    setDescriptionPanelContent('')
                    setDescriptionPanelHeading('')
                }}>
                <div className="h-[calc(100vh_-_1rem)] pt-4">
                    {openDescriptionPanel && (
                        <div className="bg-sllightblue-50 h-full flex flex-col justify-between rounded-r-lg">
                            <div className="items-center flex justify-between font-medium py-2 px-4 sm:py-4 sm:px-6 rounded-tr-lg bg-slblue-1000">
                                <Heading
                                    slot="title"
                                    className="text-lg sm:text-xl font-semibold leading-6 my-2 text-white text-left sm:text-center">
                                    Field -{' '}
                                    <span className="font-thin">
                                        {descriptionPanelHeading}
                                    </span>
                                </Heading>
                                <XMarkIcon
                                    className="w-6 h-6 sm:w-8 sm:h-8 text-white "
                                    onClick={() => {
                                        setOpenDescriptionPanel(false)
                                        setDescriptionPanelContent('')
                                        setDescriptionPanelHeading('')
                                    }}
                                />
                            </div>

                            <div className="text-xl p-4 flex-grow overflow-scroll ql-container">
                                <div className="ql-editor">
                                    <div
                                        className="text-xs leading-loose font-light"
                                        dangerouslySetInnerHTML={{
                                            __html: descriptionPanelContent,
                                        }}></div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </SlidingPanel>
            <Toaster position="top-right" />
        </div>
    )
}

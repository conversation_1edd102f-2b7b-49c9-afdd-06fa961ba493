'use client'

import { OverdueTrainingList } from '@/app/ui/crew-training/list'
import { TrainingList } from '@/app/ui/reporting/crew-training-completed-report'

export default function CrewTrainingTab({
    trainingSessions,
    trainingSessionDues,
}: {
    trainingSessions: any
    trainingSessionDues: any
}) {
    return (
        <div>
            {trainingSessionDues?.length > 0 && (
                <div className="block mb-5">
                    <OverdueTrainingList
                        trainingSessionDues={trainingSessionDues}
                        isVesselView={true}
                    />
                </div>
            )}
            {trainingSessions?.length > 0 && (
                <div className="block">
                    <TrainingList
                        trainingList={trainingSessions}
                        trainingSessionDues={trainingSessionDues}
                        isVesselView={true}
                    />
                </div>
            )}
        </div>
    )
}

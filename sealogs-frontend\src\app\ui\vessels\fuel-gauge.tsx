'use client'

// https://codesandbox.io/p/sandbox/gauge-recharts-v2-1hegx?file=%2Fsrc%2FApp.js%3A127%2C31 => reference for gauge chart
import { Label, PolarRadiusAxis, RadialBar, RadialBarChart } from 'recharts'

import {
    ChartConfig,
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart'

const chartConfig = {
    safe: {
        label: 'Safe Capacity',
        color: 'hsl(var(--destructive))',
    },
    total: {
        label: 'Total Capacity',
        color: 'hsl(var(--accent))',
    },
} satisfies ChartConfig

interface IProps {
    title: string
    currentCapacity: number
    safeCapacity: number
    maxCapacity: number
}

const RADIAN = Math.PI / 180

const Arrow = ({
    cx,
    cy,
    midAngle,
    outerRadius,
    width,
}: {
    cx: number
    cy: number
    midAngle: number
    outerRadius: number
    width: number
}) => {
    //eslint-disable-line react/no-multi-comp
    const sin = Math.sin(RADIAN * midAngle)
    const cos = Math.cos(RADIAN * midAngle)
    const mx = cx + (outerRadius + width * 0.03) * cos
    const my = cy + (outerRadius + width * 0.03) * sin
    return (
        <g>
            <path
                d={`M${cx},${cy}L${mx},${my}`}
                strokeWidth="4"
                stroke="black"
                fill="none"
                strokeLinecap="round"
            />
            <circle cx={cx} cy={cy} r={5} fill="black" stroke="none" />
        </g>
    )
}

export default function FuelGauge({
    title,
    currentCapacity,
    safeCapacity,
    maxCapacity,
}: IProps) {
    const chartData = [
        {
            month: 'Fuel Level',
            safe: safeCapacity,
            total: maxCapacity - safeCapacity,
        },
    ]

    return (
        <ChartContainer config={chartConfig} className="w-[250px]">
            <RadialBarChart
                title={title}
                data={chartData}
                endAngle={180}
                height={400}
                cy={110}
                innerRadius={80}
                outerRadius={130}>
                <ChartTooltip
                    cursor={false}
                    content={
                        <ChartTooltipContent
                            hideLabel
                            formatter={(value, name) => {
                                return (
                                    <div className="flex min-w-[150px] items-center text-xs text-muted-foreground">
                                        {chartConfig[
                                            name as keyof typeof chartConfig
                                        ]?.label || name}
                                        <div className="ml-auto flex items-baseline gap-1 font-mono font-medium tabular-nums text-foreground">
                                            {name == 'total'
                                                ? maxCapacity
                                                : value}
                                            <span className="font-normal text-muted-foreground">
                                                L
                                            </span>
                                        </div>
                                    </div>
                                )
                            }}
                        />
                    }
                />
                <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
                    <Label
                        content={({ viewBox }) => {
                            if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                                return (
                                    <Arrow
                                        cx={viewBox.cx!}
                                        cy={(viewBox.cy || 0) - 16}
                                        midAngle={
                                            180 +
                                            (currentCapacity / maxCapacity) *
                                                180
                                        }
                                        outerRadius={viewBox.innerRadius! - 32}
                                        width={100}
                                    />
                                )
                            }
                        }}
                    />
                    <Label
                        content={({ viewBox }) => {
                            if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                                return (
                                    <text
                                        x={viewBox.cx}
                                        y={viewBox.cy}
                                        textAnchor="middle">
                                        <tspan
                                            x={viewBox.cx}
                                            y={(viewBox.cy || 0) + 10}
                                            className="fill-foreground font-semibold">
                                            {currentCapacity.toLocaleString()} L
                                        </tspan>
                                        <tspan
                                            x={viewBox.cx}
                                            y={(viewBox.cy || 0) + 27}
                                            className="fill-muted-foreground text-sm">
                                            {title}
                                        </tspan>
                                    </text>
                                )
                            }
                        }}
                    />
                </PolarRadiusAxis>
                <RadialBar
                    dataKey="total"
                    stackId="a"
                    cornerRadius={1.5}
                    className=" stroke-bright-turquoise-600 fill-teal-50 stroke-[1.5]"
                />
                <RadialBar
                    dataKey="safe"
                    stackId="a"
                    fill="hsl(var(--destructive) / 0.05)"
                    stroke="hsl(var(--destructive))"
                    cornerRadius={1.5}
                    className="stroke-transparent stroke-[1.5]"
                />
            </RadialBarChart>
        </ChartContainer>
    )
}

'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import { GET_INCIDENT_RECORDS } from './graphql/queries'
import CustomPagination from '@/components/ui/custom-pagination'
import dayjs from 'dayjs'
import Loading from '@/app/loading'
import { formatDate } from '@/app/helpers/dateHelper'
import DatePicker from '@/components/DateRange'
import VesselDropdown from '@/components/filter/components/vessel-dropdown'
import { H3 } from '@/components/ui'
import Link from 'next/link'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import TableWrapper from '@/components/ui/table-wrapper'

interface SearchFilter {
    vessel?: any
    dateRange?: {
        startDate: string | null
        endDate: string | null
    }
}

const IncidentRecordList = () => {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [incidentRecords, setIncidentRecords] = useState([])
    const limit = 100
    const [pageInfo, setPageInfo] = useState({
        totalCount: 0,
        hasNextPage: false,
        hasPreviousPage: false,
    })
    const [page, setPage] = useState(0)
    const [filter, setFilter] = useState<SearchFilter>({})

    // GraphQL query to fetch incident records
    const [getIncidentRecords, { loading: queryIncidentRecordsLoading }] =
        useLazyQuery(GET_INCIDENT_RECORDS, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response) => {
                if (response && response.readIncidentRecords) {
                    setIncidentRecords(response.readIncidentRecords.nodes)
                    setPageInfo(response.readIncidentRecords.pageInfo)
                    setIsLoading(false)
                }
            },
            onError: (error) => {
                console.error('Error fetching incident records:', error)
                setIsLoading(false)
            },
        })

    // Load incident records on initial render and when page or filter changes
    useEffect(() => {
        loadIncidentRecords()
    }, [page, filter])

    // Function to load incident records with pagination and filters
    const loadIncidentRecords = () => {
        const variables: any = {
            limit,
            offset: page * limit,
        }

        // Add filters if they exist
        if (Object.keys(filter).length > 0) {
            const graphqlFilter: any = {}

            // Add vessel filter
            if (filter.vessel && filter.vessel.value) {
                graphqlFilter.vesselID = { eq: filter.vessel.value }
            }

            // Add date range filter
            if (filter.dateRange) {
                if (filter.dateRange.startDate) {
                    graphqlFilter.startDate = {
                        gte: dayjs(filter.dateRange.startDate)
                            .startOf('day')
                            .toISOString(),
                    }
                }
                if (filter.dateRange.endDate) {
                    graphqlFilter.endDate = {
                        lte: dayjs(filter.dateRange.endDate)
                            .endOf('day')
                            .toISOString(),
                    }
                }
            }

            variables.filter = graphqlFilter
        }

        getIncidentRecords({ variables })
    }

    // Handle pagination navigation
    const handleNavigationClick = (newPage: number) => {
        setPage(newPage)
    }

    // Handle filter changes
    const handleFilterChange = (type: string, data: any) => {
        setFilter((prevFilter) => ({
            ...prevFilter,
            [type]: data,
        }))
        setPage(0) // Reset to first page when filter changes
    }

    // Format names for display
    const formatName = (person: any) => {
        // Return dash if person doesn't exist, or if id is zero
        return person && +person.id !== 0
            ? `${person.firstName} ${person.surname}`
            : ''
    }

    // Format members to notify for display
    const formatMembersToNotify = (members: any) => {
        if (!members || !members.nodes || members.nodes.length === 0) {
            return ''
        }
        return members.nodes
            .map((member: any) => `${member.firstName} ${member.surname}`)
            .join(', ')
    }

    return (
        <div className="w-full py-0">
            <div className="flex justify-between mb-4">
                <H3>Incident Records</H3>
                <div>
                    <SeaLogsButton
                        text="New Incident Record"
                        type="primary"
                        icon="check"
                        color="slblue"
                        action={() => {
                            router.push('/incident-records/create')
                        }}
                    />
                </div>
            </div>

            {/* Filters */}
            <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="w-full md:w-1/3">
                    <VesselDropdown
                        isClearable={true}
                        placeholder="Filter by Vessel"
                        onChange={(data: any) =>
                            handleFilterChange('vessel', data)
                        }
                    />
                </div>
                <div className="w-full md:w-2/3">
                    <DatePicker
                        placeholder="Filter by Date Range"
                        onChange={(data: any) =>
                            handleFilterChange('dateRange', data)
                        }
                    />
                </div>
            </div>

            {/* Table */}
            <div className="shadow-sm w-full border border-slblue-100 my-4 rounded-lg">
                {isLoading || queryIncidentRecordsLoading ? (
                    <Loading />
                ) : (
                    <>
                        <TableWrapper
                            headings={[
                                'Title',
                                'Start Date',
                                'End Date',
                                'Vessel',
                                'Reported By',
                                'Members to Notify',
                            ]}
                            showHeader>
                            {incidentRecords.length === 0 ? (
                                <tr>
                                    <td colSpan={6} className="p-4 text-center">
                                        No incident records found
                                    </td>
                                </tr>
                            ) : (
                                incidentRecords.map((incident: any) => (
                                    <tr
                                        key={incident.id}
                                        className={`border-b border-sldarkblue-50 even:bg-sllightblue-50/50 hover:bg-sllightblue-50`}>
                                        <td className="p-2">
                                            <Link
                                                href={`/incident-records/edit/?id=${incident.id}`}
                                                className="group-hover:text-sllightblue-1000">
                                                {incident.title || '-'}
                                            </Link>
                                        </td>
                                        <td className="p-2">
                                            {formatDate(incident.startDate)}
                                        </td>
                                        <td className="p-2">
                                            {formatDate(incident.endDate)}
                                        </td>
                                        <td className="p-2">
                                            {incident.vessel?.title || '-'}
                                        </td>
                                        <td className="p-2">
                                            {formatName(incident.reportedBy)}
                                        </td>
                                        <td className="p-2">
                                            {formatMembersToNotify(
                                                incident.membersToNotify,
                                            )}
                                        </td>
                                    </tr>
                                ))
                            )}
                        </TableWrapper>

                        {/* Pagination */}
                        <CustomPagination
                            page={page}
                            limit={limit}
                            visiblePageCount={5}
                            {...pageInfo}
                            onClick={(newPage: number) =>
                                handleNavigationClick(newPage)
                            }
                        />
                    </>
                )}
            </div>
        </div>
    )
}

export default IncidentRecordList

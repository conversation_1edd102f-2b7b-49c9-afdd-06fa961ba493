'use client'

import { useEffect, useMemo, useState } from 'react'
import vesselTypes from '@/app/lib/vesselTypes'
import {
    UPDATE_CUSTOMISED_COMPONENT_FIELD,
    UPDATE_CUSTOMISED_LOGBOOK_COMPONENT,
    CREATE_CUSTOMISED_COMPONENT_FIELD,
    CREATE_CUSTOMISED_LOGBOOK_COMPONENT,
    UPDATE_CUSTOMISED_LOGBOOK_CONFIG,
    CreateCustomisedLogBookConfig,
} from '@/app/lib/graphQL/mutation'
import { useLazyQuery, useMutation } from '@apollo/client'
import { GET_LOGBOOK_CONFIG } from '@/app/lib/graphQL/query'
import { getVesselByID } from '@/app/lib/actions'
import { Heading } from 'react-aria-components'
import { useRouter } from 'next/navigation'
import { toast, useToast } from '@/hooks/use-toast'
import { isCrew } from '@/app/helpers/userHelper'
import Sliding<PERSON>anel from 'react-sliding-side-panel'
import 'react-sliding-side-panel/lib/index.css'
import { XMarkIcon } from '@heroicons/react/24/outline'
import Editor from '../editor'
import {
    filterByVesselType,
    sortTabs,
    getYesCheckedStatus,
    getLevelThreeCategory,
    isCategorised,
    getComponentsNotInConfig,
    getFieldName,
    isFieldInConfig,
    getLevelThreeCategoryGroup,
    fieldIsGroup,
    sortCustomisedComponentFields,
} from './actions'
import 'react-quill/dist/quill.snow.css'
import CustomisedComponentFieldModel from '@/app/offline/models/customisedComponentField'
import CustomisedLogBookConfigModel from '@/app/offline/models/customisedLogBookConfig'
import { uniqueLogbookComponents } from '@/app/helpers/logBookHelper'
import {
    getSLALL_LogBookFieldsByVessel,
    RequiredFields,
    SLALL_LogBookFields,
} from '@/app/lib/logbook-configuration'
import LogbookConfigurationForm from './components/log-book-configuration-form'
import { LogBookConfiguration } from '@/app/lib/logbook-configuration/types'
import { FooterWrapper, SeaLogsButton } from '../daily-checks/Components'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Toaster } from '@/components/ui/toaster'
import { Card } from '@/components/ui'
import { H1, H2, P } from '@/components/ui/typography'

export interface ITab {
    title: string
    category: string
    componentClass: string
}

export default function LogbookConfig({
    logBookID,
    vesselID,
}: {
    logBookID: number
    vesselID: number
}) {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [logBookConfig, setLogBookConfig] = useState<any>(false)
    const [updatedFields, setUpdatedFields] = useState<any>([])
    const [updatedLocalFields, setUpdatedLocalFields] = useState<any>([])
    const [vessel, setVessel] = useState<any>(false)
    const [openConfigEditDialog, setOpenConfigEditDialog] = useState(false)
    const [currentField, setCurrentField] = useState<any>(false)
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')
    const [tab, setTab] = useState<string>('')
    const [tabs, setTabs] = useState<ITab[]>([])
    const [categoryTab, setCategoryTab] = useState<any>()
    const [categoryTabs, setCategoryTabs] = useState<any>()
    const [dailyCheckCategory, setDailyCheckCategory] = useState<any>(false)
    const [dailyCheckCategories, setDailyCheckCategories] = useState<any>(false)
    const [levelThreeCategories, setLevelThreeCategories] = useState<any>(false)
    const [levelThreeCategory, setLevelThreeCategory] = useState<any>(false)
    const [resetCounter, setResetCounter] = useState(-1)
    const [documents, setDocuments] = useState<Array<Record<string, any>>>([])
    const [saving, setSaving] = useState(false)
    const [notify, setNotify] = useState(true)
    const [imCrew, setImCrew] = useState(false)
    const [filteredFields, setFilteredFields] = useState<
        LogBookConfiguration[] | null
    >(null)
    const [content, setContent] = useState<any>('')

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const handleSetVessel = (vessel: any) => {
        if (vessel?.vesselType) {
            const logbookFields = SLALL_LogBookFields.filter((field: any) => {
                if (field?.items?.length > 0) {
                    return field.vesselType.includes(
                        vesselTypes.indexOf(vessel?.vesselType),
                    )
                }
                return false
            })
            var filteredFields: any = []
            logbookFields.map((logbookField: any) => {
                var currentField = logbookField
                var currentFieldItems: any = []
                logbookField.items.map((fields: any) => {
                    if (
                        fields.vesselType.includes(
                            vesselTypes.indexOf(vessel?.vesselType),
                        )
                    ) {
                        if (
                            vessel?.vesselSpecifics?.carriesDangerousGoods ==
                            false
                        ) {
                            if (fields.classes !== 'dangerous-goods-sailing') {
                                currentFieldItems.push(fields)
                            }
                        } else {
                            currentFieldItems.push(fields)
                        }
                    }
                    // if (fields.hasDynamicChildren) {
                    //     const dynamicChildren =
                    //         vessel.defaultRadioLogs.nodes.map(
                    //             (radioLog: any) => {
                    //                 return {
                    //                     value: radioLog.title,
                    //                     label: radioLog.title,
                    //                     vesselType: [
                    //                         0, 1, 2, 3, 4, 5, 6, 7, 8, 9,
                    //                     ],
                    //                     status: 'Required',
                    //                     groupTo: 'RadioLog',
                    //                 }
                    //             },
                    //         )
                    //     currentFieldItems.push(...dynamicChildren)
                    // }
                })
                currentField.items = currentFieldItems
                filteredFields.push(currentField)
            })
            setFilteredFields(filteredFields)
        }
        setVessel(vessel)
        loadLogBookConfig()
    }

    getVesselByID(vesselID, handleSetVessel)

    const slallFields = useMemo(() => {
        return filteredFields ? filteredFields : SLALL_LogBookFields
    }, [filteredFields])

    const [queryLogBookConfig] = useLazyQuery(GET_LOGBOOK_CONFIG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            /**
             * Removes duplicate components from the customisedLogBookComponents array based on the componentClass property.
             * If a duplicate is found, it keeps the component with the highest id value.
             * @param {Object} responseData - The response data containing customisedLogBookComponents.
             * @param {Array} responseData.customisedLogBookComponents.nodes - The array of components to be deduplicated.
             * @returns {Array} The unique components with the highest id value for each componentClass.
             */
            const responseData = response.readOneCustomisedLogBookConfig
            /* const customisedLogBookComponents =
                responseData?.customisedLogBookComponents?.nodes ?? []
            let uniqueComponents = customisedLogBookComponents.reduce(
                (acc: any, current: any) => {
                    const existing = acc.find(
                        (item: any) =>
                            item.componentClass === current.componentClass,
                    )
                    if (existing) {
                        if (Number(current.id) > Number(existing.id)) {
                            return acc.map((item: any) =>
                                item.componentClass === current.componentClass
                                    ? current
                                    : item,
                            )
                        }
                        return acc
                    }
                    return [...acc, current]
                },
                [],
            )
            uniqueComponents = uniqueComponents.map((uniqueComponent: any) => {
                const matchingLogBookField = SLALL_LogBookFields.find(
                    (logBookField: any) =>
                        logBookField.componentClass ===
                        uniqueComponent.componentClass,
                )
                if (matchingLogBookField) {
                    return {
                        ...uniqueComponent,
                        title: matchingLogBookField.label,
                    }
                }
                return uniqueComponent
            }) */
            let uniqueComponents = uniqueLogbookComponents(responseData)
            uniqueComponents = uniqueComponents.map((uniqueComponent: any) => {
                if (uniqueComponent.customisedComponentFields.nodes) {
                    uniqueComponent.customisedComponentFields.nodes.sort(
                        sortCustomisedComponentFields,
                    )
                }
                return uniqueComponent
            })
            responseData.customisedLogBookComponents.nodes = uniqueComponents

            const data = filterByVesselType(
                responseData,
                slallFields,
                vesselTypes,
                vessel,
            )

            if (data) {
                setUpdatedLocalFields([])
                setUpdatedFields([])

                setLogBookConfig(data)
                {
                    data.policies.nodes.length > 0 &&
                        setDocuments(data.policies.nodes)
                }
                if (!tab) {
                    const tabs = data.customisedLogBookComponents?.nodes
                        .map((component: any) => ({
                            title: component.title,
                            category: component.category,
                            componentClass: component.componentClass,
                        }))
                        .sort()
                    const logbookFields = slallFields
                    const config = data.customisedLogBookComponents.nodes
                    const defaultConfig = logbookFields.map(
                        (component: any) => component,
                    )
                    var componentsNotInConfig: any = []
                    defaultConfig.forEach((defaultLogBookComponents: any) => {
                        var found = false
                        config.forEach((customisedLogBookComponents: any) => {
                            if (
                                customisedLogBookComponents.componentClass ===
                                defaultLogBookComponents.componentClass
                            ) {
                                found = true
                            }
                        })
                        if (!found) {
                            componentsNotInConfig.push(defaultLogBookComponents)
                        }
                    })
                    const additionalTabs = componentsNotInConfig.map(
                        (component: any) => ({
                            title: component.label,
                            category: component.category,
                            componentClass: component.componentClass,
                        }),
                    )
                    const sortedTabs = sortTabs(
                        [...tabs, ...additionalTabs],
                        slallFields,
                    )
                    setTabs(sortedTabs)
                    setTab(sortedTabs[0].title)
                    const categoryTabs: string[] = Array.from(
                        new Set<string>(
                            data.customisedLogBookComponents?.nodes.map(
                                (component: any) => component.category,
                            ),
                        ),
                    )
                    setCategoryTabs(categoryTabs)
                    setCategoryTab(categoryTabs[0])
                    var currentTab = false
                    sortedTabs.forEach((element: any) => {
                        if (element.category === categoryTabs[0]) {
                            if (!currentTab) {
                                setTab(element.title)
                            }
                            currentTab = element.title
                        }
                    })
                }
            } else {
                document.body.style.cursor = 'wait'
                createCustomisedLogBookConfig({
                    variables: {
                        input: {
                            customisedLogBookID: logBookID,
                        },
                    },
                })
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookConfig error', error)
        },
    })

    const [createCustomisedLogBookConfig] = useMutation(
        CreateCustomisedLogBookConfig,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedLogBookConfig
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    useEffect(() => {
        if (isLoading && vessel) {
            setImCrew(isCrew() || false)
            setIsLoading(false)
            handleSetDailyCheckCategories()
            handleLevelThreeCategories()
        }
    }, [isLoading, vessel])

    const loadLogBookConfig = async () => {
        await queryLogBookConfig({
            variables: {
                id: +logBookID,
            },
        })
    }

    const updateFieldStatus = (field: any, status: string) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const appendedData = [
                ...updatedFields.filter(
                    (updatedField: any) => updatedField.fieldID !== field.id,
                ),
                { fieldID: field.id, status: status },
            ]
            setUpdatedFields(appendedData)
            document.body.style.cursor = 'wait'
            updateCustomisedComponentField({
                variables: {
                    input: {
                        id: field.id,
                        status: status,
                    },
                },
            })
        } else {
            setUpdatedLocalFields([
                ...updatedLocalFields.filter(
                    (updatedField: any) =>
                        updatedField.localID !== field.localID,
                ),
                { ...field, status: status },
            ])
            document.body.style.cursor = 'wait'
            createCustomisedComponentField({
                variables: {
                    input: {
                        customisedFieldTitle: field?.title
                            ? field.title
                            : field.label,
                        customisedLogBookComponentID:
                            field.customisedLogBookComponentID,
                        fieldName: field.value,
                        status: status,
                        sortOrder: field.sortOrder || 0,
                    },
                },
            })
        }
    }

    const [createCustomisedComponentField] = useMutation(
        CREATE_CUSTOMISED_COMPONENT_FIELD,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedComponentField
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )
    const deleteLocalCustomisedComponentField = async (id: any) => {
        if (+id > 0) {
            const customisedComponentFieldModel =
                new CustomisedComponentFieldModel()
            await customisedComponentFieldModel.delete(id)
        }
    }

    const [updateCustomisedComponentField] = useMutation(
        UPDATE_CUSTOMISED_COMPONENT_FIELD,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedComponentField
                deleteLocalCustomisedComponentField(data.id)
                if (resetCounter > 0) {
                    setResetCounter(resetCounter - 1)
                }
                if (resetCounter == 0) {
                    setResetCounter(resetCounter - 1)
                }
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const handleUpdateConfigEdit = async () => {
        const customisedFieldTitle = (
            document.getElementById('field-name') as HTMLInputElement
        )?.value
        const sortOrder = (
            document.getElementById('field-sort-order') as HTMLInputElement
        )?.value
        document.body.style.cursor = 'wait'
        await updateCustomisedComponentField({
            variables: {
                input: {
                    id: currentField.id,
                    customisedFieldTitle: customisedFieldTitle,
                    sortOrder: +sortOrder,
                    description: content === '<p><br></p>' ? '' : content,
                },
            },
        })
        setOpenConfigEditDialog(false)
        handleEditorChange('')
    }

    const handleSetDailyCheckCategories = () => {
        const logbookFields = slallFields

        const dailyCheckCategories = Array.from(
            new Set(
                logbookFields.filter((field: any) => field.subCategory).length >
                0
                    ? logbookFields
                          .filter((field: any) => field.subCategory)[0]
                          .items.filter((item: any) => item.level !== 3) // Exclude level 3 items
                          .map((item: any) => {
                              return item.fieldSet ? item.fieldSet : 'Other' // Map main categories
                          })
                    : 'Other',
            ),
        )

        setDailyCheckCategories(
            dailyCheckCategories.filter(
                (category: any) =>
                    category !== 'Checks' &&
                    category !== 'Other' &&
                    category !== 'Documentation' &&
                    category !== 'Fuel Checks',
            ),
        )
        if (!dailyCheckCategory) {
            setDailyCheckCategory(dailyCheckCategories[0])
        }
    }

    const handleLevelThreeCategories = () => {
        const logbookFields = slallFields
        const levelThreeCategories: any = Array.from(
            new Set(
                logbookFields
                    .filter((field: any) => field.subCategory)[0]
                    .items.filter((field: any) => field.level === 3)
                    .map((field: any) => {
                        return {
                            fieldSet: field.fieldSet,
                            label: field.label,
                            status: field.status,
                        }
                    }),
            ),
        )
        setLevelThreeCategories(levelThreeCategories)
        if (!levelThreeCategory) {
            setLevelThreeCategory(levelThreeCategories[0].label)
        }
    }

    const mapConfigToDefault = (currentComponent: any) => {
        const logbookFields = slallFields
        setResetCounter(resetCounter + 1)
        const config = logBookConfig.customisedLogBookComponents.nodes
            .filter((component: any) => component.id == currentComponent.id)
            .map((component: any) => component)
        const defaultConfig = logbookFields.map((component: any) => component)
        config.forEach((customisedLogBookComponents: any) => {
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    customisedLogBookComponents.componentClass ===
                    defaultLogBookComponents.componentClass
                ) {
                    customisedLogBookComponents.customisedComponentFields.nodes
                        .filter(
                            (customFields: any, index: number, self: any[]) =>
                                self.findIndex(
                                    (c: any) =>
                                        c.fieldName === customFields.fieldName,
                                ) === index,
                        )
                        .forEach((customFields: any) => {
                            defaultLogBookComponents.items.forEach(
                                (defaultField: any) => {
                                    if (
                                        customFields.fieldName ===
                                        defaultField.value
                                    ) {
                                        const updatedField = updatedFields.find(
                                            (updatedField: any) =>
                                                updatedField.fieldID ===
                                                customFields.id,
                                        )
                                        if (
                                            defaultField.status !=
                                            customFields.status
                                        ) {
                                            document.body.style.cursor = 'wait'
                                            updateCustomisedComponentField({
                                                variables: {
                                                    input: {
                                                        id: customFields.id,
                                                        status: defaultField.status,
                                                    },
                                                },
                                            })
                                            setResetCounter(resetCounter + 1)
                                        }
                                        if (
                                            updatedField?.fieldID &&
                                            updatedField?.status !=
                                                defaultField.status
                                        ) {
                                            document.body.style.cursor = 'wait'
                                            updateCustomisedComponentField({
                                                variables: {
                                                    input: {
                                                        id: customFields.id,
                                                        status: defaultField.status,
                                                    },
                                                },
                                            })
                                            setResetCounter(resetCounter + 1)
                                        }
                                    }
                                },
                            )
                        })
                }
            })
        })
    }

    const changeTab = (tab: string) => () => {
        setTab(tab?.replace('Logbook', 'LogBook'))
    }

    const changeCategoryTab = (tab: string) => () => {
        setCategoryTab(tab)
        var currentTab = false
        tabs.forEach((element: any) => {
            if (
                element.category.replace('Logbook', 'LogBook') ===
                tab?.replace('Logbook', 'LogBook')
            ) {
                if (!currentTab) {
                    setTab(element.title.replace('Logbook', 'LogBook'))
                }
                currentTab = element.title.replace('Logbook', 'LogBook')
            }
        })
    }

    const [updateCustomisedComponent] = useMutation(
        UPDATE_CUSTOMISED_LOGBOOK_COMPONENT,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedLogBookComponent
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const activateCustomisedComponent = (id: number) => () => {
        document.body.style.cursor = 'wait'
        updateCustomisedComponent({
            variables: {
                input: {
                    id: id,
                    active: true,
                },
            },
        })
    }

    const deactivateCustomisedComponent = (id: number) => () => {
        document.body.style.cursor = 'wait'
        updateCustomisedComponent({
            variables: {
                input: {
                    id: id,
                    active: false,
                },
            },
        })
    }

    const activateCustomisedSubComponent =
        (component: any, category: string) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: component.subFields + '||' + category,
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .concat(category)
                                .join('||'),
                        },
                    },
                })
            }
        }

    const deactivateCustomisedSubComponent =
        (component: any, category: string) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: component.subFields
                                .split('||')
                                .filter((field: any) => field !== category)
                                .join('||'),
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .filter((cat: any) => cat != category)
                                .join('||'),
                        },
                    },
                })
            }
        }

    const activateCustomisedLevelThreeComponent =
        (component: any, levelThree: any) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields:
                                component.subFields + '||' + levelThree.label,
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .concat(levelThree.label)
                                .join('||'),
                        },
                    },
                })
            }
            //if (levelThree?.status === 'disabled') {
            levelThreeCategories.map((category: any) => {
                if (category.label === levelThree.label) {
                    category.status = 'Enabled'
                }
                return category
            })
            //}
        }

    const deactivateCustomisedLevelThreeComponent =
        (component: any, levelThree: any) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: component.subFields
                                .split('||')
                                .filter(
                                    (field: any) => field !== levelThree.label,
                                )
                                .join('||'),
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .filter((cat: any) => cat != levelThree.label)
                                .join('||'),
                        },
                    },
                })
            }
            //if (levelThree?.status === 'enabled') {
            levelThreeCategories.map((category: any) => {
                if (category.label === levelThree.label) {
                    category.status = 'Disabled'
                }
                return category
            })
            //}
        }

    const changeDailyChecksCategoryTab = (tab: string) => () => {
        setDailyCheckCategory(tab)
    }

    const changeLevelThreeCategoryTab = (tab: string) => () => {
        setLevelThreeCategory(tab)
    }

    const [createCustomisedComponent] = useMutation(
        CREATE_CUSTOMISED_LOGBOOK_COMPONENT,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedComponent
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const isRequiredField = (field: any) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const isRequired = RequiredFields.includes(field.fieldName)
                ? true
                : false
            if (
                isRequired &&
                !getYesCheckedStatus(field, updatedFields, updatedLocalFields)
            ) {
                updateFieldStatus(field, 'Required')
            }
            return isRequired
        } else {
            const isRequired = RequiredFields.includes(field.value)
                ? true
                : false
            if (
                isRequired &&
                !getYesCheckedStatus(field, updatedFields, updatedLocalFields)
            ) {
                if (
                    updatedLocalFields.filter(
                        (updatedField: any) =>
                            updatedField.localID !== field.localID,
                    ).length === 0
                ) {
                    document.body.style.cursor = 'wait'
                    createCustomisedComponentField({
                        variables: {
                            input: {
                                customisedFieldTitle: field?.title
                                    ? field.title
                                    : field.label,
                                customisedLogBookComponentID:
                                    field.customisedLogBookComponentID,
                                fieldName: field.value,
                                status: 'Required',
                                sortOrder: field.sortOrder || 0,
                            },
                        },
                    })
                }
                setUpdatedLocalFields([
                    ...updatedLocalFields.filter(
                        (updatedField: any) =>
                            updatedField.localID !== field.localID,
                    ),
                    { ...field, status: 'Required' },
                ])
            }
            return isRequired
        }
    }

    const handleSave = (notify = true) => {
        if (notify) {
            setSaving(true)
            toast({
                description: 'Logbook configuration saving...',
            })
        } else {
            setNotify(false)
        }
        const policies =
            documents.length > 0
                ? documents?.map((doc: any) => +doc.id).join(',')
                : ''
        document.body.style.cursor = 'wait'
        updateCustomisedLogBookConfig({
            variables: {
                input: {
                    id: logBookConfig.id,
                    policies: policies,
                },
            },
        })
        setIsLoading(true)
    }

    useEffect(() => {
        if (!isLoading && documents) {
            setNotify(false)
            handleSave(false)
        }
    }, [documents])

    const deleteLocalCustomisedLogBookConfig = async (id: any) => {
        if (+id > 0) {
            const customisedLogBookConfigModel =
                new CustomisedLogBookConfigModel()
            await customisedLogBookConfigModel.delete(id)
        }
    }

    const [updateCustomisedLogBookConfig] = useMutation(
        UPDATE_CUSTOMISED_LOGBOOK_CONFIG,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedLogBookConfig
                deleteLocalCustomisedLogBookConfig(data.id)
                if (data.id > 0) {
                    if (notify) {
                        toast({
                            description: 'Logbook configuration saved',
                        })
                        setSaving(false)
                    }
                    setNotify(true)
                }
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const filterFieldsWithGroup = (fields: any, componentClass: string) => {
        const logbookFields = slallFields
        const defaultConfig = logbookFields.map((component: any) => component)
        var groupFields: any = []
        var groups: any = []
        defaultConfig.forEach((defaultLogBookComponents: any) => {
            if (
                tab?.replace('Logbook', 'LogBook') ===
                    defaultLogBookComponents.label.replace(
                        'Logbook',
                        'LogBook',
                    ) &&
                defaultLogBookComponents.componentClass === componentClass
            ) {
                defaultLogBookComponents.items.forEach((defaultField: any) => {
                    if (defaultField.groupTo && defaultField.level !== 3) {
                        if (!groups.includes(defaultField.groupTo)) {
                            groups.push(defaultField.groupTo)
                        }
                    }
                })
            }
        })
        defaultConfig.forEach((defaultLogBookComponents: any) => {
            if (
                tab?.replace('Logbook', 'LogBook') ===
                defaultLogBookComponents.label.replace('Logbook', 'LogBook')
            ) {
                defaultLogBookComponents.items.forEach(
                    (defaultField: any, index: number) => {
                        groups.forEach((group: any) => {
                            if (
                                defaultField.value === group &&
                                !isFieldInConfig(
                                    defaultField.value,
                                    logBookConfig,
                                    tab,
                                )
                            ) {
                                if (defaultField.level !== 3) {
                                    groupFields.push({
                                        ...defaultField,
                                        description: null,
                                        fieldName: defaultField.value,
                                        id: index + '0',
                                        sortOrder: index,
                                        status: defaultField.status,
                                    })
                                }
                            }
                        })
                    },
                )
            }
        })
        if (groups.length > 0) {
            const flatGroupFields = groupFields.flatMap(
                (group: any) => group.value,
            )
            return [
                ...groupFields.map((group: any) => {
                    let groupData = group
                    fields.map((field: any) => {
                        if (group.value === field.fieldName) {
                            groupData = { ...group, ...field }
                        }
                    })
                    return groupData
                }),
                ...fields.filter(
                    (field: any) => !flatGroupFields.includes(field.fieldName),
                ),
            ]
                .filter((field: any) =>
                    filterFieldsWithGroupCombinedFilter(field),
                )
                .sort(sortCustomisedComponentFields)
        }
        return fields
            .filter((field: any) => filterFieldsWithGroupCombinedFilter(field))
            .sort(sortCustomisedComponentFields)
    }

    const filterFieldsWithGroupCombinedFilter = (field: any) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tab?.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tab === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.fieldName,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.fieldName === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.fieldName === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false &&
                returnField !== false &&
                groupFields !== false
            )
        } else {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tab?.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tab === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.value,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.value === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.value === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false &&
                returnField !== false &&
                groupFields !== false
            )
        }
    }

    const customisedComponentFieldsCombinedFilter = (field: any) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tab?.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tab === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.fieldName,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.fieldName === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.fieldName === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false && returnField !== false && groupFields == false
            )
        } else {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tab?.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tab === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.value,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.value === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.value === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false && returnField !== false && groupFields == false
            )
        }
    }

    const checkLevelThree = (field: any): boolean => {
        if (
            getLevelThreeCategory(
                field,
                slallFields,
                tab?.replace('Logbook', 'LogBook'),
            )
        ) {
            if (
                getLevelThreeCategory(
                    field,
                    slallFields,
                    tab?.replace('Logbook', 'LogBook'),
                ) === levelThreeCategory
            ) {
                return true
            }
            return false
        }
        return true
    }

    const checkLevelThreeGroup = (field: any) => {
        const levelThreeCategoryGroup = getLevelThreeCategoryGroup(
            field,
            slallFields,
            tab?.replace('Logbook', 'LogBook'),
        )
        if (levelThreeCategoryGroup) {
            if (levelThreeCategoryGroup === levelThreeCategory) {
                return true
            }
            return false
        }
        return true
    }

    useEffect(() => {
        if (!tab && Array.isArray(tabs) && tabs.length > 0) {
            setTab(tabs[0].title)
        }
    }, [tab, tabs])

    const isEmpty = (title: any) => {
        if (title) {
            return title.trim().length === 0
        }
        return false
    }

    const isPreDepartureCheckSectionEnabled = (
        dailyCheckCategory: any,
        dailyCheckCategories: any,
        component: any,
        levelThreeCategory: any = null,
        levelThreeCategories: any = [],
    ) => {
        let isEnabled = false
        if (dailyCheckCategory !== 'Engine Checks') {
            if (component.subFields) {
                if (dailyCheckCategories.includes(dailyCheckCategory)) {
                    const subFieldsList = component.subFields.split('||')
                    isEnabled = subFieldsList.includes(dailyCheckCategory)
                } else {
                    // do nothing
                }
            } else if (!isEnabled) {
                isEnabled = component.active
            }
        } else {
            // Engine checks
            isEnabled = true
            const result = levelThreeCategories.some(
                (category: any) => category.label === levelThreeCategory,
            )
            if (result) {
                if (component.subFields) {
                    const subFieldsList = component.subFields.split('||')
                    isEnabled = subFieldsList.includes(levelThreeCategory)
                } else if (!isEnabled) {
                    isEnabled = component.active
                }
            } else {
                // do nothing
            }
        }
        return isEnabled
    }

    const isRadioLogsChecked = () => {
        if (logBookConfig) {
            const tripReportComponent =
                logBookConfig.customisedLogBookComponents.nodes.find(
                    (component: any) =>
                        component.componentClass ===
                        'TripReport_LogBookComponent',
                )

            if (tripReportComponent?.customisedComponentFields?.nodes) {
                const radioLogs =
                    tripReportComponent.customisedComponentFields.nodes.find(
                        (field: any) =>
                            field.fieldName === 'RadioLog' &&
                            field.status === 'Required',
                    )
                if (radioLogs) {
                    return true
                }
            }
        }
        return false
    }

    return (
        <>
            <div className="flex items-center justify-between ">
                <H1>
                    {' '}
                    Logbook Configuration -{' '}
                    <span className="">{vessel?.title}</span>{' '}
                </H1>
                <SeaLogsButton
                    text="Back to Vessel"
                    type="text"
                    className=" "
                    icon="back_arrow"
                    action={() => router.back()}
                />
            </div>
            <Card className=" mb-10">
                <div className="border-t border-cool-grey-300 px-4">
                    {logBookConfig && (
                        <div className={`my-4 `}>
                            {' '}
                            <Label className="inline-block   mr-4 w-32">
                                {' '}
                                Vessel Title:{' '}
                            </Label>{' '}
                            {vessel?.title}{' '}
                        </div>
                    )}
                    {vessel && (
                        <div className={`my-4 `}>
                            {' '}
                            <Label className="inline-block   mr-4 w-32">
                                {' '}
                                Vessel Type:{' '}
                            </Label>{' '}
                            {vessel?.vesselType?.replaceAll('_', ' ')}{' '}
                        </div>
                    )}
                </div>

                {!isLoading && tabs && (
                    <LogbookConfigurationForm
                        tabs={tabs}
                        tab={tab}
                        changeTab={changeTab}
                        logBookConfig={logBookConfig}
                        slallFields={slallFields}
                        imCrew={imCrew}
                        mapConfigToDefault={mapConfigToDefault}
                        activateCustomisedComponent={
                            activateCustomisedComponent
                        }
                        deactivateCustomisedComponent={
                            deactivateCustomisedComponent
                        }
                        dailyCheckCategories={dailyCheckCategories}
                        dailyCheckCategory={dailyCheckCategory}
                        changeDailyChecksCategoryTab={
                            changeDailyChecksCategoryTab
                        }
                        levelThreeCategories={levelThreeCategories}
                        levelThreeCategory={levelThreeCategory}
                        changeLevelThreeCategoryTab={
                            changeLevelThreeCategoryTab
                        }
                        activateCustomisedSubComponent={
                            activateCustomisedSubComponent
                        }
                        deactivateCustomisedSubComponent={
                            deactivateCustomisedSubComponent
                        }
                        activateCustomisedLevelThreeComponent={
                            activateCustomisedLevelThreeComponent
                        }
                        deactivateCustomisedLevelThreeComponent={
                            deactivateCustomisedLevelThreeComponent
                        }
                        customisedComponentFieldsCombinedFilter={
                            customisedComponentFieldsCombinedFilter
                        }
                        checkLevelThree={checkLevelThree}
                        handleEditorChange={handleEditorChange}
                        setCurrentField={setCurrentField}
                        setDescriptionPanelContent={setDescriptionPanelContent}
                        setDescriptionPanelHeading={setDescriptionPanelHeading}
                        setOpenConfigEditDialog={setOpenConfigEditDialog}
                        setOpenDescriptionPanel={setOpenDescriptionPanel}
                        checkLevelThreeGroup={checkLevelThreeGroup}
                        createCustomisedComponent={createCustomisedComponent}
                        documents={documents}
                        setDocuments={setDocuments}
                        filterFieldsWithGroup={filterFieldsWithGroup}
                        isRequiredField={isRequiredField}
                        updateFieldStatus={updateFieldStatus}
                        updatedFields={updatedFields}
                        updatedLocalFields={updatedLocalFields}
                    />
                )}
                <FooterWrapper>
                    <SeaLogsButton
                        text="Cancel"
                        type="text"
                        action={() => {
                            router.back()
                        }}
                    />
                    {!isLoading &&
                        tabs &&
                        logBookConfig.customisedLogBookComponents?.nodes
                            ?.length > 0 &&
                        logBookConfig.customisedLogBookComponents?.nodes?.map(
                            (component: any) => (
                                <div
                                    key={component.id}
                                    className={`${tab?.replace('Logbook', 'LogBook') === component.title.replace('Logbook', 'LogBook') ? '' : 'hidden'}`}>
                                    {isCategorised(
                                        component,
                                        slallFields,
                                        logBookConfig,
                                    ) && (
                                        <>
                                            {component.active ? (
                                                <>
                                                    {!imCrew && (
                                                        <>
                                                            <SeaLogsButton
                                                                text="Disable"
                                                                type="secondary"
                                                                color="rose"
                                                                action={deactivateCustomisedComponent(
                                                                    component.id,
                                                                )}
                                                            />
                                                            <SeaLogsButton
                                                                type="secondary"
                                                                text="Reset default"
                                                                color="sky"
                                                                action={() =>
                                                                    mapConfigToDefault(
                                                                        component,
                                                                    )
                                                                }
                                                            />
                                                        </>
                                                    )}
                                                </>
                                            ) : (
                                                <>
                                                    {!imCrew && (
                                                        <SeaLogsButton
                                                            text="Enable"
                                                            type="secondary"
                                                            color="sky"
                                                            action={activateCustomisedComponent(
                                                                component.id,
                                                            )}
                                                        />
                                                    )}
                                                </>
                                            )}
                                        </>
                                    )}
                                </div>
                            ),
                        )}
                    {!isLoading &&
                        tabs &&
                        logBookConfig &&
                        getComponentsNotInConfig(
                            slallFields,
                            logBookConfig,
                        )?.map((component: any) => (
                            <div
                                key={component.label}
                                className={`${tab?.replace('Logbook', 'LogBook') === component.label.replace('Logbook', 'LogBook') ? '' : 'hidden'}`}>
                                {!imCrew && (
                                    <SeaLogsButton
                                        type="secondary"
                                        text={`Add ${component.label}`}
                                        icon="check"
                                        color="sky"
                                        action={() => {
                                            document.body.style.cursor = 'wait'
                                            createCustomisedComponent({
                                                variables: {
                                                    input: {
                                                        title: component.label,
                                                        sortOrder:
                                                            component.sortOrder ||
                                                            0,
                                                        category:
                                                            component.category,
                                                        customisedLogBookConfigID:
                                                            logBookConfig.id,
                                                        componentClass:
                                                            component.componentClass,
                                                        active: true,
                                                    },
                                                },
                                            })
                                        }}
                                    />
                                )}
                            </div>
                        ))}
                    {!imCrew && (
                        <SeaLogsButton
                            text={saving ? 'Saving' : 'Save'}
                            type="primary"
                            icon="check"
                            color="sky"
                            action={handleSave}
                        />
                    )}
                </FooterWrapper>
                <SlidingPanel
                    type={'right'}
                    isOpen={openConfigEditDialog}
                    size={40}>
                    <div className="h-[calc(100vh_-_1rem)] pt-4">
                        {openConfigEditDialog && (
                            <div className="  h-full flex flex-col justify-between rounded-l-lg">
                                <div className="    items-center flex justify-between font-medium py-4 px-6 rounded-tl-lg 0">
                                    <Heading
                                        slot="title"
                                        className="  leading-6 my-2 ">
                                        Field -{' '}
                                        <span className="font-thin">
                                            {currentField?.customisedFieldTitle
                                                ? currentField?.customisedFieldTitle
                                                : getFieldName(
                                                      currentField,
                                                      slallFields,
                                                  )}
                                        </span>
                                    </Heading>
                                    <XMarkIcon
                                        className="w-6 h-6"
                                        onClick={() =>
                                            setOpenConfigEditDialog(false)
                                        }
                                    />
                                </div>
                                <div className="p-4 flex-grow overflow-scroll">
                                    <div className="">
                                        <div className="">
                                            <div
                                                slot="content"
                                                className="mb-4">
                                                {!fieldIsGroup(
                                                    currentField,
                                                    slallFields,
                                                    tab?.replace(
                                                        'Logbook',
                                                        'LogBook',
                                                    ),
                                                ) && (
                                                    <>
                                                        <Input
                                                            type="text"
                                                            id="field-name"
                                                            placeholder="Customised Field Name (Optional)"
                                                            className={''}
                                                            defaultValue={
                                                                currentField?.customisedFieldTitle
                                                            }
                                                        />
                                                        <Input
                                                            type="number"
                                                            id="field-sort-order"
                                                            placeholder="Sort Order"
                                                            className={`mt-4`}
                                                            defaultValue={
                                                                currentField?.sortOrder
                                                            }
                                                        />
                                                    </>
                                                )}
                                                <Heading className=" leading-loose font-medium mt-4   ">
                                                    Procedure description{' '}
                                                </Heading>
                                                <Editor
                                                    id="field-description"
                                                    placeholder="Description (Optional)"
                                                    className={`mt-2`}
                                                    content={content}
                                                    handleEditorChange={
                                                        handleEditorChange
                                                    }
                                                />
                                            </div>
                                            <SeaLogsButton
                                                text="Save"
                                                type="primary"
                                                color="sky"
                                                icon="check"
                                                action={handleUpdateConfigEdit}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </SlidingPanel>
                <SlidingPanel
                    type={'left'}
                    isOpen={openDescriptionPanel}
                    size={40}>
                    <div className="h-[calc(100vh_-_1rem)] pt-4">
                        {openDescriptionPanel && (
                            <div className="  h-full flex flex-col justify-between rounded-r-lg">
                                <div className="    items-center flex justify-between py-4 px-6 rounded-tr-lg 0">
                                    <Heading
                                        slot="title"
                                        className="  leading-6 my-2  ">
                                        Field -{' '}
                                        <span className="font-thin">
                                            {descriptionPanelHeading}
                                        </span>
                                    </Heading>
                                    <XMarkIcon
                                        className="w-6 h-6"
                                        onClick={() => {
                                            setOpenDescriptionPanel(false)
                                            setDescriptionPanelContent('')
                                            setDescriptionPanelHeading('')
                                        }}
                                    />
                                </div>
                                <div className=" p-4 flex-grow overflow-scroll">
                                    <div
                                        className=" leading-loose "
                                        dangerouslySetInnerHTML={{
                                            __html: descriptionPanelContent,
                                        }}></div>
                                </div>
                            </div>
                        )}
                    </div>
                </SlidingPanel>
                <Toaster />
            </Card>
        </>
    )
}

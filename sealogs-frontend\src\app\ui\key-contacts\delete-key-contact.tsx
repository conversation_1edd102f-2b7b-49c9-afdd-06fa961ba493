'use client'

import { DELETE_KEY_CONTACTS } from '@/app/lib/graphQL/mutation'
import { AlertDialogNew } from '@/components/ui'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import { useMutation } from '@apollo/client'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'
import { Heading } from 'react-aria-components'
import toast from 'react-hot-toast'

export default function DeleteKeyContact({
    id,
    fullName,
}: {
    id: number
    fullName: string
}) {
    const router = useRouter()
    const [openConfirm, setOpenConfirm] = useState(false)

    const [mutationDelete] = useMutation(DELETE_KEY_CONTACTS, {
        onCompleted: (response: any) => {
            if (
                response.deleteKeyContacts &&
                response.deleteKeyContacts.length > 0
            ) {
                router.push('/key-contacts')
            } else {
                toast.error('Error deleting key contact')
            }
        },
        onError: (error: any) => {
            console.error('mutationDeleteKeyContacts error:', error.message)
        },
    })

    const handleDelete = () => {
        mutationDelete({
            variables: {
                ids: [id],
            },
        })
    }

    return (
        <>
            <SeaLogsButton
                text="Delete"
                type="secondary"
                icon="trash"
                color="rose"
                action={() => setOpenConfirm(true)}
            />
            <AlertDialogNew
                openDialog={openConfirm}
                setOpenDialog={setOpenConfirm}
                handleCreate={handleDelete}
                actionText="Delete Key Contact">
                <Heading
                    slot="title"
                    className="text-2xl font-light leading-6 my-2 ">
                    Delete Key Contact
                </Heading>
                <div className="my-4 flex items-center">
                    Are you sure you want to delete {fullName}?
                </div>
            </AlertDialogNew>
        </>
    )
}

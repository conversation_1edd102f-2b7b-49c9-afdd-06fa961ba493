'use client'

import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    SeaLogsButton,
} from '@/app/components/Components'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import {
    CreateEventType_RestrictedVisibility,
    UpdateEventType_RestrictedVisibility,
    CreateTripEvent,
    UpdateTripEvent,
    CreateRiskFactor,
    UpdateRiskFactor,
    CreateMitigationStrategy,
} from '@/app/lib/graphQL/mutation'
import { GetRiskFactors, GetTripEvent } from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import toast, { Toaster } from 'react-hot-toast'
import { classes } from '@/app/components/GlobalClasses'
import LocationField from '../components/location'
import TimeField from '../components/time'
import TripEventModel from '@/app/offline/models/tripEvent'
import EventType_RestrictedVisibilityModel from '@/app/offline/models/eventType_RestrictedVisibility'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import SlidingPanel from 'react-sliding-side-panel'
import { TrashIcon, XMarkIcon } from '@heroicons/react/24/outline'
import Select from 'react-select'
import {
    Button,
    DialogTrigger,
    Heading,
    ListBox,
    ListBoxItem,
    Popover,
} from 'react-aria-components'
import Editor from '../../editor'
import Slider from '@mui/material/Slider'
import Creatable from 'react-select/creatable'
import { useSearchParams } from 'next/navigation'

export default function restrictedVisibility({
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    logBookConfig,
    locked,
    offline = false,
    members,
}: {
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    logBookConfig: any
    locked: any
    offline?: boolean
    members: any
}) {
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const [crossingTime, setCrossingTime] = useState<any>()
    const [crossedTime, setCrossedTime] = useState<any>()
    const [restrictedVisibility, setRestrictedVisibility] = useState<any>(false)
    const [tripEvent, setTripEvent] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [openProcedureChecklist, setOpenProcedureChecklist] = useState(false)
    const [displaySOP, setDisplaySOP] = useState(false)
    const [crewMembers, setCrewMembers] = useState<any>([])
    const [selectedAuthor, setSelectedAuthor] = useState<any>()
    const [riskFactors, setRiskFactors] = useState<any>([])
    const [openRiskDialog, setOpenRiskDialog] = useState(false)
    const [currentRisk, setCurrentRisk] = useState<any>()
    const [riskValue, setRiskValue] = useState<any>(null)
    const [riskToDelete, setRiskToDelete] = useState<any>()
    const [allRisks, setAllRisks] = useState<any>(false)
    const [content, setContent] = useState<any>()
    const [openRecommendedstrategy, setOpenRecommendedstrategy] =
        useState(false)
    const [allRiskFactors, setAllRiskFactors] = useState<any>([])
    const [currentStrategies, setCurrentStrategies] = useState<any>([])
    const [recommendedStratagies, setRecommendedStratagies] =
        useState<any>(false)
    const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false)
    const [currentStartLocation, setCurrentStartLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const [currentEndLocation, setCurrentEndLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const tripEventModel = new TripEventModel()
    const restrictedVisibilityModel = new EventType_RestrictedVisibilityModel()
    const handleCrossingTimeChange = (date: any) => {
        setCrossingTime(dayjs(date).format('HH:mm'))
    }

    const handleCrossedTimeChange = (date: any) => {
        setCrossedTime(dayjs(date).format('HH:mm'))
    }

    const handleSetDisplaySOP = (value: any) => {
        setDisplaySOP(value)
    }

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const handleDeleteRisk = async () => {
        updateRiskFactor({
            variables: {
                input: {
                    id: riskToDelete.id,
                    eventType_RestrictedVisibilityID: 0,
                    vesselID: 0,
                },
            },
        })
        setOpenDeleteConfirmation(false)
    }

    const handleNewStrategy = async () => {
        if (content) {
            createMitigationStrategy({
                variables: {
                    input: {
                        strategy: content,
                    },
                },
            })
        }
        setOpenRecommendedstrategy(false)
    }

    const [createMitigationStrategy] = useMutation(CreateMitigationStrategy, {
        onCompleted: (data) => {
            setCurrentStrategies([
                ...currentStrategies,
                { id: data.createMitigationStrategy.id, strategy: content },
            ])
            setContent('')
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const handleSetCurrentStrategies = (strategy: any) => {
        if (currentStrategies.length > 0) {
            if (currentStrategies.find((s: any) => s.id === strategy.id)) {
                setCurrentStrategies(
                    currentStrategies.filter((s: any) => s.id !== strategy.id),
                )
            } else {
                setCurrentStrategies([...currentStrategies, strategy])
            }
        } else {
            setCurrentStrategies([strategy])
        }
    }

    const handleSetRiskValue = (v: any) => {
        setRiskValue({
            value: v.title,
            label: v.title,
        })
        if (v.mitigationStrategy.nodes) {
            setCurrentStrategies(v.mitigationStrategy.nodes)
        }
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.title &&
                    risk.mitigationStrategy.nodes?.length > 0,
            ).length > 0
        ) {
            setRecommendedStratagies(
                Array.from(
                    new Set(
                        allRiskFactors
                            ?.filter(
                                (r: any) =>
                                    r.title === v.title &&
                                    r.mitigationStrategy.nodes?.length > 0,
                            )
                            .map((r: any) => r.mitigationStrategy.nodes)[0]
                            .map((s: any) => ({
                                id: s.id,
                                strategy: s.strategy,
                            })),
                    ),
                ),
            )
        } else {
            setRecommendedStratagies(false)
        }
    }

    useEffect(() => {
        getRiskFactors({
            variables: {
                filter: { type: { eq: 'RestrictedVisibility' } },
            },
        })
    }, [])

    useEffect(() => {
        getRiskFactors({
            variables: {
                filter: { type: { eq: 'RestrictedVisibility' } },
            },
        })
    }, [openProcedureChecklist])

    const [getRiskFactors] = useLazyQuery(GetRiskFactors, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            const risks = Array.from(
                new Set(
                    data.readRiskFactors.nodes?.map((risk: any) => risk.title),
                ),
            )?.map((risk: any) => ({ label: risk, value: risk }))
            setAllRisks(risks)
            setAllRiskFactors(data.readRiskFactors.nodes)
            setRiskFactors(
                data.readRiskFactors.nodes.filter(
                    (r: any) =>
                        r.eventType_RestrictedVisibilityID ==
                        restrictedVisibility?.id,
                ),
            )
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    useEffect(() => {
        setRestrictedVisibility(false)
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        }
    }, [selectedEvent])

    useEffect(() => {
        setRestrictedVisibility(false)
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [currentEvent])

    const getCurrentEvent = async (id: any) => {
        getTripEvent({
            variables: {
                id: id,
            },
        })
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setTripEvent(event)
                setRestrictedVisibility({
                    id: +event.eventType_RestrictedVisibility.id,
                    startLocationID:
                        event.eventType_RestrictedVisibility?.startLocationID,
                    crossingTime:
                        event.eventType_RestrictedVisibility?.crossingTime,
                    estSafeSpeed:
                        event.eventType_RestrictedVisibility?.estSafeSpeed,
                    stopAssessPlan:
                        event.eventType_RestrictedVisibility?.stopAssessPlan,
                    crewBriefing:
                        event.eventType_RestrictedVisibility?.crewBriefing,
                    navLights: event.eventType_RestrictedVisibility?.navLights,
                    soundSignal:
                        event.eventType_RestrictedVisibility?.soundSignal,
                    lookout: event.eventType_RestrictedVisibility?.lookout,
                    soundSignals:
                        event.eventType_RestrictedVisibility?.soundSignals,
                    radarWatch:
                        event.eventType_RestrictedVisibility?.radarWatch,
                    radioWatch:
                        event.eventType_RestrictedVisibility?.radioWatch,
                    endLocationID:
                        event.eventType_RestrictedVisibility?.endLocationID,
                    crossedTime:
                        event.eventType_RestrictedVisibility?.crossedTime,
                    approxSafeSpeed:
                        event.eventType_RestrictedVisibility?.approxSafeSpeed,
                    report: event.eventType_RestrictedVisibility?.report,
                    startLat: event.eventType_RestrictedVisibility?.startLat,
                    startLong: event.eventType_RestrictedVisibility?.startLong,
                    endLat: event.eventType_RestrictedVisibility?.endLat,
                    endLong: event.eventType_RestrictedVisibility?.endLong,
                })
                if (
                    event.eventType_RestrictedVisibility?.startLat &&
                    event.eventType_RestrictedVisibility?.startLong
                ) {
                    setCurrentStartLocation({
                        latitude:
                            event.eventType_RestrictedVisibility?.startLat,
                        longitude:
                            event.eventType_RestrictedVisibility?.startLong,
                    })
                }
                if (
                    event.eventType_RestrictedVisibility?.endLat &&
                    event.eventType_RestrictedVisibility?.endLong
                ) {
                    setCurrentEndLocation({
                        latitude: event.eventType_RestrictedVisibility?.endLat,
                        longitude:
                            event.eventType_RestrictedVisibility?.endLong,
                    })
                }
                setCrossedTime(
                    event.eventType_RestrictedVisibility?.crossedTime,
                )
                setCrossingTime(
                    event.eventType_RestrictedVisibility?.crossingTime,
                )
                if (event.eventType_RestrictedVisibility?.memberID > 0) {
                    setSelectedAuthor({
                        label: `${event.eventType_RestrictedVisibility?.member.firstName} ${event.eventType_RestrictedVisibility?.member.surname}`,
                        value: event.eventType_RestrictedVisibility?.memberID,
                    })
                }
                if (
                    event.eventType_RestrictedVisibility?.stopAssessPlan ||
                    event.eventType_RestrictedVisibility?.crewBriefing ||
                    event.eventType_RestrictedVisibility?.navLights ||
                    event.eventType_RestrictedVisibility?.soundSignal ||
                    event.eventType_RestrictedVisibility?.lookout ||
                    event.eventType_RestrictedVisibility?.soundSignals ||
                    event.eventType_RestrictedVisibility?.radarWatch ||
                    event.eventType_RestrictedVisibility?.radioWatch ||
                    event.eventType_RestrictedVisibility?.memberID > 0
                ) {
                    setDisplaySOP(true)
                }
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleSave = async () => {
        const variables = {
            input: {
                startLocationID: restrictedVisibility?.startLocationID,
                crossingTime: crossingTime ?? dayjs().format('HH:mm'),
                estSafeSpeed: restrictedVisibility?.estSafeSpeed,
                stopAssessPlan: restrictedVisibility?.stopAssessPlan,
                crewBriefing: restrictedVisibility?.crewBriefing,
                navLights: restrictedVisibility?.navLights,
                soundSignal: restrictedVisibility?.soundSignal,
                lookout: restrictedVisibility?.lookout,
                soundSignals: restrictedVisibility?.soundSignals,
                radarWatch: restrictedVisibility?.radarWatch,
                radioWatch: restrictedVisibility?.radioWatch,
                endLocationID: restrictedVisibility?.endLocationID,
                crossedTime: crossedTime ?? dayjs().format('HH:mm'),
                approxSafeSpeed: restrictedVisibility?.approxSafeSpeed,
                report: restrictedVisibility?.report,
                startLat: currentStartLocation.latitude.toString(),
                startLong: currentStartLocation.longitude.toString(),
                endLat: currentEndLocation.latitude.toString(),
                endLong: currentEndLocation.longitude.toString(),
                memberID: selectedAuthor?.value,
            },
        }
        if (currentEvent) {
            if (offline) {
                await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'RestrictedVisibility',
                    logBookEntrySectionID: currentTrip.id,
                })
                toast.success('Trip event updated')
                getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'RestrictedVisibility',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }

            if (offline) {
                await restrictedVisibilityModel.save({
                    id: +selectedEvent?.eventType_RestrictedVisibilityID,
                    ...variables.input,
                })
            } else {
                updateEventType_RestrictedVisibility({
                    variables: {
                        input: {
                            id: +selectedEvent?.eventType_RestrictedVisibilityID,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                const tripEventData = await tripEventModel.save({
                    id: generateUniqueId(),
                    eventCategory: 'RestrictedVisibility',
                    logBookEntrySectionID: currentTrip.id,
                })
                toast.success('Trip event created')
                setCurrentEvent(tripEventData)
                const restrictedVisibilityData =
                    await restrictedVisibilityModel.save({
                        id: generateUniqueId(),
                        startLocationID: restrictedVisibility?.startLocationID,
                        crossingTime: crossingTime,
                        estSafeSpeed: restrictedVisibility?.estSafeSpeed,
                        stopAssessPlan: restrictedVisibility?.stopAssessPlan,
                        crewBriefing: restrictedVisibility?.crewBriefing,
                        navLights: restrictedVisibility?.navLights,
                        soundSignal: restrictedVisibility?.soundSignal,
                        lookout: restrictedVisibility?.lookout,
                        soundSignals: restrictedVisibility?.soundSignals,
                        radarWatch: restrictedVisibility?.radarWatch,
                        radioWatch: restrictedVisibility?.radioWatch,
                        endLocationID: restrictedVisibility?.endLocationID,
                        crossedTime: crossedTime,
                        approxSafeSpeed: restrictedVisibility?.approxSafeSpeed,
                        report: restrictedVisibility?.report,
                        startLat: currentStartLocation.latitude.toString(),
                        startLong: currentStartLocation.longitude.toString(),
                        endLat: currentEndLocation.latitude.toString(),
                        endLong: currentEndLocation.longitude.toString(),
                    })
                await tripEventModel.save({
                    id: tripEventData.id,
                    eventCategory: 'RestrictedVisibility',
                    eventType_RestrictedVisibilityID:
                        restrictedVisibilityData.id,
                })

                getCurrentEvent(tripEventData.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
                toast.success('Trip event updated')
                closeModal()
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'RestrictedVisibility',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            toast.success('Trip event created')
            const data = response.createTripEvent
            setCurrentEvent(data)
            createEventType_RestrictedVisibility({
                variables: {
                    input: {
                        startLocationID: restrictedVisibility?.startLocationID,
                        crossingTime: crossingTime,
                        estSafeSpeed: restrictedVisibility?.estSafeSpeed,
                        stopAssessPlan: restrictedVisibility?.stopAssessPlan,
                        crewBriefing: restrictedVisibility?.crewBriefing,
                        navLights: restrictedVisibility?.navLights,
                        soundSignal: restrictedVisibility?.soundSignal,
                        lookout: restrictedVisibility?.lookout,
                        soundSignals: restrictedVisibility?.soundSignals,
                        radarWatch: restrictedVisibility?.radarWatch,
                        radioWatch: restrictedVisibility?.radioWatch,
                        endLocationID: restrictedVisibility?.endLocationID,
                        crossedTime: crossedTime,
                        approxSafeSpeed: restrictedVisibility?.approxSafeSpeed,
                        report: restrictedVisibility?.report,
                        startLat: currentStartLocation.latitude.toString(),
                        startLong: currentStartLocation.longitude.toString(),
                        endLat: currentEndLocation.latitude.toString(),
                        endLong: currentEndLocation.longitude.toString(),
                        memberID: selectedAuthor?.value,
                    },
                },
            })
            updateTripEvent({
                variables: {
                    input: {
                        id: data.id,
                        eventCategory: 'RestrictedVisibility',
                        eventType_RestrictedVisibilityID: data.id,
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const [createEventType_RestrictedVisibility] = useMutation(
        CreateEventType_RestrictedVisibility,
        {
            onCompleted: (response) => {
                const data = response.createEventType_RestrictedVisibility
                updateTripEvent({
                    variables: {
                        input: {
                            id: currentEvent?.id,
                            eventType_RestrictedVisibilityID: data.id,
                        },
                    },
                })
                closeModal()
            },
            onError: (error) => {
                console.error('Error creating Person rescue', error)
            },
        },
    )

    const [updateEventType_RestrictedVisibility] = useMutation(
        UpdateEventType_RestrictedVisibility,
        {
            onCompleted: (response) => {
                const data = response.updateEventType_RestrictedVisibility
            },
            onError: (error) => {
                console.error('Error updating restricted visibility', error)
            },
        },
    )

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: (response) => {
            toast.success('Trip event updated')
            getCurrentEvent(currentEvent?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })

    const displayField = (fieldName: string) => {
        const eventTypesConfig =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )
        if (
            eventTypesConfig?.length > 0 &&
            eventTypesConfig[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const handleStartLocationChange = (value: any) => {
        setRestrictedVisibility({
            ...restrictedVisibility,
            startLocationID: value?.value,
        })
    }

    const handleEndLocationChange = (value: any) => {
        setRestrictedVisibility({
            ...restrictedVisibility,
            endLocationID: value?.value,
        })
    }
    const startLocationData = {
        geoLocationID:
            restrictedVisibility?.startLocationID > 0
                ? restrictedVisibility.startLocationID
                : tripEvent.eventType_RestrictedVisibility?.startLocationID,
        lat: tripEvent.eventType_RestrictedVisibility?.startLat,
        long: tripEvent.eventType_RestrictedVisibility?.startLong,
    }

    const endLocationData = {
        geoLocationID:
            restrictedVisibility?.endLocationID > 0
                ? restrictedVisibility.endLocationID
                : tripEvent.eventType_RestrictedVisibility?.endLocationID,
        lat: tripEvent.eventType_RestrictedVisibility?.endLat,
        long: tripEvent.eventType_RestrictedVisibility?.endLong,
    }

    useEffect(() => {
        if (members) {
            const crewMembers = members.map((member: any) => {
                return {
                    label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                    value: member.crewMemberID,
                }
            })
            setCrewMembers(crewMembers)
        }
    }, [members])

    const riskImpacts = [
        { value: 'Low', label: 'Low impact' },
        { value: 'Medium', label: 'Medium impact' },
        { value: 'High', label: 'High impact' },
        { value: 'Severe', label: 'Severe impact' },
    ]

    const handleSaveRisk = async () => {
        if (currentRisk.id > 0) {
            updateRiskFactor({
                variables: {
                    input: {
                        id: currentRisk.id,
                        type: 'RestrictedVisibility',
                        title: currentRisk.title,
                        impact: currentRisk?.impact
                            ? currentRisk?.impact
                            : 'Low',
                        probability: currentRisk?.probability
                            ? currentRisk?.probability
                            : 5,
                        mitigationStrategy:
                            currentStrategies.length > 0
                                ? currentStrategies
                                      .map((s: any) => s.id)
                                      .join(',')
                                : '',
                        eventType_RestrictedVisibilityID:
                            restrictedVisibility?.id,
                    },
                },
            })
        } else {
            createRiskFactor({
                variables: {
                    input: {
                        type: 'RestrictedVisibility',
                        title: currentRisk.title,
                        impact: currentRisk?.impact
                            ? currentRisk?.impact
                            : 'Low',
                        probability: currentRisk?.probability
                            ? currentRisk?.probability
                            : 5,
                        mitigationStrategy:
                            currentStrategies.length > 0
                                ? currentStrategies
                                      .map((s: any) => s.id)
                                      .join(',')
                                : '',
                        eventType_RestrictedVisibilityID:
                            restrictedVisibility?.id,
                        vesselID: vesselID,
                    },
                },
            })
        }
    }

    const [createRiskFactor] = useMutation(CreateRiskFactor, {
        onCompleted: (data) => {
            toast.dismiss()
            toast.success('Risk created')
            setOpenRiskDialog(false)
            getRiskFactors({
                variables: {
                    filter: { type: { eq: 'RestrictedVisibility' } },
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [updateRiskFactor] = useMutation(UpdateRiskFactor, {
        onCompleted: (data) => {
            setOpenRiskDialog(false)
            getRiskFactors({
                variables: {
                    filter: { type: { eq: 'RestrictedVisibility' } },
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const handleCreateRisk = (inputValue: any) => {
        setCurrentRisk({
            ...currentRisk,
            title: inputValue,
        })
        setRiskValue({ value: inputValue, label: inputValue })
        if (allRisks) {
            const risk = [...allRisks, { value: inputValue, label: inputValue }]
            setAllRisks(risk)
        } else {
            setAllRisks([{ value: inputValue, label: inputValue }])
        }
    }

    const handleRiskValue = (v: any) => {
        setCurrentRisk({
            ...currentRisk,
            title: v?.value,
        })
        setRiskValue({ value: v.value, label: v.value })
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.value &&
                    risk.mitigationStrategy.nodes?.length > 0,
            ).length > 0
        ) {
            setRecommendedStratagies(
                Array.from(
                    new Set(
                        allRiskFactors
                            ?.filter(
                                (r: any) =>
                                    r.title === v.value &&
                                    r.mitigationStrategy.nodes?.length > 0,
                            )
                            .map((r: any) => r.mitigationStrategy.nodes)[0]
                            .map((s: any) => ({
                                id: s.id,
                                strategy: s.strategy,
                            })),
                    ),
                ),
            )
        } else {
            setRecommendedStratagies(false)
        }
    }

    return (
        <div className="w-full">
            {displayField('RestrictedVisibility_CrossingTime') ||
            displayField('RestrictedVisibility_StartLocation') ||
            displayField('RestrictedVisibility_EstSafeSpeed') ? (
                <>
                    <p className={classes.helpText}>
                        For describing the conditions, actions taken and
                        duration of when there is limited visability
                    </p>
                    <div className="flex flex-col gap-4 my-4">
                        <div
                            className={`${locked ? 'pointer-events-none' : ''}`}>
                            <label
                                className={`${classes.label} ${locked ? 'pointer-events-none' : ''} !w-full`}>
                                Location where limited visibility starts
                            </label>
                            {displayField(
                                'RestrictedVisibility_StartLocation',
                            ) && (
                                <LocationField
                                    offline={offline}
                                    currentTrip={currentTrip}
                                    setCurrentLocation={setCurrentStartLocation}
                                    handleLocationChange={
                                        handleStartLocationChange
                                    }
                                    currentLocation={currentStartLocation}
                                    currentEvent={startLocationData}
                                />
                            )}
                        </div>
                        <div
                            className={`${locked ? 'pointer-events-none' : ''}`}>
                            <label
                                className={`${classes.label} ${locked ? 'pointer-events-none' : ''} !w-full`}>
                                Time where limited visibility starts
                            </label>
                            {displayField(
                                'RestrictedVisibility_CrossingTime',
                            ) && (
                                <>
                                    <TimeField
                                        time={crossingTime}
                                        handleTimeChange={
                                            handleCrossingTimeChange
                                        }
                                        timeID="crossingTime"
                                        fieldName="Time vis. restriction started"
                                    />
                                </>
                            )}
                        </div>
                        <div
                            className={`${locked ? 'pointer-events-none' : ''}`}>
                            <label
                                className={`${classes.label} ${locked ? 'pointer-events-none' : ''} !w-full`}>
                                Estimated safe speed for conditions
                            </label>
                            {displayField(
                                'RestrictedVisibility_EstSafeSpeed',
                            ) && (
                                <input
                                    id="estSafeSpeed"
                                    type="number"
                                    className={classes.input}
                                    placeholder="Enter safe speed for conditions"
                                    min={1}
                                    value={
                                        restrictedVisibility?.estSafeSpeed ??
                                        undefined
                                    }
                                    onChange={(e: any) => {
                                        setRestrictedVisibility({
                                            ...restrictedVisibility,
                                            estSafeSpeed: e.target.value,
                                        })
                                    }}
                                />
                            )}
                        </div>
                    </div>
                </>
            ) : null}
            {displayField('RestrictedVisibility_StopAssessPlan') ||
            displayField('RestrictedVisibility_CrewBriefing') ||
            displayField('RestrictedVisibility_NavLights') ||
            displayField('RestrictedVisibility_SoundSignal') ||
            displayField('RestrictedVisibility_Lookout') ||
            displayField('RestrictedVisibility_SoundSignals') ||
            displayField('RestrictedVisibility_RadarWatch') ||
            displayField('RestrictedVisibility_RadioWatch') ? (
                <>
                    <hr className="my-2" />
                    <div
                        className={`flex items-center my-4 w-full justify-between`}>
                        <label
                            className={` ${locked ? 'pointer-events-none' : ''} relative flex items-center pr-3 rounded-full cursor-pointer`}
                            htmlFor="task-log-dgr"
                            data-ripple="true"
                            data-ripple-color="dark"
                            data-ripple-dark="true">
                            <input
                                type="checkbox"
                                id="task-log-dgr"
                                className="before:content[''] peer relative h-5 w-5 cursor-pointer p-3 appearance-none rounded-full border border-sky-400 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-sky-500 before:opacity-0 before:transition-opacity checked:border-sky-700 checked:bg-sky-700 before:bg-sky-700 hover:before:opacity-10"
                                checked={displaySOP}
                                onChange={(e: any) => {
                                    handleSetDisplaySOP(e.target.checked)
                                }}
                            />
                            <span className="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-1/3 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100"></span>
                            <span className="ml-3 text-sm font-semibold uppercase">
                                Safe operating procedures checklist
                            </span>
                        </label>
                        {displaySOP && (
                            <div>
                                <SeaLogsButton
                                    text="Safe operating procedures checklist"
                                    color="orange"
                                    type="primaryWithColor"
                                    action={() =>
                                        setOpenProcedureChecklist(true)
                                    }
                                />
                            </div>
                        )}
                    </div>
                </>
            ) : null}
            <hr className="my-4" />
            {displayField('RestrictedVisibility_EndLocation') ||
            displayField('RestrictedVisibility_CrossedTime') ||
            displayField('RestrictedVisibility_ApproxSafeSpeed') ||
            displayField('RestrictedVisibility_Report') ? (
                <div className="flex flex-col">
                    <div
                        className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                        <label
                            className={`${classes.label} ${locked ? 'pointer-events-none' : ''} !w-full`}>
                            Location where limited visibility ends
                        </label>
                        {displayField('RestrictedVisibility_EndLocation') && (
                            <LocationField
                                offline={offline}
                                // geoLocations={geoLocations}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                tripReport={tripReport}
                                setCurrentLocation={setCurrentEndLocation}
                                handleLocationChange={handleEndLocationChange}
                                currentLocation={currentEndLocation}
                                currentEvent={endLocationData}
                            />
                        )}
                    </div>
                    <div
                        className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                        <label
                            className={`${classes.label} ${locked ? 'pointer-events-none' : ''} !w-full`}>
                            Time when limiited visibility ends
                        </label>
                        {displayField('RestrictedVisibility_CrossedTime') && (
                            <TimeField
                                time={crossedTime}
                                handleTimeChange={handleCrossedTimeChange}
                                timeID="crossedTime"
                                fieldName="End time"
                            />
                        )}
                    </div>
                    <div
                        className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                        <label
                            className={`${classes.label} ${locked ? 'pointer-events-none' : ''} !w-full`}>
                            Approximate average speed during restricted
                            visibility period
                        </label>
                        {displayField(
                            'RestrictedVisibility_ApproxSafeSpeed',
                        ) && (
                            <input
                                id="approxSafeSpeed"
                                type="number"
                                className={classes.input}
                                placeholder="Enter approximate average speed"
                                min={1}
                                value={
                                    restrictedVisibility?.approxSafeSpeed ??
                                    undefined
                                }
                                onChange={(e: any) => {
                                    setRestrictedVisibility({
                                        ...restrictedVisibility,
                                        approxSafeSpeed: e.target.value,
                                    })
                                }}
                            />
                        )}
                    </div>
                    <div
                        className={`${locked ? 'pointer-events-none' : ''} my-4 w-full`}>
                        {displayField('RestrictedVisibility_Report') && (
                            <textarea
                                id="restricted-visibility-report"
                                className={classes.textarea}
                                rows={4}
                                placeholder="Add any comments or observations pertinant to the limited visibility event"
                                value={
                                    restrictedVisibility?.report ?? undefined
                                }
                                onChange={(e: any) => {
                                    setRestrictedVisibility({
                                        ...restrictedVisibility,
                                        report: e.target.value,
                                    })
                                }}
                            />
                        )}
                    </div>
                    <hr className="my-2" />
                    <div className="flex justify-end px-4 pb-4 pt-4">
                        <SeaLogsButton
                            action={closeModal}
                            type="text"
                            text="Cancel"
                        />
                        <SeaLogsButton
                            type="primary"
                            icon="check"
                            text={selectedEvent ? 'Update' : 'Save'}
                            color="slblue"
                            action={locked ? () => {} : handleSave}
                        />
                    </div>
                </div>
            ) : null}
            <Toaster position="top-right" />
            <SlidingPanel
                type={'right'}
                isOpen={openProcedureChecklist}
                size={60}>
                <div className="h-[calc(100vh_-_1rem)] pt-4">
                    <div className="bg-orange-100 h-full flex flex-col justify-between rounded-l-lg">
                        <div className="text-xl  text-white items-center flex justify-between font-medium py-4 px-6 rounded-tl-lg bg-orange-400">
                            <div>Safe operating procedures checklist</div>
                            <XMarkIcon
                                className="w-6 h-6"
                                onClick={() => setOpenProcedureChecklist(false)}
                            />
                        </div>
                        {restrictedVisibility?.id > 0 ? (
                            <div
                                className={`${locked ? 'pointer-events-none' : ''} p-4 flex-grow overflow-scroll`}>
                                <div
                                    className={`${locked ? 'pointer-events-none' : ''} grid grid-cols-3 gap-6 pb-4 pt-3 px-4`}>
                                    <div className="">
                                        <div className="">
                                            {displayField(
                                                'RestrictedVisibility_StopAssessPlan',
                                            ) && (
                                                <div className="my-4 flex flex-row items-center gap-4">
                                                    <label
                                                        className="relative flex items-center rounded-full cursor-pointer"
                                                        htmlFor="stopAssessPlan-onChangeComplete"
                                                        data-ripple="true"
                                                        data-ripple-color="dark"
                                                        data-ripple-dark="true">
                                                        <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                            {restrictedVisibility?.stopAssessPlan ? (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-ok-check.svg"
                                                                    alt=""
                                                                />
                                                            ) : (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-empty-check.svg"
                                                                    alt=""
                                                                />
                                                            )}
                                                        </div>
                                                        <input
                                                            type="checkbox"
                                                            id="stopAssessPlan-onChangeComplete"
                                                            className={
                                                                classes.radioInput +
                                                                ' ' +
                                                                'hidden'
                                                            }
                                                            checked={
                                                                restrictedVisibility?.stopAssessPlan
                                                            }
                                                            onChange={(
                                                                e: any,
                                                            ) => {
                                                                setRestrictedVisibility(
                                                                    {
                                                                        ...restrictedVisibility,
                                                                        stopAssessPlan:
                                                                            e
                                                                                .target
                                                                                .checked,
                                                                    },
                                                                )
                                                            }}
                                                        />
                                                    </label>
                                                    <div>
                                                        Stopped, assessed,
                                                        planned
                                                    </div>
                                                </div>
                                            )}
                                            {displayField(
                                                'RestrictedVisibility_CrewBriefing',
                                            ) && (
                                                <div className="my-4 flex flex-row items-center gap-4">
                                                    <label
                                                        className="relative flex items-center rounded-full cursor-pointer"
                                                        htmlFor="crewBriefing-onChangeComplete"
                                                        data-ripple="true"
                                                        data-ripple-color="dark"
                                                        data-ripple-dark="true">
                                                        <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                            {restrictedVisibility?.crewBriefing ? (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-ok-check.svg"
                                                                    alt=""
                                                                />
                                                            ) : (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-empty-check.svg"
                                                                    alt=""
                                                                />
                                                            )}
                                                        </div>
                                                        <input
                                                            type="checkbox"
                                                            id="crewBriefing-onChangeComplete"
                                                            className={
                                                                classes.radioInput +
                                                                ' ' +
                                                                'hidden'
                                                            }
                                                            checked={
                                                                restrictedVisibility?.crewBriefing
                                                            }
                                                            onChange={(
                                                                e: any,
                                                            ) => {
                                                                setRestrictedVisibility(
                                                                    {
                                                                        ...restrictedVisibility,
                                                                        crewBriefing:
                                                                            e
                                                                                .target
                                                                                .checked,
                                                                    },
                                                                )
                                                            }}
                                                        />
                                                    </label>
                                                    <span>Briefed crew</span>
                                                </div>
                                            )}
                                            {displayField(
                                                'RestrictedVisibility_NavLights',
                                            ) && (
                                                <div className="my-4 flex flex-row items-center gap-4">
                                                    <label
                                                        className="relative flex items-center rounded-full cursor-pointer"
                                                        htmlFor="navLights-onChangeComplete"
                                                        data-ripple="true"
                                                        data-ripple-color="dark"
                                                        data-ripple-dark="true">
                                                        <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                            {restrictedVisibility?.navLights ? (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-ok-check.svg"
                                                                    alt=""
                                                                />
                                                            ) : (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-empty-check.svg"
                                                                    alt=""
                                                                />
                                                            )}
                                                        </div>
                                                        <input
                                                            type="checkbox"
                                                            id="navLights-onChangeComplete"
                                                            className={
                                                                classes.radioInput +
                                                                ' ' +
                                                                'hidden'
                                                            }
                                                            checked={
                                                                restrictedVisibility?.navLights
                                                            }
                                                            onChange={(
                                                                e: any,
                                                            ) => {
                                                                setRestrictedVisibility(
                                                                    {
                                                                        ...restrictedVisibility,
                                                                        navLights:
                                                                            e
                                                                                .target
                                                                                .checked,
                                                                    },
                                                                )
                                                            }}
                                                        />
                                                    </label>
                                                    <span>
                                                        Navigation lights on
                                                    </span>
                                                </div>
                                            )}
                                            <hr className="my-4" />
                                            {displayField(
                                                'RestrictedVisibility_SoundSignal',
                                            ) && (
                                                <div className="pl-4">
                                                    <label
                                                        className={
                                                            classes.label
                                                        }>
                                                        Sounds signals used
                                                        (pick one)
                                                    </label>
                                                    <div className="flex flex-col">
                                                        <div className="my-2 flex flex-row items-center gap-4">
                                                            <label
                                                                className="relative flex items-center rounded-full cursor-pointer"
                                                                htmlFor="soundSignalNone-onChangeComplete"
                                                                data-ripple="true"
                                                                data-ripple-color="dark"
                                                                data-ripple-dark="true">
                                                                <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                                    {restrictedVisibility?.soundSignal ==
                                                                    'None' ? (
                                                                        <img
                                                                            className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                            src="/sealogs-ok-check.svg"
                                                                            alt=""
                                                                        />
                                                                    ) : (
                                                                        <img
                                                                            className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                            src="/sealogs-empty-check.svg"
                                                                            alt=""
                                                                        />
                                                                    )}
                                                                </div>
                                                                <input
                                                                    type="checkbox"
                                                                    id="soundSignalNone-onChangeComplete"
                                                                    className={
                                                                        classes.radioInput +
                                                                        ' ' +
                                                                        'hidden'
                                                                    }
                                                                    checked={
                                                                        restrictedVisibility?.soundSignal ==
                                                                        'None'
                                                                    }
                                                                    onChange={(
                                                                        e: any,
                                                                    ) => {
                                                                        setRestrictedVisibility(
                                                                            e
                                                                                .target
                                                                                .checked && {
                                                                                ...restrictedVisibility,
                                                                                soundSignal:
                                                                                    'None',
                                                                            },
                                                                        )
                                                                    }}
                                                                />
                                                            </label>
                                                            <span>
                                                                None needed
                                                            </span>
                                                        </div>
                                                        <div className="my-2 flex flex-row items-center gap-4">
                                                            <label
                                                                className="relative flex items-center rounded-full cursor-pointer"
                                                                htmlFor="soundSignalMakingWay-onChangeComplete"
                                                                data-ripple="true"
                                                                data-ripple-color="dark"
                                                                data-ripple-dark="true">
                                                                <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                                    {restrictedVisibility?.soundSignal ==
                                                                    'MakingWay' ? (
                                                                        <img
                                                                            className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                            src="/sealogs-ok-check.svg"
                                                                            alt=""
                                                                        />
                                                                    ) : (
                                                                        <img
                                                                            className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                            src="/sealogs-empty-check.svg"
                                                                            alt=""
                                                                        />
                                                                    )}
                                                                </div>
                                                                <input
                                                                    type="checkbox"
                                                                    id="soundSignalMakingWay-onChangeComplete"
                                                                    className={
                                                                        classes.radioInput +
                                                                        ' ' +
                                                                        'hidden'
                                                                    }
                                                                    checked={
                                                                        restrictedVisibility?.soundSignal ==
                                                                        'MakingWay'
                                                                    }
                                                                    onChange={(
                                                                        e: any,
                                                                    ) => {
                                                                        setRestrictedVisibility(
                                                                            e
                                                                                .target
                                                                                .checked && {
                                                                                ...restrictedVisibility,
                                                                                soundSignal:
                                                                                    'MakingWay',
                                                                            },
                                                                        )
                                                                    }}
                                                                />
                                                            </label>
                                                            <span>
                                                                Making way (1
                                                                long / 2 mins)
                                                            </span>
                                                        </div>
                                                        <div className="my-2 flex flex-row items-center gap-4">
                                                            <label
                                                                className="relative flex items-center rounded-full cursor-pointer"
                                                                htmlFor="soundSignalNotMakingWay-onChangeComplete"
                                                                data-ripple="true"
                                                                data-ripple-color="dark"
                                                                data-ripple-dark="true">
                                                                <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                                    {restrictedVisibility?.soundSignal ==
                                                                    'NotMakingWay' ? (
                                                                        <img
                                                                            className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                            src="/sealogs-ok-check.svg"
                                                                            alt=""
                                                                        />
                                                                    ) : (
                                                                        <img
                                                                            className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                            src="/sealogs-empty-check.svg"
                                                                            alt=""
                                                                        />
                                                                    )}
                                                                </div>
                                                                <input
                                                                    type="checkbox"
                                                                    id="soundSignalNotMakingWay-onChangeComplete"
                                                                    className={
                                                                        classes.radioInput +
                                                                        ' ' +
                                                                        'hidden'
                                                                    }
                                                                    checked={
                                                                        restrictedVisibility?.soundSignal ==
                                                                        'NotMakingWay'
                                                                    }
                                                                    onChange={(
                                                                        e: any,
                                                                    ) => {
                                                                        setRestrictedVisibility(
                                                                            e
                                                                                .target
                                                                                .checked && {
                                                                                ...restrictedVisibility,
                                                                                soundSignal:
                                                                                    'NotMakingWay',
                                                                            },
                                                                        )
                                                                    }}
                                                                />
                                                            </label>
                                                            <span>
                                                                Not making way
                                                                (2 long / 2
                                                                mins)
                                                            </span>
                                                        </div>
                                                        <div className="my-2 flex flex-row items-center gap-4">
                                                            <label
                                                                className="relative flex items-center rounded-full cursor-pointer"
                                                                htmlFor="soundSignalTowing-onChangeComplete"
                                                                data-ripple="true"
                                                                data-ripple-color="dark"
                                                                data-ripple-dark="true">
                                                                <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                                    {restrictedVisibility?.soundSignal ==
                                                                    'Towing' ? (
                                                                        <img
                                                                            className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                            src="/sealogs-ok-check.svg"
                                                                            alt=""
                                                                        />
                                                                    ) : (
                                                                        <img
                                                                            className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                            src="/sealogs-empty-check.svg"
                                                                            alt=""
                                                                        />
                                                                    )}
                                                                </div>
                                                                <input
                                                                    type="checkbox"
                                                                    id="soundSignalTowing-onChangeComplete"
                                                                    className={
                                                                        classes.radioInput +
                                                                        ' ' +
                                                                        'hidden'
                                                                    }
                                                                    checked={
                                                                        restrictedVisibility?.soundSignal ==
                                                                        'Towing'
                                                                    }
                                                                    onChange={(
                                                                        e: any,
                                                                    ) => {
                                                                        setRestrictedVisibility(
                                                                            e
                                                                                .target
                                                                                .checked && {
                                                                                ...restrictedVisibility,
                                                                                soundSignal:
                                                                                    'Towing',
                                                                            },
                                                                        )
                                                                    }}
                                                                />
                                                            </label>
                                                            <span>
                                                                Towing (1 long +
                                                                2 short / 2
                                                                mins)
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                            <hr className="my-4" />
                                            {displayField(
                                                'RestrictedVisibility_Lookout',
                                            ) && (
                                                <div className="my-4 flex flex-row items-center gap-4">
                                                    <label
                                                        className="relative flex items-center pr-3 rounded-full cursor-pointer"
                                                        htmlFor="lookout-onChangeComplete"
                                                        data-ripple="true"
                                                        data-ripple-color="dark"
                                                        data-ripple-dark="true">
                                                        <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                            {restrictedVisibility?.lookout ? (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-ok-check.svg"
                                                                    alt=""
                                                                />
                                                            ) : (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-empty-check.svg"
                                                                    alt=""
                                                                />
                                                            )}
                                                        </div>
                                                        <input
                                                            type="checkbox"
                                                            id="lookout-onChangeComplete"
                                                            className={
                                                                classes.radioInput +
                                                                ' ' +
                                                                'hidden'
                                                            }
                                                            checked={
                                                                restrictedVisibility?.lookout
                                                            }
                                                            onChange={(
                                                                e: any,
                                                            ) => {
                                                                setRestrictedVisibility(
                                                                    {
                                                                        ...restrictedVisibility,
                                                                        lookout:
                                                                            e
                                                                                .target
                                                                                .checked,
                                                                    },
                                                                )
                                                            }}
                                                        />
                                                    </label>
                                                    <span>
                                                        Set proper lookout
                                                    </span>
                                                </div>
                                            )}
                                            {displayField(
                                                'RestrictedVisibility_SoundSignals',
                                            ) && (
                                                <div className="my-4 flex flex-row items-center gap-4">
                                                    <label
                                                        className="relative flex items-center pr-3 rounded-full cursor-pointer"
                                                        htmlFor="soundSignals-onChangeComplete"
                                                        data-ripple="true"
                                                        data-ripple-color="dark"
                                                        data-ripple-dark="true">
                                                        <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                            {restrictedVisibility?.soundSignals ? (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-ok-check.svg"
                                                                    alt=""
                                                                />
                                                            ) : (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-empty-check.svg"
                                                                    alt=""
                                                                />
                                                            )}
                                                        </div>
                                                        <input
                                                            type="checkbox"
                                                            id="soundSignals-onChangeComplete"
                                                            className={
                                                                classes.radioInput +
                                                                ' ' +
                                                                'hidden'
                                                            }
                                                            checked={
                                                                restrictedVisibility?.soundSignals
                                                            }
                                                            onChange={(
                                                                e: any,
                                                            ) => {
                                                                setRestrictedVisibility(
                                                                    {
                                                                        ...restrictedVisibility,
                                                                        soundSignals:
                                                                            e
                                                                                .target
                                                                                .checked,
                                                                    },
                                                                )
                                                            }}
                                                        />
                                                    </label>
                                                    <span>
                                                        Listening for other
                                                        sound signals
                                                    </span>
                                                </div>
                                            )}
                                            {displayField(
                                                'RestrictedVisibility_RadarWatch',
                                            ) && (
                                                <div className="my-4 flex flex-row items-center gap-4">
                                                    <label
                                                        className="relative flex items-center pr-3 rounded-full cursor-pointer"
                                                        htmlFor="radarWatch-onChangeComplete"
                                                        data-ripple="true"
                                                        data-ripple-color="dark"
                                                        data-ripple-dark="true">
                                                        <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                            {restrictedVisibility?.radarWatch ? (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-ok-check.svg"
                                                                    alt=""
                                                                />
                                                            ) : (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-empty-check.svg"
                                                                    alt=""
                                                                />
                                                            )}
                                                        </div>
                                                        <input
                                                            type="checkbox"
                                                            id="radarWatch-onChangeComplete"
                                                            className={
                                                                classes.radioInput +
                                                                ' ' +
                                                                'hidden'
                                                            }
                                                            checked={
                                                                restrictedVisibility?.radarWatch
                                                            }
                                                            onChange={(
                                                                e: any,
                                                            ) => {
                                                                setRestrictedVisibility(
                                                                    {
                                                                        ...restrictedVisibility,
                                                                        radarWatch:
                                                                            e
                                                                                .target
                                                                                .checked,
                                                                    },
                                                                )
                                                            }}
                                                        />
                                                    </label>
                                                    <span>Radar watch on</span>
                                                </div>
                                            )}
                                            {displayField(
                                                'RestrictedVisibility_RadioWatch',
                                            ) && (
                                                <div className="my-4 flex flex-row items-center gap-4">
                                                    <label
                                                        className="relative flex items-center pr-3 rounded-full cursor-pointer"
                                                        htmlFor="radioWatch-onChangeComplete"
                                                        data-ripple="true"
                                                        data-ripple-color="dark"
                                                        data-ripple-dark="true">
                                                        <div className="md:w-8 md:h-8 w-6 h-6 flex-shrink-0">
                                                            {restrictedVisibility?.radioWatch ? (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-ok-check.svg"
                                                                    alt=""
                                                                />
                                                            ) : (
                                                                <img
                                                                    className="bg-orange-50 ring-1 ring-orange-200 p-0.5 rounded-full"
                                                                    src="/sealogs-empty-check.svg"
                                                                    alt=""
                                                                />
                                                            )}
                                                        </div>
                                                        <input
                                                            type="checkbox"
                                                            id="radioWatch-onChangeComplete"
                                                            className={
                                                                classes.radioInput +
                                                                ' ' +
                                                                'hidden'
                                                            }
                                                            checked={
                                                                restrictedVisibility?.radioWatch
                                                            }
                                                            onChange={(
                                                                e: any,
                                                            ) => {
                                                                setRestrictedVisibility(
                                                                    {
                                                                        ...restrictedVisibility,
                                                                        radioWatch:
                                                                            e
                                                                                .target
                                                                                .checked,
                                                                    },
                                                                )
                                                            }}
                                                        />
                                                    </label>
                                                    <span>Radio watch on</span>
                                                </div>
                                            )}
                                        </div>
                                        <div className="flex items-center">
                                            {members && (
                                                <Select
                                                    id="author"
                                                    options={crewMembers}
                                                    menuPlacement="top"
                                                    placeholder="Author"
                                                    value={selectedAuthor}
                                                    className={
                                                        classes.selectMain
                                                    }
                                                    onChange={(value: any) => {
                                                        setSelectedAuthor(value)
                                                        // updateRiskAnalysisMember(
                                                        //     value?.value,
                                                        // )
                                                    }}
                                                    classNames={{
                                                        control: () =>
                                                            'flex py-0.5 w-full !text-sm !text-orange-900 !bg-orange-50 !rounded-lg !border !border-orange-200 focus:ring-orange-500 focus:border-orange-500',
                                                        singleValue: () => '',
                                                        menu: () => '',
                                                    }}
                                                />
                                            )}
                                        </div>
                                    </div>
                                    <div className="col-span-3 md:col-span-2">
                                        <div
                                            className={`mt-4 bg-orange-50 border-orange-300 border rounded-lg p-4`}>
                                            <Heading className="text-lg font-semibold leading-6 mb-4 text-gray-700 ">
                                                Risk Analysis
                                            </Heading>
                                            <div>
                                                {riskFactors?.length > 0 && (
                                                    <ListBox
                                                        aria-label="RiskAnalysis"
                                                        className={`mb-4`}>
                                                        {riskFactors.map(
                                                            (risk: any) => (
                                                                <ListBoxItem
                                                                    key={`${risk.id}-riskAnalysis`}
                                                                    textValue={
                                                                        risk.title
                                                                    }
                                                                    className="flex items-center justify-between mb-4 text-sm  ">
                                                                    <label
                                                                        className="relative inline-flex items-center pr-3 rounded-full cursor-pointer"
                                                                        htmlFor={
                                                                            risk.id
                                                                        }
                                                                        data-ripple="true"
                                                                        data-ripple-color="dark"
                                                                        data-ripple-dark="true"
                                                                        onClick={() => {
                                                                            handleSetRiskValue(
                                                                                risk,
                                                                            )
                                                                            setCurrentRisk(
                                                                                risk,
                                                                            )
                                                                            setOpenRiskDialog(
                                                                                true,
                                                                            )
                                                                        }}>
                                                                        <span
                                                                            className={`text-sm`}>
                                                                            {
                                                                                risk.title
                                                                            }
                                                                            {risk?.impact
                                                                                ? ' - ' +
                                                                                  risk.impact
                                                                                : ''}
                                                                            {risk?.probability
                                                                                ? ' - ' +
                                                                                  risk.probability +
                                                                                  '/10'
                                                                                : ''}
                                                                        </span>
                                                                    </label>
                                                                    <div className="flex items-center">
                                                                        <DialogTrigger>
                                                                            <SeaLogsButton
                                                                                icon="alert"
                                                                                className="w-6 h-6 sup -mt-2 ml-0.5"
                                                                            />
                                                                            <Popover>
                                                                                <PopoverWrapper>
                                                                                    {risk
                                                                                        ?.mitigationStrategy
                                                                                        ?.nodes
                                                                                        ?.length >
                                                                                        0 &&
                                                                                        risk?.mitigationStrategy?.nodes.map(
                                                                                            (
                                                                                                s: any,
                                                                                            ) => (
                                                                                                <div
                                                                                                    key={
                                                                                                        s.id
                                                                                                    }>
                                                                                                    <div
                                                                                                        dangerouslySetInnerHTML={{
                                                                                                            __html: s.strategy,
                                                                                                        }}></div>
                                                                                                </div>
                                                                                            ),
                                                                                        )}
                                                                                </PopoverWrapper>
                                                                            </Popover>
                                                                        </DialogTrigger>
                                                                        <Button
                                                                            className="text-base outline-none px-1"
                                                                            onPress={() => {
                                                                                setOpenDeleteConfirmation(
                                                                                    true,
                                                                                ),
                                                                                    setRiskToDelete(
                                                                                        risk,
                                                                                    )
                                                                            }}>
                                                                            <TrashIcon className="w-5 h-5 text-emerald-800 " />
                                                                        </Button>
                                                                    </div>
                                                                </ListBoxItem>
                                                            ),
                                                        )}
                                                    </ListBox>
                                                )}
                                            </div>
                                            <div>
                                                <SeaLogsButton
                                                    text="Add Risk"
                                                    type="text"
                                                    icon="plus"
                                                    action={() => {
                                                        setCurrentRisk({}),
                                                            setContent('')
                                                        setRiskValue(null),
                                                            setOpenRiskDialog(
                                                                true,
                                                            )
                                                    }}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <SeaLogsButton
                                    text="Save"
                                    type="primary"
                                    color="sky"
                                    icon="check"
                                    action={() =>
                                        setOpenProcedureChecklist(false)
                                    }
                                />
                            </div>
                        ) : (
                            <div
                                className={`${locked ? 'pointer-events-none' : ''} p-4 flex-grow overflow-scroll`}>
                                <div className="flex items-center justify-center">
                                    <div>
                                        Please save the Restricted visibility
                                        before adding Safe operating procedures
                                        checklist
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </SlidingPanel>
            <AlertDialog
                openDialog={openRiskDialog}
                setOpenDialog={setOpenRiskDialog}
                handleCreate={handleSaveRisk}
                actionText={currentRisk?.id > 0 ? 'Update' : 'Create Risk'}>
                <Heading
                    slot="title"
                    className="text-2xl font-light leading-6 my-2 mb-4 text-gray-700 ">
                    {currentRisk?.id > 0 ? 'Update Risk' : 'Create New Risk'}
                </Heading>
                <div className="my-2 flex items-center">
                    {allRisks && (
                        <Creatable
                            id="impact"
                            options={allRisks}
                            menuPlacement="top"
                            placeholder="Risk"
                            value={riskValue}
                            className={classes.selectMain}
                            onCreateOption={handleCreateRisk}
                            onChange={handleRiskValue}
                            classNames={{
                                control: () =>
                                    'flex py-0.5 w-full !text-sm !text-gray-900 !bg-transparent !rounded-lg !border !border-gray-200 focus:ring-blue-500 focus:border-blue-500   ',
                                singleValue: () => '',
                                menu: () => '',
                            }}
                        />
                    )}
                </div>
                <div className="my-2 flex items-center">
                    <Select
                        id="impact"
                        options={riskImpacts}
                        menuPlacement="top"
                        placeholder="Risk impact"
                        defaultValue={
                            currentRisk?.impact
                                ? riskImpacts?.find(
                                      (impact: any) =>
                                          impact.value == currentRisk?.impact,
                                  )
                                : null
                        }
                        className={classes.selectMain}
                        onChange={(value: any) =>
                            setCurrentRisk({
                                ...currentRisk,
                                impact: value?.value,
                            })
                        }
                        classNames={{
                            control: () =>
                                'flex py-0.5 w-full !text-sm !text-gray-900 !bg-transparent !rounded-lg !border !border-gray-200 focus:ring-blue-500 focus:border-blue-500   ',
                            singleValue: () => '',
                            menu: () => '',
                        }}
                    />
                </div>
                <div className="my-1">
                    <div>Risk probability</div>
                    <Slider
                        defaultValue={
                            currentRisk?.probability
                                ? currentRisk?.probability
                                : 5
                        }
                        valueLabelDisplay="auto"
                        getAriaValueText={(value: number) => `${value}/10`}
                        step={1}
                        min={0}
                        max={10}
                        style={{ color: '#ff9900', height: 8 }}
                        onChange={(event: any, value: any) =>
                            setCurrentRisk({
                                ...currentRisk,
                                probability: value,
                            })
                        }
                    />
                </div>
                <div className="flex items-center">
                    <div className="flex gap-4 flex-col">
                        <Heading className="text-lg font-semibold leading-6 text-gray-700 ">
                            Mitigation strategy
                        </Heading>
                        {currentRisk?.mitigationStrategy?.nodes?.length > 0 &&
                            currentRisk?.mitigationStrategy?.nodes.map(
                                (s: any) => (
                                    <div key={s.id}>
                                        <div
                                            dangerouslySetInnerHTML={{
                                                __html: s.strategy,
                                            }}></div>
                                    </div>
                                ),
                            )}
                        <div
                            dangerouslySetInnerHTML={{
                                __html: content,
                            }}></div>
                        <SeaLogsButton
                            text="Add strategy"
                            color="orange"
                            type="primaryWithColor"
                            action={() => setOpenRecommendedstrategy(true)}
                        />
                    </div>
                </div>
            </AlertDialog>
            <AlertDialog
                openDialog={openRecommendedstrategy}
                setOpenDialog={setOpenRecommendedstrategy}
                handleCreate={handleNewStrategy}
                noCancel
                actionText="Save">
                <Heading
                    slot="title"
                    className="text-2xl font-light leading-6 my-2 mb-4 text-gray-700 ">
                    Recommended strategy
                </Heading>
                <div className="my-2 flex items-center gap-4 flex-wrap">
                    {recommendedStratagies ? (
                        <>
                            {recommendedStratagies?.map((risk: any) => (
                                <Button
                                    key={risk.id}
                                    onPress={() => {
                                        handleSetCurrentStrategies(risk)
                                        setCurrentRisk({
                                            ...currentRisk,
                                            mitigationStrategy: risk,
                                        })
                                    }}
                                    className={`${currentStrategies?.find((s: any) => s.id === risk.id) ? 'border-orange-400 bg-orange-50' : 'border-gray-400 bg-gray-50'} border p-4 rounded-lg cursor-pointer`}>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: risk?.strategy,
                                        }}></div>
                                </Button>
                            ))}
                            <Heading className="text-lg font-normal leading-6 text-gray-700 ">
                                or add new Mitigation strategy
                            </Heading>
                        </>
                    ) : (
                        <>
                            <div>No recommendations available!</div>
                            <Heading className="text-lg font-normal leading-6 mb-4 text-gray-700 ">
                                Create a new strategy instead
                            </Heading>
                        </>
                    )}
                </div>
                <div className="flex items-center">
                    <Editor
                        id="strategy"
                        placeholder="Mitigation strategy"
                        className="w-full"
                        content={content}
                        handleEditorChange={handleEditorChange}
                    />
                </div>
            </AlertDialog>
            <AlertDialog
                openDialog={openDeleteConfirmation}
                setOpenDialog={setOpenDeleteConfirmation}
                handleCreate={handleDeleteRisk}
                actionText="Delete">
                <Heading
                    slot="title"
                    className="text-2xl font-light leading-6 my-2 mb-4 text-gray-700 ">
                    Delete risk analysis!
                </Heading>
            </AlertDialog>
        </div>
    )
}

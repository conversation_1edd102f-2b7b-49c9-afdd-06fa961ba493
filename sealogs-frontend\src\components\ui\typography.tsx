import { cn } from '@/app/lib/utils'
import React, { HTMLAttributes, forwardRef } from 'react'

export const H1 = forwardRef<
    HTMLHeadingElement,
    HTMLAttributes<HTMLHeadingElement> & {
        children: React.ReactNode
        className?: string
    }
>(({ children, className, ...props }, ref) => {
    return (
        <h1
            ref={ref}
            className={cn(
                'scroll-m-20 text-4xl leading-[36px] my-0 font-extrabold',
                className,
            )}
            {...props}>
            {children}
        </h1>
    )
})
H1.displayName = 'H1'

export const H2 = forwardRef<
    HTMLHeadingElement,
    HTMLAttributes<HTMLHeadingElement> & {
        children: React.ReactNode
        className?: string
    }
>(({ children, className, ...props }, ref) => {
    return (
        <h2
            ref={ref}
            className={cn(
                'scroll-m-20 text-4xl leading-11 text-foreground tracking-[-0.09px] my-0 font-bold',
                className,
            )}
            {...props}>
            {children}
        </h2>
    )
})
H2.displayName = 'H2'

export const H3 = forwardRef<
    HTMLHeadingElement,
    HTMLAttributes<HTMLHeadingElement> & {
        children: React.ReactNode
        className?: string
    }
>(({ children, className, ...props }, ref) => {
    return (
        <h3
            ref={ref}
            className={cn(
                'scroll-m-20 text-2xl font-semibold text-foreground tracking-tight',
                className,
            )}
            {...props}>
            {children}
        </h3>
    )
})
H3.displayName = 'H3'

export const H4 = forwardRef<
    HTMLHeadingElement,
    HTMLAttributes<HTMLHeadingElement> & {
        children: React.ReactNode
        className?: string
    }
>(({ children, className, ...props }, ref) => {
    return (
        <h4
            ref={ref}
            className={cn(
                'scroll-m-20 text-xl font-semibold text-outer-space-500 tracking-tight',
                className,
            )}
            {...props}>
            {children}
        </h4>
    )
})
H4.displayName = 'H4'

export const H5 = forwardRef<
    HTMLHeadingElement,
    HTMLAttributes<HTMLHeadingElement> & {
        children: React.ReactNode
        className?: string
    }
>(({ children, className, ...props }, ref) => {
    return (
        <h5
            ref={ref}
            className={cn(
                'scroll-m-20 text-lg font-semibold text-outer-space-500 tracking-tight',
                className,
            )}
            {...props}>
            {children}
        </h5>
    )
})
H5.displayName = 'H5'

export const P = forwardRef<
    HTMLParagraphElement,
    HTMLAttributes<HTMLParagraphElement> & {
        children: React.ReactNode
        className?: string
    }
>(({ children, className, ...props }, ref) => {
    return (
        <p ref={ref} className={cn(' text-outer-space-500', className)} {...props}>
            {children}
        </p>
    )
})
P.displayName = 'P'

import * as React from 'react'
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group'
import { Circle } from 'lucide-react'
import { cva, type VariantProps } from 'class-variance-authority'
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from './tooltip'

import { cn } from '@/app/lib/utils'

const radioGroupVariants = cva('grid gap-2', {
    variants: {
        layout: {
            horizontal: 'flex flex-row',
            vertical: 'flex flex-col gap-2',
        },
        gap: {
            default: '',
            none: 'gap-0',
            large: 'gap-4',
        },
    },
    defaultVariants: {
        layout: 'vertical',
        gap: 'default',
    },
})

const RadioGroup = React.forwardRef<
    React.ElementRef<typeof RadioGroupPrimitive.Root>,
    React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root> &
        VariantProps<typeof radioGroupVariants> & {
            variant?: 'horizontal' | 'vertical'
            size?: 'sm' | 'default' | 'md' | 'lg'
        }
>(({ className, variant, layout, gap, ...props }, ref) => {
    // For backward compatibility
    const layoutValue =
        variant === 'horizontal'
            ? 'horizontal'
            : variant === 'vertical'
              ? 'vertical'
              : layout

    return (
        <RadioGroupPrimitive.Root
            className={cn(
                radioGroupVariants({ layout: layoutValue, gap }),
                className,
            )}
            {...props}
            ref={ref}
        />
    )
})
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName

const radioGroupItemVariants = cva(
    'aspect-square rounded-full group flex items-center justify-center border border-border relative shadow-[0_2px_0_#FFFFFF33] focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
    {
        variants: {
            variant: {
                default: 'text-primary',
                success: 'text-bright-turquoise-600',
                destructive: 'text-cinnabar-700',
                outline: 'text-foreground',
                secondary: 'text-outer-space-400',
                'light-blue': 'text-accent',
                pink: 'text-pink-vivid-700',
                warning: 'text-fire-bush-700',
            },
            size: {
                sm: 'size-3',
                default: 'size-4',
                md: 'size-5',
                lg: 'size-7',
            },
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
        },
    },
)

const indicatorSizeMap = {
    sm: 'size-2.5',
    default: 'size-3',
    md: 'size-4',
    lg: 'size-5',
}

const indicatorColorMap = {
    default: 'bg-primary',
    success: 'bg-bright-turquoise-600',
    destructive: 'bg-cinnabar-600',
    outline: 'bg-foreground',
    secondary: 'bg-outer-space-400',
    'outer-space': 'bg-outer-space-700',
    pink: 'bg-pink-vivid-700',
    warning: 'bg-fire-bush-600',
}

const indicatorHoverMap = {
    default: 'group-hover:bg-primary/20',
    success: 'group-hover:bg-bright-turquoise-200',
    destructive: 'group-hover:bg-cinnabar-300',
    outline: 'group-hover:bg-foreground/20',
    secondary: 'group-hover:bg-outer-space-200',
    'outer-space': 'group-hover:bg-curious-blue-200',
    pink: 'group-hover:bg-pink-vivid-200',
    warning: 'group-hover:bg-fire-bush-200',
}

interface RadioGroupItemProps
    extends React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>,
        VariantProps<typeof radioGroupItemVariants> {
    tooltip?: string
}

const RadioGroupItem = React.forwardRef<
    React.ElementRef<typeof RadioGroupPrimitive.Item>,
    RadioGroupItemProps
>(({ className, variant, size, tooltip, ...props }, ref) => {
    const sizeClass = size
        ? indicatorSizeMap[size as keyof typeof indicatorSizeMap]
        : indicatorSizeMap.default
    const colorClass = variant
        ? indicatorColorMap[variant as keyof typeof indicatorColorMap]
        : indicatorColorMap.default
    const hoverClass = variant
        ? indicatorHoverMap[variant as keyof typeof indicatorHoverMap]
        : indicatorHoverMap.default

    const radioItem = (
        <RadioGroupPrimitive.Item
            ref={ref}
            className={cn(
                radioGroupItemVariants({ variant, size }),
                className,
                'before:content-[""] before:absolute before:inset-0 before:rounded-full before:shadow-[inset_0_2px_2px_#0000001A]',
            )}
            {...props}>
            <RadioGroupPrimitive.Indicator className="flex relative z-20 items-center justify-center">
                <div className={cn('rounded-full', sizeClass, colorClass)} />
            </RadioGroupPrimitive.Indicator>
            <div
                className={cn(
                    'rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
                    'will-change-transform will-change-width will-change-padding transform-gpu',
                    'group-hover:transition-colors group-hover:ease-out group-hover:duration-300',
                    sizeClass,
                    hoverClass,
                )}
            />
        </RadioGroupPrimitive.Item>
    )

    if (tooltip) {
        return (
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>{radioItem}</TooltipTrigger>
                    <TooltipContent>{tooltip}</TooltipContent>
                </Tooltip>
            </TooltipProvider>
        )
    }

    return radioItem
})
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName

export {
    RadioGroup,
    RadioGroupItem,
    radioGroupItemVariants,
    type RadioGroupItemProps,
    indicatorSizeMap,
    indicatorColorMap,
    indicatorHoverMap,
}

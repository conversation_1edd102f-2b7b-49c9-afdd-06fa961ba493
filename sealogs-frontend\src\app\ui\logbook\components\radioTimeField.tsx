'use client'
import React, { useEffect, useState } from 'react'
import {
    <PERSON>ton,
    DialogTrigger,
    ModalOverlay,
    Modal,
    Dialog,
} from 'react-aria-components'
// import { classes } from '@/app/components/GlobalClasses'
import { LocalizationProvider, StaticTimePicker } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import dayjs from 'dayjs'
import SeaLogsButton from '@/components/ui/sea-logs-button'

export default function RadioTimeField({
    log,
    handleTimeChange,
    fieldName = 'Time',
    buttonLabel = 'Set To Now',
    hideButton = false,
}: {
    log: any
    handleTimeChange: any
    fieldName?: any
    buttonLabel?: any
    hideButton?: any
}) {
    const [time, setTime] = useState<any>(log.time ? dayjs(log.time) : false)
    const handleTimeFieldChange = (time: any) => {
        setTime(time)
        handleTimeChange(log, time)
    }

    return (
        <div className="flex flex-row gap-2 items-center">
            <DialogTrigger>
                <Button>
                    <input
                        id={log.id}
                        name={log.id}
                        type="text"
                        value={time ? dayjs(time).format('HH:mm') : ''}
                        onChange={(e) => {
                            const newTime = e.target.value
                            setTime(dayjs(newTime, 'HH:mm').toDate())
                        }}
                        aria-describedby="time-error"
                        required
                        placeholder={fieldName}
                        />
                </Button>
                <ModalOverlay
                    className={({ isEntering, isExiting }) => `
                    fixed inset-0 z-[15002] overflow-y-auto bg-black/25 flex min-h-full justify-center p-4 text-center backdrop-blur
                    ${isEntering ? 'animate-in fade-in duration-300 ease-out' : ''}
                    ${isExiting ? 'animate-out fade-out duration-200 ease-in' : ''}
                    `}>
                    <Modal>
                        <Dialog
                            role="alertdialog"
                            className="outline-none relative">
                            {({ close }) => (
                                <LocalizationProvider
                                    dateAdapter={AdapterDayjs}>
                                    <StaticTimePicker
                                        className={`p-0 mr-4`}
                                        onAccept={handleTimeFieldChange}
                                        defaultValue={dayjs()}
                                        onClose={close}
                                    />
                                </LocalizationProvider>
                            )}
                        </Dialog>
                    </Modal>
                </ModalOverlay>
            </DialogTrigger>
            {!hideButton && (
                <div className="flex flex-wrap flex-row">
                    <SeaLogsButton
                        text={buttonLabel}
                        type="secondary"
                        color="sky"
                        action={() => {
                            handleTimeFieldChange(dayjs())
                        }}
                        className="text-nowrap"
                    />
                </div>
            )}
        </div>
    )
}

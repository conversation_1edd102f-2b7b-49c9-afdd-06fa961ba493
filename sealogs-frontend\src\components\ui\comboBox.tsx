'use client'

import * as React from 'react'
import { Check, PlusCircle, ChevronDown, X } from 'lucide-react'
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from '@/components/ui/command'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { cn } from '@/app/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'
import {
    Avatar,
    AvatarFallback,
    AvatarImage,
    getCrewInitials,
} from '@/components/ui/avatar'
import { Label, LabelPosition } from './label'

/* -------------------------------------------------------------------------- */
/* Types                                                                      */
/* -------------------------------------------------------------------------- */

export type Option = {
    value: string
    label: string | number | null
    profile?: {
        avatar?: string
        firstName?: string
        surname?: string | null
    }
}

interface ComboboxProps
    extends Omit<
        React.ButtonHTMLAttributes<HTMLButtonElement>,
        'onChange' | 'value' | 'defaultValue'
    > {
    options: Option[]
    title?: string
    value?: Option | Option[] | null
    defaultValues?: Option | Option[] | null
    onChange: (option: Option | Option[] | null) => void
    placeholder?: string
    buttonClassName?: string
    multi?: boolean
    isDisabled?: boolean
    isLoading?: boolean
    label?: string
    labelPosition?: LabelPosition
    labelClassName?: string
    searchThreshold?: number
    required?: boolean
    noResultsMessage?: string
    searchPlaceholder?: string
    groupBy?: (option: Option) => string
    selectAllLabel?: string
    badgeLimit?: number
    wrapBadges?: boolean
}

/* -------------------------------------------------------------------------- */
/* Controlled / uncontrolled helper                                           */
/* -------------------------------------------------------------------------- */

function useControlled<T>(controlled: T | undefined, defaultValue: T) {
    const [state, setState] = React.useState(defaultValue)
    const value = controlled !== undefined ? controlled : state
    return [value, setState] as [T, React.Dispatch<React.SetStateAction<T>>]
}

/* -------------------------------------------------------------------------- */
/* Avatar helper                                                              */
/* -------------------------------------------------------------------------- */

const OptionAvatar = React.memo(function OptionAvatar({
    profile,
    label,
}: {
    profile?: Option['profile']
    label?: Option['label']
}) {
    if (!profile) return null
    const initials =
        getCrewInitials(profile.firstName, profile.surname ?? '') ??
        String(label).charAt(0)
    return (
        <Avatar size="xs">
            <AvatarImage src={profile.avatar} alt={String(label)} />
            <AvatarFallback>{initials}</AvatarFallback>
        </Avatar>
    )
})

/* -------------------------------------------------------------------------- */
/* Main component                                                             */
/* -------------------------------------------------------------------------- */

export const Combobox = ({
    options,
    title,
    value,
    defaultValues,
    onChange,
    placeholder = 'Select an option',
    buttonClassName = '',
    multi = false,
    isDisabled = false,
    isLoading = false,
    label,
    labelPosition = 'top',
    required = false,
    labelClassName = '',
    searchThreshold = 5,
    noResultsMessage = 'No results found.',
    searchPlaceholder = 'Search...',
    groupBy,
    selectAllLabel = 'Select All',
    badgeLimit = 2,
    wrapBadges = false,
    ...buttonProps
}: ComboboxProps) => {
    const comboboxId = React.useId()
    const [open, setOpen] = React.useState(false)
    const [searchQuery, setSearchQuery] = React.useState('')
    const [activeItem, setActiveItem] = React.useState<string | null>(null)

    /* ----------------------------------------------------------------------- */
    /* Controlled / uncontrolled                                               */
    /* ----------------------------------------------------------------------- */
    const [currentValue, setCurrentValue] = useControlled<
        Option | Option[] | null
    >(
        value,
        multi
            ? (defaultValues as Option[]) || []
            : (defaultValues as Option) || null,
    )

    /* ----------------------------------------------------------------------- */
    /* Filtering & grouping                                                    */
    /* ----------------------------------------------------------------------- */
    const filteredOptions = React.useMemo(() => {
        if (!searchQuery) return options
        const q = searchQuery.toLowerCase()
        return options.filter((opt) => {
            const lbl = String(opt.label ?? '').toLowerCase()
            return (
                lbl.includes(q) || lbl.split(' ').some((w) => w.startsWith(q))
            )
        })
    }, [options, searchQuery])

    const groupedOptions = React.useMemo(() => {
        if (!groupBy) return { ungrouped: filteredOptions }
        return filteredOptions.reduce<Record<string, Option[]>>((acc, opt) => {
            const key = groupBy(opt) || 'Other'
            ;(acc[key] = acc[key] || []).push(opt)
            return acc
        }, {})
    }, [filteredOptions, groupBy])

    /* ----------------------------------------------------------------------- */
    /* Badge logic                                                             */
    /* ----------------------------------------------------------------------- */
    const [visibleBadges, hiddenCount] = React.useMemo(() => {
        if (!multi || !Array.isArray(currentValue) || currentValue.length === 0)
            return [[], 0] as [Option[], number]

        if (wrapBadges) return [currentValue, 0]

        const limit = Math.max(badgeLimit, 0)
        const visible = currentValue.slice(0, limit)
        return [visible, currentValue.length - visible.length]
    }, [currentValue, multi, badgeLimit, wrapBadges])

    /* ----------------------------------------------------------------------- */
    /* Helpers                                                                 */
    /* ----------------------------------------------------------------------- */
    const isSelected = React.useCallback(
        (opt: Option) => {
            if (multi) {
                return (
                    Array.isArray(currentValue) &&
                    currentValue.some((c) => c.value === opt.value)
                )
            }
            return (currentValue as Option)?.value === opt.value
        },
        [currentValue, multi],
    )

    const updateBadges = React.useCallback(() => {
        setSearchQuery((q) => q) // force re-render
    }, [])

    const handleSelect = React.useCallback(
        (selectedValue: string) => {
            /* -- “Select All” ---------------------------------------------------- */
            if (multi && selectedValue === 'select-all') {
                const currentArr = Array.isArray(currentValue)
                    ? [...currentValue]
                    : []
                const allSelected = filteredOptions.every((f) =>
                    currentArr.some((c) => c.value === f.value),
                )

                const newVals: Option[] = allSelected
                    ? currentArr.filter(
                          (c) =>
                              !filteredOptions.some((f) => f.value === c.value),
                      )
                    : [
                          ...currentArr.filter(
                              (c) =>
                                  !filteredOptions.some(
                                      (f) => f.value === c.value,
                                  ),
                          ),
                          ...filteredOptions.filter(
                              (f) =>
                                  !currentArr.some((c) => c.value === f.value),
                          ),
                      ]

                setCurrentValue(newVals)
                onChange(newVals)
                updateBadges()
                return
            }

            /* -- Regular selection ---------------------------------------------- */
            const opt = options.find((o) => o.value === selectedValue)
            if (!opt) return

            if (multi) {
                const curr = Array.isArray(currentValue)
                    ? [...currentValue]
                    : []
                const idx = curr.findIndex((c) => c.value === opt.value)
                const newArr =
                    idx >= 0
                        ? [...curr.slice(0, idx), ...curr.slice(idx + 1)]
                        : [...curr, opt]
                setCurrentValue(newArr)
                onChange(newArr)
                updateBadges()
            } else {
                const newVal =
                    (currentValue as Option)?.value === opt.value ? null : opt
                setCurrentValue(newVal)
                onChange(newVal)
                setOpen(false)
            }
        },
        [multi, currentValue, filteredOptions, options, onChange, updateBadges],
    )

    const handleBadgeRemove = React.useCallback(
        (value: string) => {
            const newArr = (currentValue as Option[]).filter(
                (i) => i.value !== value,
            )
            setCurrentValue(newArr)
            onChange(newArr)
            updateBadges()
        },
        [currentValue, onChange, updateBadges],
    )

    /* Reset search on popover close ----------------------------------------- */
    React.useEffect(() => {
        if (!open) {
            setSearchQuery('')
            setActiveItem(null)
        }
    }, [open])

    /* Screen reader text ---------------------------------------------------- */
    const selectedOptionsText = React.useMemo(() => {
        if (!multi || !Array.isArray(currentValue) || currentValue.length === 0)
            return ''
        return `Selected options: ${currentValue.map((o) => o.label ?? 'Unknown').join(', ')}`
    }, [multi, currentValue])

    /* ----------------------------------------------------------------------- */
    /* Renderers                                                               */
    /* ----------------------------------------------------------------------- */

    const renderComboboxButton = () => (
        <Button
            id={comboboxId}
            disabled={isDisabled}
            aria-required={required}
            variant="outline"
            asInput
            data-wrap={wrapBadges ? 'true' : undefined}
            className={cn(
                'justify-between shadow-none font-normal h-13 flex-1 px-4 bg-background text-outer-space-500 transition-colors',
                'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                wrapBadges
                    ? 'items-start min-h-[43px] h-fit py-3'
                    : 'items-center justify-between max-h-[43px]',
                buttonClassName,
            )}
            aria-expanded={open}
            aria-haspopup="listbox"
            aria-describedby={`${comboboxId}-sr`}
            iconLeft={multi && <PlusCircle className="flex-shrink-0 size-3" />}
            iconRight={
                <ChevronDown size={20} className="text-outer-space-400" />
            }
            {...buttonProps}>
            <div className="w-full flex flex-col overflow-auto gap-2.5 min-w-0">
                {multi ? (
                    Array.isArray(currentValue) && currentValue.length > 0 ? (
                        <div className="flex border-l border-border ps-2.5 gap-2.5">
                            {/* Visible / scrollable badge row -------------------------------- */}
                            <div
                                className={cn(
                                    'flex gap-2.5',
                                    wrapBadges
                                        ? 'flex-wrap'
                                        : 'flex-nowrap flex-1 overflow-auto',
                                )}>
                                {visibleBadges.map((opt) => (
                                    <Badge
                                        key={opt.value}
                                        variant="outline"
                                        title={String(opt.label ?? '')}
                                        className={cn(
                                            'rounded-md min-w-28 font-normal px-1.5 py-0.5 flex items-center gap-1 hover:bg-secondary/80 transition-colors flex-shrink-0',
                                            wrapBadges
                                                ? 'h-fit w-fit'
                                                : 'h-full min-w-min overflow-auto',
                                        )}>
                                        <div className="flex flex-1 items-center overflow-auto gap-2.5">
                                            <OptionAvatar
                                                profile={opt.profile}
                                                label={opt.label}
                                            />
                                            <span className="text-base leading-5 truncate text-outer-space-500">
                                                {opt.label ?? ''}
                                            </span>
                                        </div>
                                        <div
                                            className="ml-1 flex items-center justify-center"
                                            onClick={(e) => {
                                                e.stopPropagation()
                                                handleBadgeRemove(opt.value)
                                            }}
                                            aria-label={`Remove ${opt.label ?? ''}`}>
                                            <X size={18} />
                                        </div>
                                    </Badge>
                                ))}
                                {hiddenCount > 0 && (
                                    <Badge
                                        variant="outline"
                                        className="rounded-md px-1.5 py-0.5 w-fit h-full flex-shrink-0"
                                        aria-label={`${hiddenCount} more selected`}>
                                        +{hiddenCount} more
                                    </Badge>
                                )}
                            </div>
                        </div>
                    ) : (
                        <span className="text-base flex items-center leading-5 text-outer-space-500">
                            {title || placeholder}
                        </span>
                    )
                ) : (
                    <span className="flex items-center gap-2.5">
                        <OptionAvatar
                            profile={(currentValue as Option)?.profile}
                            label={(currentValue as Option)?.label}
                        />
                        <span className="text-base leading-5 text-cool-grey-600">
                            {(currentValue as Option)?.label ?? placeholder}
                        </span>
                    </span>
                )}
            </div>
        </Button>
    )

    const renderCombobox = () => (
        <>
            <Popover modal open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    {renderComboboxButton()}
                </PopoverTrigger>
                <PopoverContent
                    align="start"
                    className={cn(
                        'p-0 z-[9999] md:w-fit md:min-w-[300px] w-[--radix-popover-trigger-width] max-h-[400px] overflow-y-auto',
                        '[&_cmdk-item][data-selected=true]:bg-transparent [&_cmdk-item][data-selected=true]:text-foreground',
                    )}>
                    {/* Command ------------------------------ */}
                    <Command
                        className="w-full"
                        loop={false}
                        shouldFilter={false}
                        value=""
                        onKeyDown={(e) => {
                            if (e.key === 'ArrowUp' || e.key === 'ArrowDown')
                                setActiveItem('keyboard-nav')
                            else if (e.key === 'Escape') setOpen(false)
                        }}
                        onMouseMove={() =>
                            activeItem === 'keyboard-nav' && setActiveItem(null)
                        }>
                        {/* Search bar */}
                        {options.length >= searchThreshold && (
                            <CommandInput
                                placeholder={searchPlaceholder}
                                className="flex h-9 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                                value={searchQuery}
                                onValueChange={setSearchQuery}
                            />
                        )}

                        {/* List */}
                        <CommandList className="p-2.5 max-h-[320px] overflow-y-auto">
                            <CommandEmpty>{noResultsMessage}</CommandEmpty>

                            {multi && filteredOptions.length > 0 && (
                                <CommandGroup>
                                    <CommandItem
                                        value="select-all"
                                        onSelect={() => {
                                            handleSelect('select-all')
                                            setActiveItem(null)
                                        }}
                                        data-selected={
                                            activeItem === 'keyboard-nav'
                                                ? undefined
                                                : false
                                        }
                                        className="flex items-center gap-2.5 h-[33px] py-[6px] px-5 hover:bg-cool-grey-50 hover:text-primary">
                                        <div
                                            className={cn(
                                                'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                                                filteredOptions.every(
                                                    (opt) =>
                                                        Array.isArray(
                                                            currentValue,
                                                        ) &&
                                                        currentValue.some(
                                                            (c) =>
                                                                c.value ===
                                                                opt.value,
                                                        ),
                                                )
                                                    ? 'bg-primary text-primary-foreground'
                                                    : 'opacity-50 [&_svg]:invisible',
                                            )}>
                                            <Check className="h-3 w-3" />
                                        </div>
                                        <span className="text-base leading-5 text-outer-space-500">
                                            {selectAllLabel}
                                        </span>
                                    </CommandItem>
                                </CommandGroup>
                            )}

                            {Object.entries(groupedOptions).map(
                                ([grp, opts]) => (
                                    <CommandGroup
                                        key={grp}
                                        heading={
                                            groupBy && grp !== 'ungrouped'
                                                ? grp
                                                : undefined
                                        }>
                                        {opts.map((opt) => (
                                            <CommandItem
                                                key={opt.value}
                                                value={opt.value}
                                                onSelect={() => {
                                                    handleSelect(opt.value)
                                                    setActiveItem(null)
                                                    if (!multi) setOpen(false)
                                                }}
                                                className={cn(
                                                    'flex items-center gap-2.5 h-[33px] py-[6px] px-5',
                                                    !multi && isSelected(opt)
                                                        ? 'bg-outer-space-50 text-primary'
                                                        : '',
                                                    'hover:bg-curious-blue-50 hover:text-primary',
                                                )}
                                                data-selected={
                                                    activeItem ===
                                                    'keyboard-nav'
                                                        ? undefined
                                                        : false
                                                }
                                                disabled={isDisabled}>
                                                {multi && (
                                                    <div
                                                        className={cn(
                                                            'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                                                            isSelected(opt)
                                                                ? 'bg-primary text-primary'
                                                                : 'opacity-50 [&_svg]:invisible',
                                                        )}>
                                                        <Check className="h-3 w-3" />
                                                    </div>
                                                )}
                                                <div className="flex items-center gap-2.5">
                                                    <OptionAvatar
                                                        profile={opt.profile}
                                                        label={opt.label}
                                                    />
                                                    <span className="text-base leading-5 text-outer-space-500">
                                                        {opt.label ?? ''}
                                                    </span>
                                                    {!multi &&
                                                        isSelected(opt) && (
                                                            <Check className="ml-auto h-4 w-4 text-primary" />
                                                        )}
                                                </div>
                                            </CommandItem>
                                        ))}
                                    </CommandGroup>
                                ),
                            )}
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>

            {selectedOptionsText && (
                <span
                    id={`${comboboxId}-sr`}
                    className="sr-only"
                    aria-live="polite">
                    {selectedOptionsText}
                </span>
            )}
        </>
    )

    /* ----------------------------------------------------------------------- */
    /* Loading state                                                           */
    /* ----------------------------------------------------------------------- */
    if (isLoading) {
        const skeleton = (
            <Skeleton
                className={cn('h-[43px] min-w-[60px]', buttonClassName)}
            />
        )

        return label ? (
            <Label
                id={comboboxId}
                label={label}
                position={labelPosition}
                className={labelClassName}
                disabled={isDisabled}>
                {skeleton}
            </Label>
        ) : (
            skeleton
        )
    }

    return label ? (
        <Label
            id={comboboxId}
            label={label}
            position={labelPosition}
            className={cn('w-full', labelClassName)}
            required={required}
            disabled={isDisabled}>
            {renderCombobox()}
        </Label>
    ) : (
        renderCombobox()
    )
}

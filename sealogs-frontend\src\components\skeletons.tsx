import { Heading } from 'react-aria-components'
import { Label } from '@/components/ui/label'
import { Skeleton } from '@/components/ui/skeleton'
import { TableWrapper } from '@/app/ui/daily-checks/Components'

const shimmer =
    'before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/60 before:to-transparent'

export function CrewListTable() {
    return (
        <div className={`${shimmer}mb-2 w-full rounded-md  p-4`}>
            <div className="flex items-center justify-between border-b pb-8">
                <div className="flex items-center">
                    <div className="mr-2 h-8 w-8 rounded-full "></div>
                    <div className="h-6 w-16 rounded "></div>
                </div>
                <div className="h-6 w-16 rounded "></div>
            </div>
            <div className="flex w-full items-center justify-between pt-4">
                <div>
                    <div className="h-6 w-16 rounded "></div>
                    <div className="mt-2 h-6 w-24 rounded "></div>
                </div>
                <div className="flex justify-end gap-2">
                    <div className="h-10 w-10 rounded "></div>
                    <div className="h-10 w-10 rounded "></div>
                </div>
            </div>
        </div>
    )
}

export function LogBookEntrySkeleton() {
    return (
        <div className={`${shimmer} w-full p-0  border  rounded-lg shadow`}>
            <div className="   shadow flex justify-between">
                <div className="  p-3">
                    <div className="h-8 w-64 rounded "></div>
                </div>
            </div>
            <div className="p-4">
                <LogDateSkeleton />
                <div className="mt-5 flex justify-start flex-col md:flex-row items-center">
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                </div>
                <hr className="my-4" />
                <div className=" mb-4 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 items-center  ">
                    <div className={`h-10 w-48 rounded  mr-2`}></div>
                    <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                        <div className="flex md:items-center flex-col md:flex-row gap-2">
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                        </div>
                    </div>
                </div>
                <div className=" mb-4 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 items-center  ">
                    <div className={`h-10 w-48 rounded  mr-2`}></div>
                    <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                        <div className="flex md:items-center flex-col md:flex-row gap-2">
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                        </div>
                    </div>
                </div>
                <div className=" mb-4 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 items-center  ">
                    <div className={`h-10 w-48 rounded  mr-2`}></div>
                    <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                        <div className="flex md:items-center flex-col md:flex-row gap-2">
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export function LogDateSkeleton() {
    return (
        <div className="mt-2 flex justify-start flex-col md:flex-row items-center">
            <div className={`h-8 w-48 rounded  mr-2`}></div>
            <div className={`mr-2 h-8 w-8 rounded-full `}></div>
            <div className={`h-8 w-48 rounded `}></div>
        </div>
    )
}

export function CrewDutyListSkeleton() {
    return (
        <>
            {Array.from({ length: 3 }, (_, i) => (
                <tr key={i}>
                    <td className="pl-6">
                        <Skeleton />
                    </td>
                    <td>
                        <Skeleton />
                    </td>
                    <td>
                        <Skeleton />
                    </td>
                </tr>
            ))}
        </>
    )
}

export function TrainingSessionListSkeleton({
    memberId = 0 as number,
    vesselId = 0 as number,
}) {
    return (
        <div className="w-full p-0 shadow ">
            <div className="relative overflow-x-auto shadow-md">
                {memberId === 0 && vesselId === 0 && (
                    <>
                        <div className=" flex items-center justify-between flex-column flex-wrap md:flex-row space-y-4 md:space-y-0 ">
                            <Heading className="   p-4">
                                Crew Training List
                            </Heading>
                        </div>
                        <div className="p-5 flex justify-between items-center   text-left rtl:text-right  ">
                            <p className="mt-1 mx-4    ">&nbsp;</p>
                        </div>
                    </>
                )}
                <TableWrapper
                    headings={['Date', 'Type of Training', 'Trainer']}>
                    <tr className=" border-b   ">
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-2 py-2">
                            <div className="flex justify-end flex-col md:flex-row">
                                <Skeleton />
                            </div>
                        </td>
                    </tr>
                    <tr className=" border-b   ">
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-2 py-2">
                            <div className="flex justify-end flex-col md:flex-row">
                                <Skeleton />
                            </div>
                        </td>
                    </tr>
                    <tr className=" border-b   ">
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-2 py-2">
                            <div className="flex justify-end flex-col md:flex-row">
                                <Skeleton />
                            </div>
                        </td>
                    </tr>
                </TableWrapper>
            </div>
        </div>
    )
}

export function TrainingSessionInfoSkeleton() {
    return (
        <div className="w-full p-0">
            <div className="flex justify-between pb-4 pt-3 items-center">
                <Heading className="flex items-center   text-3xl  ">
                    <span className="font-medium mr-2">Training Session:</span>
                    <div className="w-48 ">
                        <InputSkeleton />
                    </div>
                </Heading>
                <div className="w-48 ">
                    <InputSkeleton />
                </div>
            </div>
            <div className="px-0 md:px-4 pt-4 border-t border-b">
                <div className="grid grid-cols-3 gap-6 py-4 px-4">
                    <div>Trainer</div>
                    <Skeleton />
                </div>
                <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Nature of Training</div>
                    <Skeleton />
                </div>
                <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Members</div>
                    <Skeleton />
                </div>
                <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Summary</div>
                    <Skeleton />
                </div>
                <hr className="my-4" />
                <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div className="my-4 ">Signatures</div>
                    <Skeleton />
                </div>
            </div>
        </div>
    )
}

export function TrainingSessionFormSkeleton() {
    return (
        <div className="px-0 md:px-4 pt-4 border-t">
            <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                <div className="my-4 ">Training Details</div>
                <div className="col-span-2">
                    <div className="flex w-full gap-4">
                        <div className="w-full">
                            <div className="w-full my-4 flex flex-col">
                                <Label className="mb-1 ">Trainer</Label>
                                <InputSkeleton />
                            </div>
                            <div className="w-full mt-4 flex flex-col">
                                <InputSkeleton />
                            </div>
                        </div>
                        <div className="w-full mt-4 flex flex-col">
                            <Label className="mb-1 ">Crew</Label>
                            <InputSkeleton />
                        </div>
                    </div>
                    <div className="flex w-full gap-4 mt-4">
                        <div className="w-full">
                            <InputSkeleton />
                        </div>
                        <div className="w-full">
                            <InputSkeleton />
                        </div>
                    </div>
                    <div className="w-full my-4 flex flex-col">
                        <InputSkeleton />
                    </div>
                </div>
            </div>
            <hr className="my-2" />
            <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                <div className="my-4 ">Signatures</div>
                <div className="col-span-2 my-4 flex justify-between flex-wrap gap-4">
                    <InputSkeleton />
                </div>
            </div>
            <hr className="mb-4" />
            <div className="flex justify-end px-4 pb-4 pt-4">
                <div className="w-48 mr-4">
                    <InputSkeleton />
                </div>
                <div className="w-48 ">
                    <InputSkeleton />
                </div>
            </div>
        </div>
    )
}

export function TrainingTypeListSkeleton() {
    return (
        <div className="w-full p-0  border  rounded-lg shadow ">
            <div className="relative overflow-x-auto shadow-md sm:rounded-lg">
                <div className=" flex items-center justify-between flex-column flex-wrap md:flex-row space-y-4 md:space-y-0 ">
                    <Heading className="   p-4">Training Types</Heading>
                </div>
                <div className="p-5 flex justify-between items-center   text-left rtl:text-right     ">
                    &nbsp;
                </div>
                <div className="relative overflow-x-auto">
                    <TableWrapper
                        headings={[
                            'Nature of Training',
                            'Vessels',
                            'Occurs Every (days)',
                            'Medium Warning Within (days)',
                            'High Warning Within (days)',
                            '',
                        ]}>
                        <tr className=" border-b   ">
                            <td className="px-6 py-4">
                                <Skeleton />
                            </td>
                            <td className="px-6 py-4">
                                <Skeleton />
                            </td>
                            <td className="px-6 py-4">
                                <Skeleton />
                            </td>
                            <td className="px-6 py-4">
                                <Skeleton />
                            </td>
                            <td className="px-6 py-4">
                                <Skeleton />
                            </td>
                            <td className="px-2 py-2">
                                <div className="flex justify-end flex-col md:flex-row">
                                    <Skeleton />
                                </div>
                            </td>
                        </tr>
                    </TableWrapper>
                </div>
            </div>
        </div>
    )
}

export function TrainingTypeInfoSkeleton() {
    return (
        <>
            <div className="flex justify-between pb-4 pt-3 items-center">
                <Heading className="  text-3xl  ">Training Types</Heading>
                <div className="flex">
                    <div className="w-48 ">
                        <InputSkeleton />
                    </div>
                    <div className="w-48 ">
                        <InputSkeleton />
                    </div>
                </div>
            </div>
            <div className="px-0 md:px-4 py-4 border-t border-b">
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Nature Of Training</div>
                    <Skeleton />
                </div>
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Occurs Every</div>
                    <Skeleton />
                </div>
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Medium Warning Within</div>
                    <Skeleton />
                </div>
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>High Warning Within</div>
                    <Skeleton />
                </div>
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Procedure</div>
                    <Skeleton />
                </div>
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Vessels</div>
                    <Skeleton />
                </div>
            </div>
        </>
    )
}

export function InputSkeleton() {
    return (
        <div className="relative overflow-hidden w-full">
            <Skeleton className={`h-11 w-full`}></Skeleton>
        </div>
    )
}

export function List(props?: any) {
    return (
        <TableWrapper
            headings={
                props.heading
                    ? [props.heading + ':firstHead', '', ':last']
                    : [':firstHead', '', ':last']
            }>
            <TR />
            <TR />
            <TR />
            <TR />
            <TR />
            <TR />
        </TableWrapper>
    )
}

export function TR() {
    return (
        <tr className={`border-b  hover: `}>
            <td scope="col" className="px-2 py-6 relative">
                <Skeleton />
            </td>
            <td scope="col" className="px-2 py-6 relative">
                <Skeleton />
            </td>
            <td scope="col" className="px-2 py-6 relative">
                <Skeleton />
            </td>
        </tr>
    )
}

export function DepartmentInfoSkeleton() {
    return (
        <div className="w-full p-0">
            <div className="flex justify-between pb-4 pt-3 items-center">
                <Heading className="flex items-center   text-3xl  ">
                    <span className="font-medium mr-2">Department:</span>{' '}
                    <Skeleton />
                </Heading>
            </div>
            <div className="px-0 md:px-4 pt-4 border-t border-b ">
                <div className="grid grid-cols-3 gap-6 py-4 px-4">
                    <div>
                        <Skeleton />
                    </div>
                    <div className="col-span-2">
                        <Skeleton />
                    </div>
                </div>
            </div>
        </div>
    )
}

export function DepartmentFormSkeleton() {
    return (
        <div className="w-full p-0">
            <div className="flex justify-between pb-4 pt-3">
                <Heading className="  text-3xl  ">Department</Heading>
            </div>

            <div className="px-0 md:px-4 pt-4 border-t  ">
                <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div className="my-4 ">
                        Department Details
                        <p className=" mt-4 max-w-[25rem] leading-loose">
                            Lorem ipsum dolor sit amet consectetur adipisicing
                            elit. Facilis possimus harum eaque itaque est id
                            reprehenderit excepturi eius temporibus, illo
                            officia amet nobis sapiente dolorem ipsa earum
                            adipisci recusandae cumque.
                        </p>
                    </div>
                    <div className="col-span-2">
                        <div className="flex w-full gap-4">
                            <div className="w-full">
                                <div className="w-full my-4 flex flex-col">
                                    <Label className="mb-1 ">Name</Label>
                                    <InputSkeleton />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <hr className="mb-4" />
            </div>
        </div>
    )
}

export function DepartmentListSkeleton() {
    return (
        <TableWrapper headings={['Departments:firstHead']}>
            <tr className={`group border-b hover: `}>
                <th scope="row" className="flex items-center px-2 py-3 lg:px-6">
                    <Skeleton />
                </th>
            </tr>
            <tr className={`group border-b hover: `}>
                <th scope="row" className="flex items-center px-2 py-3 lg:px-6">
                    <Skeleton />
                </th>
            </tr>
            <tr className={`group border-b hover: `}>
                <th scope="row" className="flex items-center px-2 py-3 lg:px-6">
                    <Skeleton />
                </th>
            </tr>
        </TableWrapper>
    )
}

export function GeoLocationListSkeleton() {
    return (
        <TableWrapper
            headings={['Location:firstHead', 'Latitude', 'Longitude']}>
            <tr className={`group border-b  hover: `}>
                <td className="pl-6">
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
            </tr>
            <tr className={`group border-b  hover: `}>
                <td className="pl-6">
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
            </tr>
            <tr className={`group border-b  hover: `}>
                <td className="pl-6">
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
            </tr>
        </TableWrapper>
    )
}

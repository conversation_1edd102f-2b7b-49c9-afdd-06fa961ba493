'use client'

import * as React from 'react'
import { cn } from '@/app/lib/utils'

export interface FormFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * The content to be rendered inside the footer
   */
  children: React.ReactNode
}

/**
 * FormFooter component provides a consistent footer for forms with a standardized style
 */
const FormFooter = React.forwardRef<HTMLDivElement, FormFooterProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-wrap items-center rounded-lg gap-4 xs:gap-0 bg-cool-grey-50 border xs:h-[85px] border-border p-5 justify-between",
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

FormFooter.displayName = 'FormFooter'

export { FormFooter }

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/crew/allocated-tasks.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleBottomCenterTextIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.3.1/node_modules/@heroicons/react/24/outline/esm/ChatBubbleBottomCenterTextIcon.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst CrewAllocatedTasks = (param)=>{\n    let { taskList } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [filteredTaskList, setFilteredTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(taskList);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        vessel: null,\n        status: null,\n        keyword: null\n    });\n    // Update filtered list when taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        setFilteredTaskList(taskList);\n    }, [\n        taskList\n    ]);\n    // Apply all filters whenever filters or taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (!taskList) return;\n        let filtered = [\n            ...taskList\n        ];\n        // Apply vessel filter - following the same pattern as maintenance list\n        if (filters.vessel) {\n            // Handle both single and multi-select vessel filtering\n            let vesselIds = [];\n            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {\n                vesselIds = filters.vessel.map((item)=>String(item.value));\n            } else if (filters.vessel && !Array.isArray(filters.vessel)) {\n                vesselIds = [\n                    String(filters.vessel.value)\n                ];\n            }\n            if (vesselIds.length > 0) {\n                filtered = filtered.filter((task)=>{\n                    var _task_basicComponent;\n                    const taskVesselId = String(task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.id);\n                    return vesselIds.includes(taskVesselId);\n                });\n            }\n        }\n        // Apply status filter\n        if (filters.status) {\n            filtered = filtered.filter((task)=>(task === null || task === void 0 ? void 0 : task.status) === filters.status.value);\n        }\n        // Apply keyword filter\n        if (filters.keyword && filters.keyword.value) {\n            const keyword = filters.keyword.value.toLowerCase();\n            console.log('Applying keyword filter: \"'.concat(keyword, '\"'));\n            const beforeCount = filtered.length;\n            filtered = filtered.filter((task)=>{\n                var _task_name, _task_description, _task_comments;\n                const nameMatch = task === null || task === void 0 ? void 0 : (_task_name = task.name) === null || _task_name === void 0 ? void 0 : _task_name.toLowerCase().includes(keyword);\n                const descMatch = task === null || task === void 0 ? void 0 : (_task_description = task.description) === null || _task_description === void 0 ? void 0 : _task_description.toLowerCase().includes(keyword);\n                const commentMatch = task === null || task === void 0 ? void 0 : (_task_comments = task.comments) === null || _task_comments === void 0 ? void 0 : _task_comments.toLowerCase().includes(keyword);\n                const match = nameMatch || descMatch || commentMatch;\n                if (match) {\n                    console.log('Task \"'.concat(task.name, '\" matches keyword \"').concat(keyword, '\"'));\n                }\n                return match;\n            });\n            console.log(\"Keyword filter: \".concat(beforeCount, \" -> \").concat(filtered.length, \" tasks\"));\n        }\n        setFilteredTaskList(filtered);\n    }, [\n        taskList,\n        filters\n    ]);\n    // Handle filter changes\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        setFilters((prev)=>({\n                ...prev,\n                [type]: data\n            }));\n    };\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: \"Task\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2, _task_isOverDue3, _task_isOverDue4, _task_isOverDue5, _task_isOverDue6, _task_isOverDue7, _task_isOverDue8, _task_isOverDue9, _task_isOverDue10, _task_isOverDue11, _task_isOverDue12, _task_isOverDue13, _task_isOverDue14, _task_isOverDue15, _task_isOverDue16, _task_isOverDue17, _task_isOverDue18, _task_isOverDue19;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-foreground flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(task.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: \"focus:outline-none\",\n                                            children: task.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block rounded px-3 py-1 ml-3 \".concat((task === null || task === void 0 ? void 0 : (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\" ? \"alert\" : \"\", \" \").concat((task === null || task === void 0 ? void 0 : (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) === \"Low\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.status) === \"Upcoming\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status) === \"Completed\" ? \"success\" : \"\", \" \").concat((task === null || task === void 0 ? void 0 : (_task_isOverDue4 = task.isOverDue) === null || _task_isOverDue4 === void 0 ? void 0 : _task_isOverDue4.status) === \"Medium\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue5 = task.isOverDue) === null || _task_isOverDue5 === void 0 ? void 0 : _task_isOverDue5.days) === \"Save As Draft\" ? \"text-yellow-600 bg-yellow-100\" : \"\"),\n                                            children: [\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue6 = task.isOverDue) === null || _task_isOverDue6 === void 0 ? void 0 : _task_isOverDue6.status) && [\n                                                    \"High\",\n                                                    \"Medium\",\n                                                    \"Low\"\n                                                ].includes(task.isOverDue.status) && (task === null || task === void 0 ? void 0 : (_task_isOverDue7 = task.isOverDue) === null || _task_isOverDue7 === void 0 ? void 0 : _task_isOverDue7.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue8 = task.isOverDue) === null || _task_isOverDue8 === void 0 ? void 0 : _task_isOverDue8.status) === \"Completed\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue9 = task.isOverDue) === null || _task_isOverDue9 === void 0 ? void 0 : _task_isOverDue9.days) === \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue10 = task.isOverDue) === null || _task_isOverDue10 === void 0 ? void 0 : _task_isOverDue10.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue11 = task.isOverDue) === null || _task_isOverDue11 === void 0 ? void 0 : _task_isOverDue11.status) === \"Upcoming\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue12 = task.isOverDue) === null || _task_isOverDue12 === void 0 ? void 0 : _task_isOverDue12.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue13 = task.isOverDue) === null || _task_isOverDue13 === void 0 ? void 0 : _task_isOverDue13.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue14 = task.isOverDue) === null || _task_isOverDue14 === void 0 ? void 0 : _task_isOverDue14.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue15 = task.isOverDue) === null || _task_isOverDue15 === void 0 ? void 0 : _task_isOverDue15.status),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue16 = task.isOverDue) === null || _task_isOverDue16 === void 0 ? void 0 : _task_isOverDue16.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue17 = task.isOverDue) === null || _task_isOverDue17 === void 0 ? void 0 : _task_isOverDue17.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue18 = task.isOverDue) === null || _task_isOverDue18 === void 0 ? void 0 : _task_isOverDue18.days) !== \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue19 = task.isOverDue) === null || _task_isOverDue19 === void 0 ? void 0 : _task_isOverDue19.days)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-14 flex items-center pl-1\",\n                                    children: task.Comments !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"outline-none px-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                                                className: \"w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"leading-loose\",\n                                                    children: task.comments\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 25\n                        }, undefined),\n                        task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1 text-sm text-muted-foreground\",\n                            children: task.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: \"Vessel\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_basicComponent;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: (task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.title) || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"inventoryItem\",\n            header: \"Inventory Item\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_inventory, _task_maintenanceSchedule_inventory, _task_maintenanceSchedule;\n                const task = row.original;\n                // Check for inventory from multiple possible sources\n                const inventoryItem = (task === null || task === void 0 ? void 0 : (_task_inventory = task.inventory) === null || _task_inventory === void 0 ? void 0 : _task_inventory.item) || (task === null || task === void 0 ? void 0 : (_task_maintenanceSchedule = task.maintenanceSchedule) === null || _task_maintenanceSchedule === void 0 ? void 0 : (_task_maintenanceSchedule_inventory = _task_maintenanceSchedule.inventory) === null || _task_maintenanceSchedule_inventory === void 0 ? void 0 : _task_maintenanceSchedule_inventory.item) || null;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: inventoryItem || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-right\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground mb-1\",\n                            children: [\n                                \"Due: \",\n                                (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(task.expires)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block rounded px-3 py-1 \".concat(task.status == \"Completed\" ? \"success\" : \"alert\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: task.status\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: !taskList ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 249,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n            columns: columns,\n            data: filteredTaskList || [],\n            showToolbar: true,\n            pageSize: 50,\n            showPageSizeSelector: false,\n            onChange: handleFilterOnChange\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 251,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s(CrewAllocatedTasks, \"ma6jPe/tw3b936HXYaWPMLLamsk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams\n    ];\n});\n_c = CrewAllocatedTasks;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewAllocatedTasks);\nvar _c;\n$RefreshReg$(_c, \"CrewAllocatedTasks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\n"));

/***/ })

});
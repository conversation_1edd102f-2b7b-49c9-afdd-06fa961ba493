
'use client'

import DepartTime from './depart-time'
import DepartLocation from './depart-location'
import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import {
    CreateLogBookEntrySection_Signature,
    CreateTripReport_LogBookEntrySection,
    CreateTripReport_Stop,
    UpdateLogBookEntrySection_Signature,
    UpdateTripReport_LogBookEntrySection,
} from '@/app/lib/graphQL/mutation'
import {
    GET_GEO_LOCATIONS,
    GET_EVENT_TYPES,
    ReadTripReportSchedules,
    ReadTripScheduleServices,
} from '@/app/lib/graphQL/query'
import { useMutation } from '@apollo/client'
import {
    AlertDialog,
    PopoverWrapper,
    SeaLogsButton,
    TableWrapper,
} from '@/app/components/Components'
import {
    Button,
    DialogTrigger,
    Heading,
    Label,
    Popover,
    TableHeader,
} from 'react-aria-components'
import { useLazyQuery } from '@apollo/client'
import Events from './events'
import POB from './trip-log-pob'
import { getOneClient, getVesselByID } from '@/app/lib/actions'
import ExpectedDestination from './expected-location'
import ExpectedArrival from './exp-arrival-time'
import ActualArrival from './actual-arrival-time'
import TripComments from './components/trip-comments'
import VOB from './trip-log-vob'
import { classes } from '@/app/components/GlobalClasses'
import DGR from './trip-log-dgr'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import toast, { Toaster } from 'react-hot-toast'
import dayjs from 'dayjs'
import ClientModel from '@/app/offline/models/client'
import VesselModel from '@/app/offline/models/vessel'
import GeoLocationModel from '@/app/offline/models/geoLocation'
import EventTypeModel from '@/app/offline/models/eventType'
import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import SignaturePad from '../signature-pad'
import { isEmpty, uniqueId } from 'lodash'
import { PlusIcon } from '@heroicons/react/24/outline'
import Select from 'react-select'
import Loading from '@/app/loading'
import { Input } from '@/app/components/formFields/Input'
import Master from './components/master'

export default function TripLog({
    tripReport = false,
    logBookConfig,
    updateTripReport,
    locked,
    crewMembers,
    masterID,
    setTab,
    createdTab = false,
    setCreatedTab,
    currentTrip = false,
    setCurrentTrip,
    vessels,
    offline = false,
    fuelLogs,
    logBookStartDate,
}: {
    tripReport: any
    logBookConfig: any
    updateTripReport: any
    locked: boolean
    crewMembers: any
    masterID: any
    setTab: any
    createdTab: any
    setCreatedTab: any
    currentTrip: any
    setCurrentTrip: any
    vessels: any
    offline?: boolean
    fuelLogs?: any
    logBookStartDate: any
}) {
    // const router = useRouter()
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const vesselID = searchParams.get('vesselID') ?? 0
    const [locations, setLocations] = useState<any>(false)
    const [eventTypes, setEventTypes] = useState<any>(false)
    const [openTripModal, setOpenTripModal] = useState(false)
    const [bufferTripID, setBufferTripID] = useState(0)
    const [vessel, setVessel] = useState<any>(false)
    const [selectedTab, setSelectedTab] = useState<any>(false)
    const [selectedDGR, setSelectedDGR] = useState<any>(0)
    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = useState(false)
    const [displayDangerousGoods, setDisplayDangerousGoods] = useState(false)
    const [displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing] =
        useState(false)
    const [displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd] =
        useState(false)
    const [
        displayDangerousGoodsPvpdSailing,
        setDisplayDangerousGoodsPvpdSailing,
    ] = useState<boolean | null>(null)
    // const [openEventModal, setOpenEventModal] = useState(false)
    // const [selectedRowdgr, setSelectedRowdgr] = useState<any>(false)
    const [tripReport_Stops, setTripReport_Stops] = useState<any>(false)
    const [selectedDGRPVPD, setSelectedDGRPVPD] = useState<any>(0)
    const [allPVPDDangerousGoods, setAllPVPDDangerousGoods] =
        useState<any>(false)
    const [selectedRowEvent, setSelectedRowEvent] = useState<any>(0)
    const [riskBufferEvDgr, setRiskBufferEvDgr] = useState<any>(false)
    const [allDangerousGoods, setAllDangerousGoods] = useState<any>(false)
    const [currentEventTypeEvent, setCurrentEventTypeEvent] =
        useState<any>(false)
    const [currentStopEvent, setCurrentStopEvent] = useState<any>(false)
    const [client, setClient] = useState<any>()
    const [signature, setSignature] = useState<any>(false)
    const [signatureKey, setSignatureKey] = useState<any>(uniqueId())
    if (!offline) {
        getOneClient(setClient)
    }
    const [comment, setComment] = useState<string>(
        tripReport
            ? tripReport?.find((trip: any) => trip.id === currentTrip.id)
                  ?.comment
            : '',
    )

    const [permissions, setPermissions] = useState<any>(false)
    const [edit_tripReport, setEdit_tripReport] = useState<any>(false)

    const clientModel = new ClientModel()
    const vesselModel = new VesselModel()
    const geoLocationModel = new GeoLocationModel()
    const eventTypeModel = new EventTypeModel()
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    const [openTripSelectionDialog, setOpenTripSelectionDialog] =
        useState(false)
    const [tripReportSchedules, setTripReportSchedules] = useState<any>([])
    const [selectedTripReportSchedule, setSelectedTripReportSchedule] =
        useState<any>(null)
    const [tripScheduleServices, setTripScheduleServices] = useState<any>([])
    const [selectedTripScheduleServiceID, setSelectedTripScheduleServiceID] =
        useState<any>(null)
    const [showNextTrips, setShowNextTrips] = useState(false)
    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_LOGBOOKENTRY_TRIPREPORT', permissions)) {
                setEdit_tripReport(true)
            } else {
                setEdit_tripReport(false)
            }
        }
    }

    const offlineLoad = async () => {
        const locations = await geoLocationModel.getAll()
        setLocations(locations)
        const types = await eventTypeModel.getAll()
        setEventTypes(types)
    }
    useEffect(() => {
        setPermissions(getPermissions)
        if (!locations) {
            if (offline) {
                offlineLoad()
            } else {
                loadLocations()
                loadEventTypes()
            }
        }
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    useEffect(() => {
        if (createdTab) {
            setSelectedTab(createdTab)
        }
    }, [createdTab])

    if (!offline) {
        getVesselByID(+vesselID, setVessel)
    }

    useEffect(() => {
        if (tripReport && currentTrip) {
            const trip = tripReport.find(
                (trip: any) => trip.id === currentTrip.id,
            )
            setCurrentTrip(trip)
        }
        if (tripReport && bufferTripID > 0) {
            const trip = tripReport.find(
                (trip: any) => trip.id === bufferTripID,
            )
            setCurrentTrip(trip)
            setBufferTripID(0)
        }
    }, [tripReport])

    const [loadLocations] = useLazyQuery(GET_GEO_LOCATIONS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            setLocations(response.readGeoLocations.nodes)
        },
        onError: (error) => {
            console.error('Error loading locations', error)
        },
    })

    const [loadEventTypes] = useLazyQuery(GET_EVENT_TYPES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            setEventTypes(response.readEventTypes.nodes)
        },
        onError: (error) => {
            console.error('Error loading activity types', error)
        },
    })

    const [createTripReport_Stop] = useMutation(CreateTripReport_Stop, {
        onCompleted: () => {},
        onError: (error) => {
            console.error('Error creating passenger drop facility', error)
        },
    })

    const handleCreateTripReportScheduleStops = async (
        logBookEntrySectionID: any,
    ) => {
        if (!isEmpty(selectedTripReportSchedule)) {
            const tripStops =
                selectedTripReportSchedule.tripReportScheduleStops.nodes || []
            await Promise.all(
                tripStops.map(async (stop: any) => {
                    const input = {
                        logBookEntrySectionID: logBookEntrySectionID,
                        tripReportScheduleStopID: stop.id,
                        arriveTime: stop.arriveTime,
                        departTime: stop.departTime,
                        stopLocationID: stop.stopLocationID,
                    }
                    await createTripReport_Stop({
                        variables: { input: input },
                    })
                }),
            )
            setSelectedTripReportSchedule(null)
            if (tripReport) {
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        logBookEntrySectionID,
                    ],
                })
            } else {
                updateTripReport({
                    id: [logBookEntrySectionID],
                })
            }
        }
    }
    const [createTripReport_LogBookEntrySection] = useMutation(
        CreateTripReport_LogBookEntrySection,
        {
            onCompleted: (response) => {
                const data = response.createTripReport_LogBookEntrySection
                if (tripReport) {
                    updateTripReport({
                        id: [
                            ...tripReport.map((trip: any) => trip.id),
                            data.id,
                        ],
                    })
                } else {
                    updateTripReport({
                        id: [data.id],
                    })
                }
                setCreatedTab(data.id)
                setBufferTripID(data.id)
                handleCreateTripReportScheduleStops(data.id)
                /* if (!isEmpty(selectedTripReportSchedule)) {
                    const tripStops =
                        selectedTripReportSchedule.tripReportScheduleStops
                            .nodes || []
                    Promise.all(
                        tripStops.map(async (stop: any) => {
                            const input = {
                                logBookEntrySectionID: data.id,
                                tripReportScheduleStopID: stop.id,
                                arriveTime: stop.arriveTime,
                                departTime: stop.departTime,
                                stopLocationID: stop.stopLocationID,
                            }
                            await createTripReport_Stop({
                                variables: { input: input },
                            })
                        }),
                    )
                } */
            },
            onError: (error) => {
                console.error('Error creating trip report', error)
            },
        },
    )
    const [updateTripReport_LogBookEntrySection] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onCompleted: (response) => {
                const data = response.updateTripReport_LogBookEntrySection
                if (tripReport) {
                    updateTripReport({
                        id: [
                            ...tripReport.map((trip: any) => trip.id),
                            data.id,
                        ],
                        currentTripID: currentTrip.id,
                        key: 'comment',
                        value: comment,
                    })
                } else {
                    updateTripReport({
                        id: [data.id],
                        currentTripID: currentTrip.id,
                        key: 'comment',
                        value: comment,
                    })
                }
                setBufferTripID(data.id)
                setCurrentTrip(false)
            },
            onError: (error) => {
                console.error('Error creating trip report', error)
            },
        },
    )

    const [
        readTripReportSchedules,
        { loading: readTripReportSchedulesLoading },
    ] = useLazyQuery(ReadTripReportSchedules, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readTripReportSchedules.nodes.filter(
                (trip: any) =>
                    // only show trips for the current vessel
                    trip.vehicles.nodes.some(
                        (vehicle: any) => +vehicle.id === +vesselID,
                    ),
                // &&
                // only show trips with no log book entry sections
                // trip.tripReport_LogBookEntrySections.nodes.length === 0,
            )
            if (showNextTrips) {
                // only show 1 past trip and 4 upcoming trips
                const currentTime = dayjs().format('HH:mm:ss')
                const pastIndex = data.findIndex(
                    (trip: any) => trip.departTime >= currentTime,
                )
                const result = (
                    pastIndex > 0 ? [data[pastIndex - 1]] : []
                ).concat(data.slice(pastIndex, pastIndex + 4))
                setTripReportSchedules(result)
            } else {
                setTripReportSchedules(data)
            }
        },
        onError: (error) => {
            console.error('Error loading TripReportSchedules', error)
        },
    })
    const [readTripScheduleServices] = useLazyQuery(ReadTripScheduleServices, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readTripScheduleServices.nodes.map(
                (tss: any) => {
                    return {
                        label: tss.title,
                        value: tss.id,
                    }
                },
            )
            setTripScheduleServices(data)
            setTripReportSchedules([])
            setOpenTripSelectionDialog(true)
        },
        onError: (error) => {
            console.error('Error loading TripScheduleServices', error)
        },
    })
    const loadTripScheduleServices = async () => {
        await readTripScheduleServices({
            variables: {
                filter: {
                    vehicles: { id: { eq: vesselID } },
                },
            },
        })
    }
    const loadTripReportSchedules = async (tripScheduleServiceID: any) => {
        setTripReportSchedules([])
        await readTripReportSchedules({
            variables: {
                filter: {
                    // archived: { eq: false },
                    // start: { eq: logBookStartDate },
                    tripScheduleServiceID: { eq: tripScheduleServiceID },
                },
            },
        })
    }
    const doCreateTripReport = async (input: any) => {
        if (!edit_tripReport) {
            toast.error('You do not have permission to add a trip')
            return
        }
        setOpenTripModal(false)
        setCurrentTrip(false)
        if (offline) {
            const data = await tripReportModel.save({
                ...input,
                id: generateUniqueId(),
            })
            if (tripReport) {
                updateTripReport({
                    id: [...tripReport.map((trip: any) => trip.id), data.id],
                })
            } else {
                updateTripReport({
                    id: [data.id],
                })
            }
            setCreatedTab(data.id)
            setBufferTripID(data.id)
        } else {
            createTripReport_LogBookEntrySection({
                variables: {
                    input: input,
                },
            })
        }
        setRiskBufferEvDgr(false)
        setOpenTripStartRiskAnalysis(false)
        setAllDangerousGoods(false)
        setCurrentStopEvent(false)
        setCurrentEventTypeEvent(false)
        setSelectedRowEvent(false)
        setDisplayDangerousGoods(false)
        setDisplayDangerousGoodsSailing(false)
        setDisplayDangerousGoodsPvpd(false)
        setDisplayDangerousGoodsPvpdSailing(null)
        setAllPVPDDangerousGoods(false)
        setSelectedDGRPVPD(false)
        setTripReport_Stops(false)
    }
    const handleAddTrip = async () => {
        const allowedVesselTypes = [
            'SLALL',
            'Tug_Boat',
            'Passenger_Ferry',
            'Water_Taxi',
        ]
        if (allowedVesselTypes.includes(vessel.vesselType)) {
            loadTripScheduleServices()
        } else {
            handleCustomTrip()
        }
    }

    const handleSelectTripReportSchedule = (trip: any) => {
        setSelectedTripReportSchedule(trip)
        setOpenTripSelectionDialog(false)
        const input = {
            tripReportScheduleID: trip.id,
            departTime: trip.departTime,
            arriveTime: trip.arriveTime,
            fromLocationID: trip.fromLocationID,
            toLocationID: trip.toLocationID,
            logBookEntryID: logentryID,
        }
        doCreateTripReport(input)
    }
    const handleCustomTrip = () => {
        setOpenTripSelectionDialog(false)
        const input = {
            logBookEntryID: logentryID,
        }
        doCreateTripReport(input)
    }
    const handleEditTrip = (trip: any) => {
        setCurrentTrip(trip)
        setOpenTripModal(true)
    }

    const [
        createLogBookEntrySection_Signature,
        { loading: createLogBookEntrySection_SignatureLoading },
    ] = useMutation(CreateLogBookEntrySection_Signature, {
        onCompleted: (response) => {
            const data = response.createLogBookEntrySection_Signature
            updateTripReport_LogBookEntrySection({
                variables: {
                    input: {
                        id: +data.logBookEntrySectionID,
                        sectionSignatureID: +data?.id,
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error saving signature', error)
        },
    })
    const [updateLogBookEntrySection_Signature] = useMutation(
        UpdateLogBookEntrySection_Signature,
        {
            onCompleted: (response) => {
                const data = response.updateLogBookEntrySection_Signature
                updateTripReport_LogBookEntrySection({
                    variables: {
                        input: {
                            id: +currentTrip.id,
                            sectionSignatureID: +data?.id,
                        },
                    },
                })
            },
            onError: (error) => {
                console.error(
                    '341 TripLog updateLogBookEntrySection_Signature',
                    error,
                )
            },
        },
    )
    const handleSave = async () => {
        toast.loading('Saving trip...')
        // setCurrentTrip(false)
        // if (signature === false || signature === '') {
        //     toast.dismiss()
        //     toast.error('Please sign the trip log')
        //     return
        // }
        const sigVariables = {
            logBookEntrySectionID: currentTrip.id,
            memberID: localStorage.getItem('userId'),
            signatureData: signature ? signature : '',
        }
        if (+currentTrip.sectionSignatureID > 0) {
            // Update signature
            updateLogBookEntrySection_Signature({
                variables: {
                    input: {
                        ...sigVariables,
                        id: +currentTrip.sectionSignatureID,
                    },
                },
            })
        } else {
            // Create signature
            createLogBookEntrySection_Signature({
                variables: {
                    input: sigVariables ?? '',
                },
            })
        }
        if (offline) {
            // updateTripReport_LogBookEntrySection
            const data = await tripReportModel.save({
                id: currentTrip.id,
                comment: comment || null,
            })
            if (tripReport) {
                updateTripReport({
                    id: [...tripReport.map((trip: any) => trip.id), data.id],
                    currentTripID: currentTrip.id,
                    key: 'comment',
                    value: comment,
                })
            } else {
                updateTripReport({
                    id: [data.id],
                    currentTripID: currentTrip.id,
                    key: 'comment',
                    value: comment,
                })
            }
            setBufferTripID(data.id)
        } else {
            await updateTripReport_LogBookEntrySection({
                variables: {
                    input: {
                        id: currentTrip.id,
                        comment: comment || null,
                    },
                },
            })
            toast.dismiss()
            toast.success('Trip updated successfully')
            setOpenTripModal(false)
            setCurrentTrip(false)
        }
    }

    const displayFieldTripLog = (fieldName: string) => {
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'TripReport_LogBookComponent',
            )
        if (
            dailyChecks?.length > 0 &&
            dailyChecks[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const displayField = (fieldName: string) => {
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )
        if (
            dailyChecks?.length > 0 &&
            dailyChecks[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const convertTimeFormat = (time: string) => {
        if (time === null || time === undefined) return ''
        const [hours, minutes, seconds] = time.split(':')
        return `${hours}:${minutes}`
    }

    const handleCancel = () => {
        setOpenTripModal(false)
        setCurrentTrip(false)
    }

    const deleteTripLog = () => {
        setOpenTripModal(false)
        setCurrentTrip(false)
    }

    const refreshTripReport = () => {
        updateTripReport({
            id: tripReport.map((trip: any) => trip.id),
        })
    }

    const initOffline = async () => {
        // getOneClient
        const client = await clientModel.getById(
            localStorage.getItem('clientId') ?? 0,
        )
        setClient(client)
        // getVesselByID(+vesselID, setVessel)
        const vessel = await vesselModel.getById(vesselID)
        setVessel(vessel)
    }
    useEffect(() => {
        if (offline) {
            initOffline()
        }
    }, [offline])
    return (
        <div className="px-2 my-4 flex flex-col gap-2">
            {/*!isWriteMode && ( */}
            <p className={classes.helpText}>
                This section covers the logbook entry. This can be made up of a
                single trip or many over the course of the voyage.
            </p>
            <div className="flex flex-col">
                <div className="w-full overflow-visible">
                    {tripReport ? (
                        <>
                            {tripReport
                                .filter((trip: any) => !trip?.archived)
                                .map((trip: any, index: number) => (
                                    <div key={'triplog-' + index}>
                                        <div
                                            key={`${trip.id}-header`}
                                            onClick={() => {
                                                if (
                                                    selectedTab === trip.id &&
                                                    openTripModal
                                                ) {
                                                    refreshTripReport()
                                                    setSelectedTab(0)
                                                } else {
                                                    setSelectedTab(trip.id)
                                                    setDisplayDangerousGoods(
                                                        trip?.enableDGR ===
                                                            true,
                                                    )
                                                    setDisplayDangerousGoodsSailing(
                                                        trip?.designatedDangerousGoodsSailing ===
                                                            true,
                                                    )
                                                }
                                                if (
                                                    openTripModal &&
                                                    currentTrip.id === trip.id
                                                ) {
                                                    setOpenTripModal(false)
                                                    setCurrentTrip(false)
                                                    return
                                                }
                                                setOpenTripModal(true)
                                                setSignatureKey(uniqueId())
                                                setCurrentTrip(trip)
                                                setRiskBufferEvDgr(
                                                    trip?.dangerousGoodsChecklist,
                                                )
                                                setOpenTripStartRiskAnalysis(
                                                    false,
                                                )
                                                setAllDangerousGoods(false)
                                                setCurrentStopEvent(false)
                                                setCurrentEventTypeEvent(false)
                                                setSelectedRowEvent(false)
                                                setDisplayDangerousGoodsPvpd(
                                                    false,
                                                )
                                                setDisplayDangerousGoodsPvpdSailing(
                                                    null,
                                                )
                                                setAllPVPDDangerousGoods(false)
                                                setSelectedDGRPVPD(false)
                                                setTripReport_Stops(false)
                                            }}
                                            className={`flex flex-row p-4 text-left rounded-md my-4 bg-sllightblue-100 group border border-sllightblue-200 hover:bg-white  items-baseline`}>
                                            <Button
                                                className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''}  group-hover:text-sllightblue-1000 text-left`}
                                                onPress={() => {
                                                    if (
                                                        selectedTab ===
                                                            trip.id &&
                                                        openTripModal
                                                    ) {
                                                        refreshTripReport()
                                                        setSelectedTab(0)
                                                    } else {
                                                        setSelectedTab(trip.id)
                                                        setDisplayDangerousGoods(
                                                            trip?.enableDGR ===
                                                                true,
                                                        )
                                                        setDisplayDangerousGoodsSailing(
                                                            trip?.designatedDangerousGoodsSailing ===
                                                                true,
                                                        )
                                                    }
                                                    if (
                                                        openTripModal &&
                                                        currentTrip.id ===
                                                            trip.id
                                                    ) {
                                                        setOpenTripModal(false)
                                                        setCurrentTrip(false)
                                                        return
                                                    }
                                                    setOpenTripModal(true)
                                                    setSignatureKey(uniqueId())
                                                    setCurrentTrip(trip)
                                                    setRiskBufferEvDgr(
                                                        trip?.dangerousGoodsChecklist,
                                                    )
                                                    setOpenTripStartRiskAnalysis(
                                                        false,
                                                    )
                                                    setAllDangerousGoods(false)
                                                    setCurrentStopEvent(false)
                                                    setCurrentEventTypeEvent(
                                                        false,
                                                    )
                                                    setSelectedRowEvent(false)
                                                    setDisplayDangerousGoodsPvpd(
                                                        false,
                                                    )
                                                    setDisplayDangerousGoodsPvpdSailing(
                                                        null,
                                                    )
                                                    setAllPVPDDangerousGoods(
                                                        false,
                                                    )
                                                    setSelectedDGRPVPD(false)
                                                    setTripReport_Stops(false)
                                                }}>
                                                {trip?.departTime
                                                    ? convertTimeFormat(
                                                          trip?.departTime,
                                                      )
                                                    : 'No depart time'}
                                                <span>&nbsp;</span>
                                                {trip?.fromLocation?.title}
                                                {trip?.fromLocation?.title &&
                                                trip?.toLocation?.title
                                                    ? ' - '
                                                    : ''}
                                                <span>&nbsp;</span>
                                                {trip?.arrive
                                                    ? convertTimeFormat(
                                                          dayjs(
                                                              trip?.arrive,
                                                          ).format('HH:mm'),
                                                      )
                                                    : trip?.arriveTime
                                                      ? convertTimeFormat(
                                                            trip?.arriveTime,
                                                        )
                                                      : 'No arrival time'}
                                                <span>&nbsp;</span>
                                                {trip?.toLocation?.title}
                                                {!trip?.fromLocation?.title &&
                                                    !trip?.toLocation?.title &&
                                                    '-'}
                                                <span>:&nbsp;</span>
                                            </Button>
                                            <span className="text-2xs font-inter">
                                                {trip?.tripEvents?.nodes
                                                    .length > 0 ? (
                                                    <>
                                                        {trip?.tripEvents
                                                            ?.nodes[0]
                                                            ?.eventType?.title
                                                            ? trip?.tripEvents
                                                                  ?.nodes[0]
                                                                  ?.eventType
                                                                  ?.title
                                                            : trip?.tripEvents
                                                                  ?.nodes[0]
                                                                  ?.eventCategory}
                                                        {trip?.tripEvents?.nodes
                                                            .length > 1 && (
                                                            <DialogTrigger>
                                                                <Button className="text-base outline-none px-1">
                                                                    <span className="text-2xs font-inter">
                                                                        {' '}
                                                                        +
                                                                        {trip
                                                                            ?.tripEvents
                                                                            ?.nodes
                                                                            .length -
                                                                            1}{' '}
                                                                        more
                                                                    </span>
                                                                </Button>
                                                                <Popover>
                                                                    <PopoverWrapper>
                                                                        {trip?.tripEvents?.nodes.map(
                                                                            (
                                                                                event: any,
                                                                            ) => (
                                                                                <div
                                                                                    key={
                                                                                        event.id
                                                                                    }
                                                                                    className="flex items-center justify-between">
                                                                                    <span className="text-xs text-gray-500 ">
                                                                                        {event
                                                                                            ?.eventType
                                                                                            ?.title
                                                                                            ? event
                                                                                                  ?.eventType
                                                                                                  ?.title
                                                                                            : event?.eventCategory}
                                                                                    </span>
                                                                                </div>
                                                                            ),
                                                                        )}
                                                                    </PopoverWrapper>
                                                                </Popover>
                                                            </DialogTrigger>
                                                        )}
                                                    </>
                                                ) : (
                                                    'No events'
                                                )}
                                            </span>
                                        </div>
                                        <div
                                            key={`${trip.id}-body`}
                                            className={`${selectedTab === trip.id && currentTrip ? ' ' : 'hidden'} text-left px-3`}>
                                            {currentTrip && (
                                                <>
                                                    <div
                                                        className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} my-4 flex flex-col md:flex-row items-start md:items-center`}>
                                                        <Label
                                                            className={
                                                                classes.label
                                                            }>
                                                            Departure time
                                                        </Label>
                                                        <DepartTime
                                                            offline={offline}
                                                            currentTrip={
                                                                currentTrip
                                                            }
                                                            tripReport={
                                                                tripReport
                                                            }
                                                            templateStyle={''}
                                                            updateTripReport={
                                                                updateTripReport
                                                            }
                                                        />
                                                    </div>
                                                    <div
                                                        className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} my-4 flex flex-col lg:flex-row items-start md:items-start`}>
                                                        <Label
                                                            className={
                                                                classes.label +
                                                                ` md:mt-3`
                                                            }>
                                                            Departure location
                                                        </Label>
                                                        <DepartLocation
                                                            offline={offline}
                                                            geoLocations={
                                                                locations
                                                            }
                                                            currentTrip={
                                                                currentTrip
                                                            }
                                                            tripReport={
                                                                tripReport
                                                            }
                                                            templateStyle={''}
                                                            updateTripReport={
                                                                updateTripReport
                                                            }
                                                        />
                                                    </div>
                                                    <hr className="my-4" />
                                                    <div className="my-4 text-sm font-semibold uppercase">
                                                        People on board
                                                    </div>
                                                    <div
                                                        className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} my-4`}>
                                                        <POB
                                                            offline={offline}
                                                            currentTrip={
                                                                currentTrip
                                                            }
                                                            tripReport={
                                                                tripReport
                                                            }
                                                            vessel={vessel}
                                                            crewMembers={
                                                                crewMembers
                                                            }
                                                            logBookConfig={
                                                                logBookConfig
                                                            }
                                                            setTab={setTab}
                                                            masterTerm={
                                                                client?.masterTerm
                                                            }
                                                            updateTripReport={
                                                                updateTripReport
                                                            }
                                                        />
                                                    </div>
                                                    {displayField(
                                                        'PassengerVehiclePickDrop',
                                                    ) &&
                                                        (displayFieldTripLog(
                                                            'VOB',
                                                        ) ||
                                                            displayField(
                                                                'PassengerVehiclePickDropVehiclePickDrop',
                                                            )) && (
                                                            <>
                                                                <hr className="my-4" />
                                                                <div className="my-4 text-sm font-semibold uppercase">
                                                                    Vehicles on
                                                                    board
                                                                </div>
                                                                <div
                                                                    className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} my-4`}>
                                                                    <VOB
                                                                        offline={
                                                                            offline
                                                                        }
                                                                        currentTrip={
                                                                            currentTrip
                                                                        }
                                                                        tripReport={
                                                                            tripReport
                                                                        }
                                                                        vessel={
                                                                            vessel
                                                                        }
                                                                        crewMembers={
                                                                            crewMembers
                                                                        }
                                                                        logBookConfig={
                                                                            logBookConfig
                                                                        }
                                                                    />
                                                                </div>
                                                                {displayFieldTripLog(
                                                                    'DangerousGoods',
                                                                ) && (
                                                                    <div
                                                                        className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} my-4`}>
                                                                        <DGR
                                                                            offline={
                                                                                offline
                                                                            }
                                                                            locked={
                                                                                locked ||
                                                                                !edit_tripReport
                                                                            }
                                                                            currentTrip={
                                                                                currentTrip
                                                                            }
                                                                            tripReport={
                                                                                tripReport
                                                                            }
                                                                            logBookConfig={
                                                                                logBookConfig
                                                                            }
                                                                            selectedDGR={
                                                                                selectedDGR
                                                                            }
                                                                            setSelectedDGR={
                                                                                setSelectedDGR
                                                                            }
                                                                            members={
                                                                                crewMembers
                                                                            }
                                                                            displayDangerousGoods={
                                                                                displayDangerousGoods
                                                                            }
                                                                            setDisplayDangerousGoods={
                                                                                setDisplayDangerousGoods
                                                                            }
                                                                            displayDangerousGoodsSailing={
                                                                                displayDangerousGoodsSailing
                                                                            }
                                                                            setDisplayDangerousGoodsSailing={
                                                                                setDisplayDangerousGoodsSailing
                                                                            }
                                                                            allDangerousGoods={
                                                                                allDangerousGoods
                                                                            }
                                                                            setAllDangerousGoods={
                                                                                setAllDangerousGoods
                                                                            }
                                                                        />
                                                                    </div>
                                                                )}
                                                            </>
                                                        )}
                                                    <hr className="my-4" />
                                                    <div className="my-4">
                                                        <Events
                                                            offline={offline}
                                                            eventTypes={
                                                                eventTypes
                                                            }
                                                            currentTrip={
                                                                currentTrip
                                                            }
                                                            logBookConfig={
                                                                logBookConfig
                                                            }
                                                            updateTripReport={
                                                                updateTripReport
                                                            }
                                                            locked={locked}
                                                            geoLocations={
                                                                locations
                                                            }
                                                            tripReport={
                                                                tripReport
                                                            }
                                                            crewMembers={
                                                                crewMembers
                                                            }
                                                            masterID={masterID}
                                                            vessel={vessel}
                                                            vessels={vessels}
                                                            setSelectedRow={
                                                                setSelectedRowEvent
                                                            }
                                                            setCurrentEventType={
                                                                setCurrentEventTypeEvent
                                                            }
                                                            setCurrentStop={
                                                                setCurrentStopEvent
                                                            }
                                                            selectedRow={
                                                                selectedRowEvent
                                                            }
                                                            currentEventType={
                                                                currentEventTypeEvent
                                                            }
                                                            currentStop={
                                                                currentStopEvent
                                                            }
                                                            tripReport_Stops={
                                                                tripReport_Stops
                                                            }
                                                            setTripReport_Stops={
                                                                setTripReport_Stops
                                                            }
                                                            displayDangerousGoodsPvpd={
                                                                displayDangerousGoodsPvpd
                                                            }
                                                            setDisplayDangerousGoodsPvpd={
                                                                setDisplayDangerousGoodsPvpd
                                                            }
                                                            displayDangerousGoodsPvpdSailing={
                                                                displayDangerousGoodsPvpdSailing
                                                            }
                                                            setDisplayDangerousGoodsPvpdSailing={
                                                                setDisplayDangerousGoodsPvpdSailing
                                                            }
                                                            allPVPDDangerousGoods={
                                                                allPVPDDangerousGoods
                                                            }
                                                            setAllPVPDDangerousGoods={
                                                                setAllPVPDDangerousGoods
                                                            }
                                                            selectedDGRPVPD={
                                                                selectedDGRPVPD
                                                            }
                                                            setSelectedDGRPVPD={
                                                                setSelectedDGRPVPD
                                                            }
                                                            fuelLogs={fuelLogs}
                                                        />
                                                    </div>
                                                    <hr className="my-4" />
                                                    <div
                                                        className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} my-4 flex flex-col lg:flex-row items-start lg:items-center`}>
                                                        <Label
                                                            className={
                                                                classes.label
                                                            }>
                                                            Arrival location
                                                        </Label>
                                                        <ExpectedDestination
                                                            offline={offline}
                                                            geoLocations={
                                                                locations
                                                            }
                                                            currentTrip={
                                                                currentTrip
                                                            }
                                                            tripReport={
                                                                tripReport
                                                            }
                                                            templateStyle={
                                                                'events'
                                                            }
                                                            updateTripReport={
                                                                updateTripReport
                                                            }
                                                        />
                                                    </div>
                                                    <div
                                                        className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} my-4`}>
                                                        <ExpectedArrival
                                                            offline={offline}
                                                            currentTrip={
                                                                currentTrip
                                                            }
                                                            tripReport={
                                                                tripReport
                                                            }
                                                            updateTripReport={
                                                                updateTripReport
                                                            }
                                                        />
                                                    </div>
                                                    <div
                                                        className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} my-4`}>
                                                        <ActualArrival
                                                            offline={offline}
                                                            currentTrip={
                                                                currentTrip
                                                            }
                                                            tripReport={
                                                                tripReport
                                                            }
                                                            updateTripReport={
                                                                updateTripReport
                                                            }
                                                        />
                                                    </div>
                                                    <div
                                                        className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} my-4`}>
                                                        <TripComments
                                                            offline={offline}
                                                            currentTrip={
                                                                currentTrip
                                                            }
                                                            tripReport={
                                                                tripReport
                                                            }
                                                            setCommentField={
                                                                setComment
                                                            }
                                                            updateTripReport={
                                                                updateTripReport
                                                            }
                                                        />
                                                    </div>
                                                    <div
                                                        className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} my-4`}>
                                                        <Master
                                                            offline={offline}
                                                            currentTrip={
                                                                currentTrip
                                                            }
                                                            tripReport={
                                                                tripReport
                                                            }
                                                            crewMembers={
                                                                crewMembers
                                                            }
                                                            updateTripReport={
                                                                updateTripReport
                                                            }
                                                        />
                                                    </div>
                                                    <div className="my-4 flex flex-col w-full">
                                                        <label
                                                            className={`${classes.label} ml-2 block font-semibold`}>
                                                            Signature
                                                            Confirmation
                                                        </label>
                                                        <p className="text-xs font-inter leading-loose text-left px-2">
                                                            By signing below, I
                                                            confirm that the
                                                            recorded entries are
                                                            accurate to the best
                                                            of my knowledge and
                                                            in accordance with
                                                            the vessel's
                                                            operating procedures
                                                            and regulations.
                                                        </p>
                                                        <SignaturePad
                                                            key={`${signatureKey}-${trip.id}`}
                                                            locked={locked}
                                                            signature={
                                                                trip.sectionSignature
                                                            }
                                                            onSignatureChanged={(
                                                                sign: String,
                                                            ) => {
                                                                setSignature(
                                                                    sign,
                                                                )
                                                            }}
                                                        />
                                                    </div>
                                                    <div className="flex justify-end">
                                                        <button
                                                            className="mr-2"
                                                            onClick={
                                                                handleCancel
                                                            }>
                                                            Cancel
                                                        </button>
                                                        {/*<DialogTrigger>
                                                            <SeaLogsButton
                                                                type="secondary"
                                                                color="slred"
                                                                icon="trash"
                                                                text="Delete"
                                                                className="!mr-0"
                                                            />
                                                            <ModalOverlay
                                                                className={({
                                                                    isEntering,
                                                                    isExiting,
                                                                }) => `
                                                                    fixed inset-0 z-10 overflow-y-auto bg-black/25 flex min-h-full items-center justify-center p-4 text-center backdrop-blur
                                                                    ${isEntering ? 'animate-in fade-in duration-300 ease-out' : ''}
                                                                    ${isExiting ? 'animate-out fade-out duration-200 ease-in' : ''}
                                                                `}>
                                                                <Modal
                                                                    className={({
                                                                        isEntering,
                                                                        isExiting,
                                                                    }) => `
                                                                        w-full max-w-md overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl
                                                                        ${isEntering ? 'animate-in zoom-in-95 ease-out duration-300' : ''}
                                                                        ${isExiting ? 'animate-out zoom-out-95 ease-in duration-200' : ''}
                                                                    `}>
                                                                    <Dialog
                                                                        role="alertdialog"
                                                                        className="outline-none relative">
                                                                        {({
                                                                            close,
                                                                        }) => (
                                                                            <div className="flex justify-center flex-col px-6 py-6">
                                                                                <Heading
                                                                                    slot="title"
                                                                                    className="text-2xl font-light leading-6 my-2 text-sldarkblue-800">
                                                                                    Delete
                                                                                    Trip
                                                                                    Log
                                                                                    Data
                                                                                </Heading>
                                                                                <p className="mt-3 text-slate-500">
                                                                                    Are
                                                                                    you
                                                                                    sure
                                                                                    you
                                                                                    want
                                                                                    to
                                                                                    delete
                                                                                    the
                                                                                    trip
                                                                                    log
                                                                                    data
                                                                                    ?
                                                                                </p>
                                                                                <hr className="my-6" />
                                                                                <div className="flex justify-end">
                                                                                    <Button
                                                                                        className="mr-8 text-sm text-gray-600 hover:text-gray-600"
                                                                                        onPress={
                                                                                            close
                                                                                        }>
                                                                                        Cancel
                                                                                    </Button>
                                                                                    <Button
                                                                                        type="button"
                                                                                        className="group inline-flex justify-center items-center rounded-md bg-sky-700 px-4 py-2 text-sm text-white shadow-sm hover:bg-white hover:text-sky-800 ring-1 ring-sky-700"
                                                                                        onPress={() => {
                                                                                            close()
                                                                                            deleteTripLog()
                                                                                        }}>
                                                                                        <svg
                                                                                            className="-ml-0.5 mr-1.5 h-5 w-5 border rounded-full bg-sky-300 group-hover:bg-sky-700 group-hover:text-white"
                                                                                            viewBox="0 0 20 20"
                                                                                            fill="currentColor"
                                                                                            aria-hidden="true">
                                                                                            <path
                                                                                                fillRule="evenodd"
                                                                                                d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                                                                                clipRule="evenodd"></path>
                                                                                        </svg>
                                                                                        Delete
                                                                                    </Button>
                                                                                </div>
                                                                            </div>
                                                                        )}
                                                                    </Dialog>
                                                                </Modal>
                                                            </ModalOverlay>
                                                        </DialogTrigger>*/}
                                                        <SeaLogsButton
                                                            type="primary"
                                                            icon="check"
                                                            text={'Update'}
                                                            color="sky"
                                                            action={handleSave}
                                                        />
                                                    </div>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                ))}
                        </>
                    ) : (
                        <div className="flex justify-center items-center h-32">
                            <p className="">Start by adding a trip</p>
                        </div>
                    )}
                </div>
            </div>
            <div className="flex justify-end">
                <button
                    type="button"
                    className={`w-48 text-sm font-semibold text-slorange-1000 bg-slorange-300 border px-4 py-2 border-transparent rounded-md shadow-sm ring-1 ring-inset ring-slorange-1000 hover:ring-sldarkblue-1000 hover:bg-sldarkblue-1000 hover:text-white ${locked || !edit_tripReport ? 'pointer-events-none' : ''}`}
                    onClick={handleAddTrip}>
                    Add Trip
                </button>
            </div>
            <AlertDialog
                openDialog={openTripSelectionDialog}
                setOpenDialog={setOpenTripSelectionDialog}>
                <div className="bg-slblue-1000 -m-6 rounded-t-lg">
                    <Heading className="text-xl text-white font-medium p-6">
                        Select a Trip
                    </Heading>
                </div>
                <div className="my-4">
                    <div className="mt-6">
                        <div className="flex justify-end">
                            <SeaLogsButton
                                action={handleCustomTrip}
                                text="Create Custom Trip"
                                type="primary"
                            />
                        </div>
                    </div>
                    <div className="mt-6">
                        <Select
                            id="trip-schedule-services-list"
                            closeMenuOnSelect={true}
                            options={tripScheduleServices}
                            menuPlacement="top"
                            onChange={(e: any) => {
                                if (e) {
                                    setSelectedTripScheduleServiceID(e.value)
                                    loadTripReportSchedules(e.value)
                                } else {
                                    setSelectedTripScheduleServiceID(null)
                                    setTripReportSchedules([])
                                    setShowNextTrips(false)
                                }
                            }}
                            isClearable={true}
                            placeholder="Select Trip Schedule Service"
                            className={classes.selectMain}
                            classNames={{
                                control: () =>
                                    classes.selectControl + ' !min-w-48',
                                singleValue: () => classes.selectSingleValue,
                                menu: () => classes.selectMenu,
                                option: () => classes.selectOption,
                            }}
                        />
                    </div>
                    {selectedTripScheduleServiceID && (
                        <div className={`flex items-center my-4 w-full`}>
                            <Label
                                className={`relative flex items-center pr-3 rounded-full cursor-pointer`}
                                htmlFor="client-use-department"
                                data-ripple="true"
                                data-ripple-color="dark"
                                data-ripple-dark="true">
                                <Input
                                    type="checkbox"
                                    id="client-use-department"
                                    className="before:content[''] peer relative h-5 w-5 cursor-pointer p-3 appearance-none rounded-full border border-sky-400 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-sky-500 before:opacity-0 before:transition-opacity checked:border-sky-700 checked:bg-sky-700 before:bg-sky-700 hover:before:opacity-10"
                                    checked={showNextTrips}
                                    onChange={(e: any) => {
                                        setShowNextTrips(e.target.checked)
                                        loadTripReportSchedules(
                                            selectedTripScheduleServiceID,
                                        )
                                    }}
                                />
                                <span className="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-1/3 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100"></span>
                                <span className="ml-3 text-sm font-semibold uppercase">
                                    Show next trips
                                </span>
                            </Label>
                        </div>
                    )}
                    {tripReportSchedules.length > 0 ? (
                        <TableWrapper
                            headings={[
                                'Depart Time',
                                'Depart Location',
                                'Arrival Time',
                                'Destination',
                                '',
                            ]}
                            showHeader={true}>
                            {tripReportSchedules.map((trs: any) => (
                                <tr
                                    key={trs.id}
                                    className={`p-3 align-middle text-sm text-left border-b border-slblue-100 even:bg-sllightblue-50/50 hover:bg-sllightblue-50 `}>
                                    <td>{trs.departTime}</td>
                                    <td>{trs.fromLocation.title}</td>
                                    <td>{trs.arriveTime}</td>
                                    <td>{trs.toLocation.title}</td>
                                    <td className="flex items-center">
                                        <button
                                            onClick={() =>
                                                handleSelectTripReportSchedule(
                                                    trs,
                                                )
                                            }
                                            className=" text-white bg-slblue-700 hover:bg-slblue-800 focus:outline-none focus:ring-4 focus:ring-slblue-300 rounded-full text-sm p-2.5 text-center ">
                                            <PlusIcon className="w-2 sm:w-4" />
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </TableWrapper>
                    ) : (
                        <div className="text-center p-10 my-10">
                            {/* There are no available timetables to select from. */}
                            {readTripReportSchedulesLoading ? (
                                <Loading />
                            ) : (
                                'Please select a schedule from the dropdown or create a custom trip.'
                            )}
                        </div>
                    )}
                </div>
                <div>
                    <div className="flex justify-end my-4 px-4">
                        <SeaLogsButton
                            action={() => setOpenTripSelectionDialog(false)}
                            text="Close"
                            type="text"
                        />
                    </div>
                </div>
            </AlertDialog>
            <Toaster position="top-right" />
        </div>
    )
}

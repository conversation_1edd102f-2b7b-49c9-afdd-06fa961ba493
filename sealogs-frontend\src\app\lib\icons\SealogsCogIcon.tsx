'use client'

import React from 'react'
import { LucideProps } from 'lucide-react'
import { forwardRef } from 'react'

// Create a component that matches the LucideIcon type
export const SealogsCogIcon = forwardRef<SVGSVGElement, LucideProps>(
    (
        {
            color = 'currentColor',
            size = 24,
            strokeWidth = 0,
            className = '',
            ...props
        },
        ref,
    ) => {
        return (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width={size}
                height={size}
                viewBox="0 0 24 24"
                fill="none"
                stroke={color}
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                strokeLinejoin="round"
                className={className}
                ref={ref}
                {...props}>
                <g transform="translate(0 0)">
                    <path
                        d="M.345,10.866a.1.1,0,0,0-.095.095V13.35a.1.1,0,0,0,.095.095H2.382a9.822,9.822,0,0,0,1.952,4.709L2.893,19.595a.1.1,0,0,0,0,.134l1.689,1.689a.1.1,0,0,0,.134,0l1.442-1.441a9.821,9.821,0,0,0,4.709,1.952v2.037a.1.1,0,0,0,.095.095H13.35a.1.1,0,0,0,.095-.095V21.928a9.821,9.821,0,0,0,4.709-1.952L19.6,21.418a.1.1,0,0,0,.134,0l1.689-1.689a.1.1,0,0,0,0-.134l-1.441-1.442a9.822,9.822,0,0,0,1.952-4.709h2.037a.1.1,0,0,0,.095-.095V10.961a.1.1,0,0,0-.095-.095H21.929a9.822,9.822,0,0,0-1.952-4.709l1.441-1.442a.094.094,0,0,0,0-.134L19.729,2.893a.094.094,0,0,0-.134,0L18.153,4.334a9.825,9.825,0,0,0-4.709-1.952V.345A.1.1,0,0,0,13.35.25H10.961a.1.1,0,0,0-.095.095V2.382A9.825,9.825,0,0,0,6.157,4.334L4.715,2.893a.094.094,0,0,0-.134,0L2.893,4.582a.094.094,0,0,0,0,.134L4.334,6.157a9.822,9.822,0,0,0-1.952,4.709Zm2.215.106A9.634,9.634,0,0,1,4.535,6.207a.1.1,0,0,0-.008-.125L3.094,4.648,4.649,3.094,6.082,4.527a.1.1,0,0,0,.125.008,9.632,9.632,0,0,1,4.765-1.975.1.1,0,0,0,.083-.094V.44h2.2V2.465a.1.1,0,0,0,.083.094A9.632,9.632,0,0,1,18.1,4.535a.1.1,0,0,0,.125-.008l1.433-1.433,1.555,1.555L19.784,6.082a.1.1,0,0,0-.008.125,9.634,9.634,0,0,1,1.975,4.765.1.1,0,0,0,.094.083h2.026v2.2H21.845a.1.1,0,0,0-.094.083A9.637,9.637,0,0,1,19.776,18.1a.1.1,0,0,0,.008.125l1.433,1.433-1.555,1.555-1.433-1.433a.094.094,0,0,0-.125-.008,9.639,9.639,0,0,1-4.765,1.975.1.1,0,0,0-.083.094v2.025h-2.2V21.845a.1.1,0,0,0-.083-.094,9.639,9.639,0,0,1-4.765-1.975.1.1,0,0,0-.125.008L4.649,21.216,3.094,19.662l1.433-1.433a.1.1,0,0,0,.008-.125,9.637,9.637,0,0,1-1.975-4.765.1.1,0,0,0-.094-.083H.44v-2.2H2.465A.1.1,0,0,0,2.559,10.972Z"
                        transform="translate(-0.155 -0.155)"
                        fill={color === 'currentColor' ? '#f5f7fa' : color}
                    />
                    <path
                        d="M.345,10.866a.1.1,0,0,0-.095.095V13.35a.1.1,0,0,0,.095.095H2.382a9.822,9.822,0,0,0,1.952,4.709L2.893,19.595a.1.1,0,0,0,0,.134l1.689,1.689a.1.1,0,0,0,.134,0l1.442-1.441a9.821,9.821,0,0,0,4.709,1.952v2.037a.1.1,0,0,0,.095.095H13.35a.1.1,0,0,0,.095-.095V21.928a9.821,9.821,0,0,0,4.709-1.952L19.6,21.418a.1.1,0,0,0,.134,0l1.689-1.689a.1.1,0,0,0,0-.134l-1.441-1.442a9.822,9.822,0,0,0,1.952-4.709h2.037a.1.1,0,0,0,.095-.095V10.961a.1.1,0,0,0-.095-.095H21.929a9.822,9.822,0,0,0-1.952-4.709l1.441-1.442a.094.094,0,0,0,0-.134L19.729,2.893a.094.094,0,0,0-.134,0L18.153,4.334a9.825,9.825,0,0,0-4.709-1.952V.345A.1.1,0,0,0,13.35.25H10.961a.1.1,0,0,0-.095.095V2.382A9.825,9.825,0,0,0,6.157,4.334L4.715,2.893a.094.094,0,0,0-.134,0L2.893,4.582a.094.094,0,0,0,0,.134L4.334,6.157a9.822,9.822,0,0,0-1.952,4.709Zm2.215.106A9.634,9.634,0,0,1,4.535,6.207a.1.1,0,0,0-.008-.125L3.094,4.648,4.649,3.094,6.082,4.527a.1.1,0,0,0,.125.008,9.632,9.632,0,0,1,4.765-1.975.1.1,0,0,0,.083-.094V.44h2.2V2.465a.1.1,0,0,0,.083.094A9.632,9.632,0,0,1,18.1,4.535a.1.1,0,0,0,.125-.008l1.433-1.433,1.555,1.555L19.784,6.082a.1.1,0,0,0-.008.125,9.634,9.634,0,0,1,1.975,4.765.1.1,0,0,0,.094.083h2.026v2.2H21.845a.1.1,0,0,0-.094.083A9.637,9.637,0,0,1,19.776,18.1a.1.1,0,0,0,.008.125l1.433,1.433-1.555,1.555-1.433-1.433a.094.094,0,0,0-.125-.008,9.639,9.639,0,0,1-4.765,1.975.1.1,0,0,0-.083.094v2.025h-2.2V21.845a.1.1,0,0,0-.083-.094,9.639,9.639,0,0,1-4.765-1.975.1.1,0,0,0-.125.008L4.649,21.216,3.094,19.662l1.433-1.433a.1.1,0,0,0,.008-.125,9.637,9.637,0,0,1-1.975-4.765.1.1,0,0,0-.094-.083H.44v-2.2H2.465A.1.1,0,0,0,2.559,10.972Z"
                        transform="translate(-0.155 -0.155)"
                        fill="none"
                        stroke="#52606d"
                        strokeMiterlimit="10"
                        strokeWidth="0.5"
                    />
                    <path
                        d="M21.03,27.517a6.486,6.486,0,1,0-6.486-6.486,6.494,6.494,0,0,0,6.486,6.486"
                        transform="translate(-9.03 -9.03)"
                        fill={color === 'currentColor' ? '#e3f8ff' : color}
                    />
                    <path
                        d="M21.03,27.517a6.486,6.486,0,1,0-6.486-6.486A6.494,6.494,0,0,0,21.03,27.517Zm0-12.783a6.3,6.3,0,1,1-6.3,6.3A6.3,6.3,0,0,1,21.03,14.734Z"
                        transform="translate(-9.03 -9.03)"
                        fill="none"
                        stroke="#52606d"
                        strokeMiterlimit="10"
                        strokeWidth="0.5"
                    />
                    <path
                        d="M27.077,29.872a2.794,2.794,0,1,0-2.794-2.794,2.8,2.8,0,0,0,2.794,2.794"
                        transform="translate(-15.077 -15.077)"
                        fill={color === 'currentColor' ? '#fff' : color}
                    />
                    <path
                        d="M27.077,29.872a2.794,2.794,0,1,0-2.794-2.794A2.8,2.8,0,0,0,27.077,29.872Zm0-5.4a2.6,2.6,0,1,1-2.6,2.6A2.608,2.608,0,0,1,27.077,24.473Z"
                        transform="translate(-15.077 -15.077)"
                        fill="none"
                        stroke="#52606d"
                        strokeMiterlimit="10"
                        strokeWidth="0.5"
                    />
                </g>
            </svg>
        )
    },
)

SealogsCogIcon.displayName = 'SealogsCogIcon'

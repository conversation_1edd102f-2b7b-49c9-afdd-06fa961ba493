'use client'

import * as React from 'react'
import { MoreHorizontal } from 'lucide-react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import Link from 'next/link'
import { useSidebar } from '@/components/ui/sidebar'
import ExportButton from '@/app/ui/reporting/export-button'
import { useEffect, useState } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { useLazyQuery } from '@apollo/client'
import { GET_MAINTENANCE_CHECK, GET_CREW_BY_IDS } from '@/app/lib/graphQL/query'
import { getVesselList } from '@/app/lib/actions'
import { isOverDueTask } from '@/app/lib/actions'
import { isEmpty, trim } from 'lodash'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { dueStatusLabel } from '@/app/ui/reporting/maintenance-status-activity-report'
import toast from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import { SealogsCogIcon } from '@/app/lib/icons'

export const MaintenanceFilterActions = () => {
    const { isMobile } = useSidebar()
    const [maintenanceChecks, setMaintenanceChecks] = useState<any>()
    const [vessels, setVessels] = useState<any>()
    const [crewInfo, setCrewInfo] = useState<any>()
    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const router = useRouter()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const [queryMaintenanceChecks] = useLazyQuery(GET_MAINTENANCE_CHECK, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readComponentMaintenanceChecks.nodes
            if (data) {
                handleSetMaintenanceChecks(data)
            }
        },
        onError: (error: any) => {
            console.error('queryMaintenanceChecks error', error)
        },
    })
    useEffect(() => {
        if (isLoading) {
            loadMaintenanceChecks()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadMaintenanceChecks = async (
        searchFilter: SearchFilter = { ...filter },
        searchkeywordFilter: any = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            const promises = searchkeywordFilter.map(
                async (keywordFilter: any) => {
                    return await queryMaintenanceChecks({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            // filter out empty results
            responses = responses.filter(
                (r: any) =>
                    r.data.readComponentMaintenanceChecks.nodes.length > 0,
            )
            // flatten results
            responses = responses.flatMap(
                (r: any) => r.data.readComponentMaintenanceChecks.nodes,
            )
            // filter out duplicates
            responses = responses.filter(
                (value: any, index: any, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            handleSetMaintenanceChecks(responses)
        } else {
            await queryMaintenanceChecks({
                variables: {
                    filter: searchFilter,
                },
            })
        }
    }
    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        const appendedData = activeVessels.map((item: any) => ({
            ...item,
        }))
        appendedData.push({ title: 'Other', id: 0 })
        setVessels(appendedData)
    }

    getVesselList(handleSetVessels)

    const handleSetMaintenanceChecks = (tasks: any) => {
        const activeTasks = tasks
            .filter((task: any) => task.archived === false)
            .map((task: any) => ({
                ...task,
                isOverDue: isOverDueTask(task),
            }))
        setMaintenanceChecks(activeTasks)
        const appendedData: number[] = Array.from(
            new Set(
                activeTasks
                    .filter((item: any) => item.assignedToID > 0)
                    .map((item: any) => item.assignedToID),
            ),
        )
        loadCrewMemberInfo(appendedData)
    }

    const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                setCrewInfo(data)
            }
        },
        onError: (error) => {
            console.error('queryCrewMemberInfo error', error)
        },
    })
    const loadCrewMemberInfo = async (crewId: any) => {
        await queryCrewMemberInfo({
            variables: {
                crewMemberIDs: crewId.length > 0 ? crewId : [0],
            },
        })
    }
    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        if (type === 'vessel') {
            if (data) {
                searchFilter.basicComponentID = { eq: +data.value }
            } else {
                delete searchFilter.basicComponentID
            }
        }
        let keyFilter = keywordFilter
        if (type === 'keyword') {
            if (!isEmpty(trim(data.value))) {
                keyFilter = [
                    { name: { contains: data.value } },
                    { comments: { contains: data.value } },
                    { workOrderNumber: { contains: data.value } },
                ]
            } else {
                keyFilter = []
            }
        }
        if (type === 'status') {
            if (data) {
                searchFilter.status = { eq: data.value }
            } else {
                delete searchFilter.status
            }
        }
        if (type === 'member') {
            if (data) {
                searchFilter.assignedToID = { eq: +data.value }
            } else {
                delete searchFilter.assignedToID
            }
        }
        if (type === 'dateRange') {
            if (data.startDate && data.endDate) {
                searchFilter.expires = {
                    gte: data.startDate,
                    lte: data.endDate,
                }
            } else {
                delete searchFilter.expires
            }
        }
        if (type === 'category') {
            if (data) {
                searchFilter.maintenanceCategoryID = { eq: +data.value }
            } else {
                delete searchFilter.maintenanceCategoryID
            }
        }
        setFilter(searchFilter)
        setKeywordFilter(keyFilter)
        loadMaintenanceChecks(searchFilter, keyFilter)
    }

    const downloadCsv = () => {
        if ((maintenanceChecks && vessels) === false) {
            return
        }

        const csvEntries: string[][] = [
            ['task', 'location', 'assigned to', 'due'],
        ]

        maintenanceChecks
            .filter(
                (maintenanceCheck: any) =>
                    maintenanceCheck.status !== 'Save_As_Draft',
            )
            .forEach((maintenanceCheck: any) => {
                csvEntries.push([
                    maintenanceCheck.name,
                    vessels
                        ?.filter(
                            (vessel: any) =>
                                vessel?.id == maintenanceCheck.basicComponentID,
                        )
                        .map((vessel: any) => vessel.title)
                        .join(', '),
                    crewInfo
                        .filter(
                            (crew: any) =>
                                crew.id === maintenanceCheck.assignedToID,
                        )
                        .map((crew: any, index: number) => {
                            return `${crew.firstName} ${crew.surname}`
                        })
                        .join(', '),
                    dueStatusLabel(maintenanceCheck.isOverDue),
                ])
            })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if ((maintenanceChecks && vessels) === false) {
            return
        }

        const headers: any = [['Task Name', 'Location', 'Assigned To', 'Due']]

        const body: any = maintenanceChecks
            .filter(
                (maintenanceCheck: any) =>
                    maintenanceCheck.status !== 'Save_As_Draft',
            )
            .map((maintenanceCheck: any) => {
                return [
                    maintenanceCheck.name,
                    vessels
                        ?.filter(
                            (vessel: any) =>
                                vessel?.id == maintenanceCheck.basicComponentID,
                        )
                        .map((vessel: any) => vessel.title)
                        .join(', '),
                    crewInfo
                        .filter(
                            (crew: any) =>
                                crew.id === maintenanceCheck.assignedToID,
                        )
                        .map((crew: any, index: number) => {
                            return `${crew.firstName} ${crew.surname}`
                        })
                        .join(', '),
                    dueStatusLabel(maintenanceCheck.isOverDue),
                ]
            })

        exportPdfTable({
            headers,
            body,
        })
    }

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                <DropdownMenuItem onClick={downloadCsv}>
                    Download CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={downloadPdf}>
                    Download PDF
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    onClick={() => {
                        if (!edit_task) {
                            toast.error(
                                'You do not have permission to edit this section',
                            )
                            return
                        }
                        router.push('/maintenance/new?redirectTo=/maintenance')
                    }}>
                    New Task
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

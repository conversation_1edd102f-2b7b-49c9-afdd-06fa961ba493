"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/crew/allocated-tasks.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleBottomCenterTextIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.3.1/node_modules/@heroicons/react/24/outline/esm/ChatBubbleBottomCenterTextIcon.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst CrewAllocatedTasks = (param)=>{\n    let { taskList } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [filteredTaskList, setFilteredTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(taskList);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        vessel: null,\n        status: null,\n        keyword: null\n    });\n    // Update filtered list when taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        setFilteredTaskList(taskList);\n    }, [\n        taskList\n    ]);\n    // Apply all filters whenever filters or taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (!taskList) return;\n        let filtered = [\n            ...taskList\n        ];\n        // Apply vessel filter - following the same pattern as maintenance list\n        if (filters.vessel) {\n            // Handle both single and multi-select vessel filtering\n            let vesselIds = [];\n            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {\n                vesselIds = filters.vessel.map((item)=>+item.value);\n            } else if (filters.vessel && !Array.isArray(filters.vessel)) {\n                vesselIds = [\n                    +filters.vessel.value\n                ];\n            }\n            if (vesselIds.length > 0) {\n                filtered = filtered.filter((task)=>{\n                    var _task_basicComponent;\n                    return vesselIds.includes(task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.id);\n                });\n            }\n        }\n        // Apply status filter\n        if (filters.status) {\n            filtered = filtered.filter((task)=>(task === null || task === void 0 ? void 0 : task.status) === filters.status.value);\n        }\n        // Apply keyword filter\n        if (filters.keyword && filters.keyword.value) {\n            const keyword = filters.keyword.value.toLowerCase();\n            filtered = filtered.filter((task)=>{\n                var _task_name, _task_description, _task_comments;\n                return (task === null || task === void 0 ? void 0 : (_task_name = task.name) === null || _task_name === void 0 ? void 0 : _task_name.toLowerCase().includes(keyword)) || (task === null || task === void 0 ? void 0 : (_task_description = task.description) === null || _task_description === void 0 ? void 0 : _task_description.toLowerCase().includes(keyword)) || (task === null || task === void 0 ? void 0 : (_task_comments = task.comments) === null || _task_comments === void 0 ? void 0 : _task_comments.toLowerCase().includes(keyword));\n            });\n        }\n        setFilteredTaskList(filtered);\n    }, [\n        taskList,\n        filters\n    ]);\n    // Handle filter changes\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        setFilters((prev)=>({\n                ...prev,\n                [type]: data\n            }));\n    };\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: \"Task\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2, _task_isOverDue3, _task_isOverDue4, _task_isOverDue5, _task_isOverDue6, _task_isOverDue7, _task_isOverDue8, _task_isOverDue9, _task_isOverDue10, _task_isOverDue11, _task_isOverDue12, _task_isOverDue13, _task_isOverDue14, _task_isOverDue15, _task_isOverDue16, _task_isOverDue17, _task_isOverDue18, _task_isOverDue19;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-foreground flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(task.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: \"focus:outline-none\",\n                                            children: task.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block rounded px-3 py-1 ml-3 \".concat((task === null || task === void 0 ? void 0 : (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\" ? \"alert\" : \"\", \" \").concat((task === null || task === void 0 ? void 0 : (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) === \"Low\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.status) === \"Upcoming\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status) === \"Completed\" ? \"success\" : \"\", \" \").concat((task === null || task === void 0 ? void 0 : (_task_isOverDue4 = task.isOverDue) === null || _task_isOverDue4 === void 0 ? void 0 : _task_isOverDue4.status) === \"Medium\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue5 = task.isOverDue) === null || _task_isOverDue5 === void 0 ? void 0 : _task_isOverDue5.days) === \"Save As Draft\" ? \"text-yellow-600 bg-yellow-100\" : \"\"),\n                                            children: [\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue6 = task.isOverDue) === null || _task_isOverDue6 === void 0 ? void 0 : _task_isOverDue6.status) && [\n                                                    \"High\",\n                                                    \"Medium\",\n                                                    \"Low\"\n                                                ].includes(task.isOverDue.status) && (task === null || task === void 0 ? void 0 : (_task_isOverDue7 = task.isOverDue) === null || _task_isOverDue7 === void 0 ? void 0 : _task_isOverDue7.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue8 = task.isOverDue) === null || _task_isOverDue8 === void 0 ? void 0 : _task_isOverDue8.status) === \"Completed\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue9 = task.isOverDue) === null || _task_isOverDue9 === void 0 ? void 0 : _task_isOverDue9.days) === \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue10 = task.isOverDue) === null || _task_isOverDue10 === void 0 ? void 0 : _task_isOverDue10.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue11 = task.isOverDue) === null || _task_isOverDue11 === void 0 ? void 0 : _task_isOverDue11.status) === \"Upcoming\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue12 = task.isOverDue) === null || _task_isOverDue12 === void 0 ? void 0 : _task_isOverDue12.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue13 = task.isOverDue) === null || _task_isOverDue13 === void 0 ? void 0 : _task_isOverDue13.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue14 = task.isOverDue) === null || _task_isOverDue14 === void 0 ? void 0 : _task_isOverDue14.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue15 = task.isOverDue) === null || _task_isOverDue15 === void 0 ? void 0 : _task_isOverDue15.status),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue16 = task.isOverDue) === null || _task_isOverDue16 === void 0 ? void 0 : _task_isOverDue16.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue17 = task.isOverDue) === null || _task_isOverDue17 === void 0 ? void 0 : _task_isOverDue17.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue18 = task.isOverDue) === null || _task_isOverDue18 === void 0 ? void 0 : _task_isOverDue18.days) !== \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue19 = task.isOverDue) === null || _task_isOverDue19 === void 0 ? void 0 : _task_isOverDue19.days)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-14 flex items-center pl-1\",\n                                    children: task.Comments !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"outline-none px-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                                                className: \"w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"leading-loose\",\n                                                    children: task.comments\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 25\n                        }, undefined),\n                        task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1 text-sm text-muted-foreground\",\n                            children: task.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: \"Vessel\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_basicComponent;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: (task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.title) || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"inventoryItem\",\n            header: \"Inventory Item\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_inventory, _task_maintenanceSchedule_inventory, _task_maintenanceSchedule;\n                const task = row.original;\n                // Check for inventory from multiple possible sources\n                const inventoryItem = (task === null || task === void 0 ? void 0 : (_task_inventory = task.inventory) === null || _task_inventory === void 0 ? void 0 : _task_inventory.item) || (task === null || task === void 0 ? void 0 : (_task_maintenanceSchedule = task.maintenanceSchedule) === null || _task_maintenanceSchedule === void 0 ? void 0 : (_task_maintenanceSchedule_inventory = _task_maintenanceSchedule.inventory) === null || _task_maintenanceSchedule_inventory === void 0 ? void 0 : _task_maintenanceSchedule_inventory.item) || null;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: inventoryItem || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-right\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground mb-1\",\n                            children: [\n                                \"Due: \",\n                                (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(task.expires)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block rounded px-3 py-1 \".concat(task.status == \"Completed\" ? \"success\" : \"alert\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: task.status\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: !taskList ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 231,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n            columns: columns,\n            data: filteredTaskList || [],\n            showToolbar: true,\n            pageSize: 50,\n            showPageSizeSelector: false,\n            onChange: handleFilterOnChange\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 233,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s(CrewAllocatedTasks, \"ma6jPe/tw3b936HXYaWPMLLamsk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams\n    ];\n});\n_c = CrewAllocatedTasks;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewAllocatedTasks);\nvar _c;\n$RefreshReg$(_c, \"CrewAllocatedTasks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\n"));

/***/ })

});
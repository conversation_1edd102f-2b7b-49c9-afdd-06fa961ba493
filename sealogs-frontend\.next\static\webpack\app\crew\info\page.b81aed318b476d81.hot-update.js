"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/crew/allocated-tasks.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleBottomCenterTextIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.3.1/node_modules/@heroicons/react/24/outline/esm/ChatBubbleBottomCenterTextIcon.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst CrewAllocatedTasks = (param)=>{\n    let { taskList } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [filteredTaskList, setFilteredTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(taskList);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        vessel: null,\n        status: null,\n        keyword: null\n    });\n    // Get crew member ID from URL params for debugging\n    const crewId = searchParams.get(\"id\");\n    // Debug crew member filtering\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (taskList && taskList.length > 0) {\n            var _taskList_;\n            console.log(\"Crew ID from URL: \".concat(crewId));\n            console.log(\"Total tasks received: \".concat(taskList.length));\n            console.log(\"Sample task assignedToID:\", (_taskList_ = taskList[0]) === null || _taskList_ === void 0 ? void 0 : _taskList_.assignedToID);\n            console.log(\"All unique assignedToIDs:\", [\n                ...new Set(taskList.map((t)=>t.assignedToID))\n            ]);\n        }\n    }, [\n        taskList,\n        crewId\n    ]);\n    // Update filtered list when taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        setFilteredTaskList(taskList);\n    }, [\n        taskList\n    ]);\n    // Apply all filters whenever filters or taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (!taskList) return;\n        let filtered = [\n            ...taskList\n        ];\n        // Apply vessel filter - following the same pattern as maintenance list\n        if (filters.vessel) {\n            // Handle both single and multi-select vessel filtering\n            let vesselIds = [];\n            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {\n                vesselIds = filters.vessel.map((item)=>String(item.value));\n            } else if (filters.vessel && !Array.isArray(filters.vessel)) {\n                vesselIds = [\n                    String(filters.vessel.value)\n                ];\n            }\n            if (vesselIds.length > 0) {\n                filtered = filtered.filter((task)=>{\n                    var _task_basicComponent;\n                    const taskVesselId = String(task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.id);\n                    return vesselIds.includes(taskVesselId);\n                });\n            }\n        }\n        // Apply status filter\n        if (filters.status) {\n            filtered = filtered.filter((task)=>(task === null || task === void 0 ? void 0 : task.status) === filters.status.value);\n        }\n        // Apply keyword filter\n        if (filters.keyword && filters.keyword.value && filters.keyword.value.trim()) {\n            const keyword = filters.keyword.value.toLowerCase().trim();\n            console.log('Applying keyword filter: \"'.concat(keyword, '\"'));\n            const beforeCount = filtered.length;\n            filtered = filtered.filter((task)=>{\n                // Safely get text content, handling null/undefined and HTML\n                const getName = ()=>((task === null || task === void 0 ? void 0 : task.name) || \"\").toLowerCase();\n                const getDescription = ()=>((task === null || task === void 0 ? void 0 : task.description) || \"\").toLowerCase();\n                const getComments = ()=>{\n                    if (!(task === null || task === void 0 ? void 0 : task.comments)) return \"\";\n                    // Strip HTML tags if present and convert to lowercase\n                    return task.comments.replace(/<[^>]*>/g, \"\").toLowerCase();\n                };\n                const nameMatch = getName().includes(keyword);\n                const descMatch = getDescription().includes(keyword);\n                const commentMatch = getComments().includes(keyword);\n                const match = nameMatch || descMatch || commentMatch;\n                if (match) {\n                    console.log('Task \"'.concat(task.name, '\" matches keyword \"').concat(keyword, '\"'));\n                }\n                return match;\n            });\n            console.log(\"Keyword filter: \".concat(beforeCount, \" -> \").concat(filtered.length, \" tasks\"));\n        }\n        setFilteredTaskList(filtered);\n    }, [\n        taskList,\n        filters\n    ]);\n    // Handle filter changes\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        console.log(\"Filter change - type: \".concat(type, \", data:\"), data);\n        if (type === \"keyword\") {\n            console.log(\"Keyword filter data structure:\", data);\n            console.log(\"Keyword value:\", data === null || data === void 0 ? void 0 : data.value);\n        }\n        setFilters((prev)=>({\n                ...prev,\n                [type]: data\n            }));\n    };\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: \"Task\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2, _task_isOverDue3, _task_isOverDue4, _task_isOverDue5, _task_isOverDue6, _task_isOverDue7, _task_isOverDue8, _task_isOverDue9, _task_isOverDue10, _task_isOverDue11, _task_isOverDue12, _task_isOverDue13, _task_isOverDue14, _task_isOverDue15, _task_isOverDue16, _task_isOverDue17, _task_isOverDue18, _task_isOverDue19;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-foreground flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(task.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: \"focus:outline-none\",\n                                            children: task.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block rounded px-3 py-1 ml-3 \".concat((task === null || task === void 0 ? void 0 : (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\" ? \"alert\" : \"\", \" \").concat((task === null || task === void 0 ? void 0 : (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) === \"Low\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.status) === \"Upcoming\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status) === \"Completed\" ? \"success\" : \"\", \" \").concat((task === null || task === void 0 ? void 0 : (_task_isOverDue4 = task.isOverDue) === null || _task_isOverDue4 === void 0 ? void 0 : _task_isOverDue4.status) === \"Medium\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue5 = task.isOverDue) === null || _task_isOverDue5 === void 0 ? void 0 : _task_isOverDue5.days) === \"Save As Draft\" ? \"text-yellow-600 bg-yellow-100\" : \"\"),\n                                            children: [\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue6 = task.isOverDue) === null || _task_isOverDue6 === void 0 ? void 0 : _task_isOverDue6.status) && [\n                                                    \"High\",\n                                                    \"Medium\",\n                                                    \"Low\"\n                                                ].includes(task.isOverDue.status) && (task === null || task === void 0 ? void 0 : (_task_isOverDue7 = task.isOverDue) === null || _task_isOverDue7 === void 0 ? void 0 : _task_isOverDue7.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue8 = task.isOverDue) === null || _task_isOverDue8 === void 0 ? void 0 : _task_isOverDue8.status) === \"Completed\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue9 = task.isOverDue) === null || _task_isOverDue9 === void 0 ? void 0 : _task_isOverDue9.days) === \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue10 = task.isOverDue) === null || _task_isOverDue10 === void 0 ? void 0 : _task_isOverDue10.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue11 = task.isOverDue) === null || _task_isOverDue11 === void 0 ? void 0 : _task_isOverDue11.status) === \"Upcoming\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue12 = task.isOverDue) === null || _task_isOverDue12 === void 0 ? void 0 : _task_isOverDue12.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue13 = task.isOverDue) === null || _task_isOverDue13 === void 0 ? void 0 : _task_isOverDue13.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue14 = task.isOverDue) === null || _task_isOverDue14 === void 0 ? void 0 : _task_isOverDue14.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue15 = task.isOverDue) === null || _task_isOverDue15 === void 0 ? void 0 : _task_isOverDue15.status),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue16 = task.isOverDue) === null || _task_isOverDue16 === void 0 ? void 0 : _task_isOverDue16.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue17 = task.isOverDue) === null || _task_isOverDue17 === void 0 ? void 0 : _task_isOverDue17.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue18 = task.isOverDue) === null || _task_isOverDue18 === void 0 ? void 0 : _task_isOverDue18.days) !== \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue19 = task.isOverDue) === null || _task_isOverDue19 === void 0 ? void 0 : _task_isOverDue19.days)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-14 flex items-center pl-1\",\n                                    children: task.Comments !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"outline-none px-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                                                className: \"w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"leading-loose\",\n                                                    children: task.comments\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 25\n                        }, undefined),\n                        task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1 text-sm text-muted-foreground\",\n                            children: task.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: \"Vessel\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_basicComponent;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: (task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.title) || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"inventoryItem\",\n            header: \"Inventory Item\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_inventory, _task_maintenanceSchedule_inventory, _task_maintenanceSchedule;\n                const task = row.original;\n                // Check for inventory from multiple possible sources\n                const inventoryItem = (task === null || task === void 0 ? void 0 : (_task_inventory = task.inventory) === null || _task_inventory === void 0 ? void 0 : _task_inventory.item) || (task === null || task === void 0 ? void 0 : (_task_maintenanceSchedule = task.maintenanceSchedule) === null || _task_maintenanceSchedule === void 0 ? void 0 : (_task_maintenanceSchedule_inventory = _task_maintenanceSchedule.inventory) === null || _task_maintenanceSchedule_inventory === void 0 ? void 0 : _task_maintenanceSchedule_inventory.item) || null;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: inventoryItem || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-right\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground mb-1\",\n                            children: [\n                                \"Due: \",\n                                (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(task.expires)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block rounded px-3 py-1 \".concat(task.status == \"Completed\" ? \"success\" : \"alert\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: task.status\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: !taskList ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 280,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n            columns: columns,\n            data: filteredTaskList || [],\n            showToolbar: true,\n            pageSize: 50,\n            showPageSizeSelector: false,\n            onChange: handleFilterOnChange\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 282,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s(CrewAllocatedTasks, \"qNtACZUcNLypXDUcvZGZmSOV7oU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams\n    ];\n});\n_c = CrewAllocatedTasks;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewAllocatedTasks);\nvar _c;\n$RefreshReg$(_c, \"CrewAllocatedTasks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy9hbGxvY2F0ZWQtdGFza3MudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEI7QUFDZ0Q7QUFDNUM7QUFDOEI7QUFDVDtBQUNSO0FBQzRCO0FBQzFCO0FBS2Y7QUFDVztBQUUzQyxNQUFNYyxxQkFBcUI7UUFBQyxFQUFFQyxRQUFRLEVBQXNCOztJQUN4RCxNQUFNQyxXQUFXYiw0REFBV0E7SUFDNUIsTUFBTWMsZUFBZWIsZ0VBQWVBO0lBQ3BDLE1BQU0sQ0FBQ2Msa0JBQWtCQyxvQkFBb0IsR0FBR1AsK0NBQVFBLENBQUNHO0lBQ3pELE1BQU0sQ0FBQ0ssU0FBU0MsV0FBVyxHQUFHVCwrQ0FBUUEsQ0FJbkM7UUFDQ1UsUUFBUTtRQUNSQyxRQUFRO1FBQ1JDLFNBQVM7SUFDYjtJQUVBLG1EQUFtRDtJQUNuRCxNQUFNQyxTQUFTUixhQUFhUyxHQUFHLENBQUM7SUFFaEMsOEJBQThCO0lBQzlCYixnREFBU0EsQ0FBQztRQUNOLElBQUlFLFlBQVlBLFNBQVNZLE1BQU0sR0FBRyxHQUFHO2dCQUdRWjtZQUZ6Q2EsUUFBUUMsR0FBRyxDQUFDLHFCQUE0QixPQUFQSjtZQUNqQ0csUUFBUUMsR0FBRyxDQUFDLHlCQUF5QyxPQUFoQmQsU0FBU1ksTUFBTTtZQUNwREMsUUFBUUMsR0FBRyxDQUFDLDhCQUE2QmQsYUFBQUEsUUFBUSxDQUFDLEVBQUUsY0FBWEEsaUNBQUFBLFdBQWFlLFlBQVk7WUFDbEVGLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkI7bUJBQ2xDLElBQUlFLElBQUloQixTQUFTaUIsR0FBRyxDQUFDLENBQUNDLElBQVdBLEVBQUVILFlBQVk7YUFDckQ7UUFDTDtJQUNKLEdBQUc7UUFBQ2Y7UUFBVVU7S0FBTztJQUVyQiw2Q0FBNkM7SUFDN0NaLGdEQUFTQSxDQUFDO1FBQ05NLG9CQUFvQko7SUFDeEIsR0FBRztRQUFDQTtLQUFTO0lBRWIseURBQXlEO0lBQ3pERixnREFBU0EsQ0FBQztRQUNOLElBQUksQ0FBQ0UsVUFBVTtRQUVmLElBQUltQixXQUFXO2VBQUluQjtTQUFTO1FBRTVCLHVFQUF1RTtRQUN2RSxJQUFJSyxRQUFRRSxNQUFNLEVBQUU7WUFDaEIsdURBQXVEO1lBQ3ZELElBQUlhLFlBQXNCLEVBQUU7WUFDNUIsSUFBSUMsTUFBTUMsT0FBTyxDQUFDakIsUUFBUUUsTUFBTSxLQUFLRixRQUFRRSxNQUFNLENBQUNLLE1BQU0sR0FBRyxHQUFHO2dCQUM1RFEsWUFBWWYsUUFBUUUsTUFBTSxDQUFDVSxHQUFHLENBQUMsQ0FBQ00sT0FDNUJDLE9BQU9ELEtBQUtFLEtBQUs7WUFFekIsT0FBTyxJQUFJcEIsUUFBUUUsTUFBTSxJQUFJLENBQUNjLE1BQU1DLE9BQU8sQ0FBQ2pCLFFBQVFFLE1BQU0sR0FBRztnQkFDekRhLFlBQVk7b0JBQUNJLE9BQU9uQixRQUFRRSxNQUFNLENBQUNrQixLQUFLO2lCQUFFO1lBQzlDO1lBRUEsSUFBSUwsVUFBVVIsTUFBTSxHQUFHLEdBQUc7Z0JBQ3RCTyxXQUFXQSxTQUFTTyxNQUFNLENBQUMsQ0FBQ0M7d0JBQ0lBO29CQUE1QixNQUFNQyxlQUFlSixPQUFPRyxpQkFBQUEsNEJBQUFBLHVCQUFBQSxLQUFNRSxjQUFjLGNBQXBCRiwyQ0FBQUEscUJBQXNCRyxFQUFFO29CQUNwRCxPQUFPVixVQUFVVyxRQUFRLENBQUNIO2dCQUM5QjtZQUNKO1FBQ0o7UUFFQSxzQkFBc0I7UUFDdEIsSUFBSXZCLFFBQVFHLE1BQU0sRUFBRTtZQUNoQlcsV0FBV0EsU0FBU08sTUFBTSxDQUN0QixDQUFDQyxPQUFjQSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1uQixNQUFNLE1BQUtILFFBQVFHLE1BQU0sQ0FBQ2lCLEtBQUs7UUFFNUQ7UUFFQSx1QkFBdUI7UUFDdkIsSUFDSXBCLFFBQVFJLE9BQU8sSUFDZkosUUFBUUksT0FBTyxDQUFDZ0IsS0FBSyxJQUNyQnBCLFFBQVFJLE9BQU8sQ0FBQ2dCLEtBQUssQ0FBQ08sSUFBSSxJQUM1QjtZQUNFLE1BQU12QixVQUFVSixRQUFRSSxPQUFPLENBQUNnQixLQUFLLENBQUNRLFdBQVcsR0FBR0QsSUFBSTtZQUN4RG5CLFFBQVFDLEdBQUcsQ0FBQyw2QkFBcUMsT0FBUkwsU0FBUTtZQUNqRCxNQUFNeUIsY0FBY2YsU0FBU1AsTUFBTTtZQUNuQ08sV0FBV0EsU0FBU08sTUFBTSxDQUFDLENBQUNDO2dCQUN4Qiw0REFBNEQ7Z0JBQzVELE1BQU1RLFVBQVUsSUFBTSxDQUFDUixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1TLElBQUksS0FBSSxFQUFDLEVBQUdILFdBQVc7Z0JBQ3BELE1BQU1JLGlCQUFpQixJQUNuQixDQUFDVixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1XLFdBQVcsS0FBSSxFQUFDLEVBQUdMLFdBQVc7Z0JBQ3pDLE1BQU1NLGNBQWM7b0JBQ2hCLElBQUksRUFBQ1osaUJBQUFBLDJCQUFBQSxLQUFNYSxRQUFRLEdBQUUsT0FBTztvQkFDNUIsc0RBQXNEO29CQUN0RCxPQUFPYixLQUFLYSxRQUFRLENBQUNDLE9BQU8sQ0FBQyxZQUFZLElBQUlSLFdBQVc7Z0JBQzVEO2dCQUVBLE1BQU1TLFlBQVlQLFVBQVVKLFFBQVEsQ0FBQ3RCO2dCQUNyQyxNQUFNa0MsWUFBWU4saUJBQWlCTixRQUFRLENBQUN0QjtnQkFDNUMsTUFBTW1DLGVBQWVMLGNBQWNSLFFBQVEsQ0FBQ3RCO2dCQUM1QyxNQUFNb0MsUUFBUUgsYUFBYUMsYUFBYUM7Z0JBRXhDLElBQUlDLE9BQU87b0JBQ1BoQyxRQUFRQyxHQUFHLENBQ1AsU0FBd0NMLE9BQS9Ca0IsS0FBS1MsSUFBSSxFQUFDLHVCQUE2QixPQUFSM0IsU0FBUTtnQkFFeEQ7Z0JBQ0EsT0FBT29DO1lBQ1g7WUFDQWhDLFFBQVFDLEdBQUcsQ0FDUCxtQkFBcUNLLE9BQWxCZSxhQUFZLFFBQXNCLE9BQWhCZixTQUFTUCxNQUFNLEVBQUM7UUFFN0Q7UUFFQVIsb0JBQW9CZTtJQUN4QixHQUFHO1FBQUNuQjtRQUFVSztLQUFRO0lBRXRCLHdCQUF3QjtJQUN4QixNQUFNeUMsdUJBQXVCO1lBQUMsRUFBRUMsSUFBSSxFQUFFQyxJQUFJLEVBQU87UUFDN0NuQyxRQUFRQyxHQUFHLENBQUMseUJBQThCLE9BQUxpQyxNQUFLLFlBQVVDO1FBQ3BELElBQUlELFNBQVMsV0FBVztZQUNwQmxDLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0NrQztZQUM5Q25DLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JrQyxpQkFBQUEsMkJBQUFBLEtBQU12QixLQUFLO1FBQzdDO1FBQ0FuQixXQUFXLENBQUMyQyxPQUFVO2dCQUNsQixHQUFHQSxJQUFJO2dCQUNQLENBQUNGLEtBQUssRUFBRUM7WUFDWjtJQUNKO0lBRUEsbUNBQW1DO0lBQ25DLE1BQU1FLFVBQXlDO1FBQzNDO1lBQ0lDLGFBQWE7WUFDYkMsUUFBUTtZQUNSQyxlQUFlO1lBQ2ZDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtvQkFhQTVCLGlCQUlBQSxrQkFDQUEsa0JBRUFBLGtCQUlBQSxrQkFDQUEsa0JBS0hBLGtCQUlHQSxrQkFDSEEsa0JBQ0dBLGtCQUVBQSxtQkFDSEEsbUJBQ0dBLG1CQUNIQSxtQkFDV0EsbUJBQ1JBLG1CQUNIQSxtQkFDWUEsbUJBQ1RBLG1CQUVBQTtnQkEvQ3hCLE1BQU1BLE9BQU80QixJQUFJQyxRQUFRO2dCQUN6QixxQkFDSSw4REFBQ0M7O3NDQUNHLDhEQUFDQTs0QkFBSUMsV0FBVTs7OENBQ1gsOERBQUNDO29DQUFLRCxXQUFVOztzREFDWiw4REFBQ3pFLGlEQUFJQTs0Q0FDRDJFLE1BQU0sdUJBQThDM0QsT0FBdkIwQixLQUFLRyxFQUFFLEVBQUMsaUJBQTJCNUIsT0FBWkQsVUFBUyxLQUEyQixPQUF4QkMsYUFBYTJELFFBQVE7NENBQ3JGSCxXQUFVO3NEQUNUL0IsS0FBS1MsSUFBSTs7Ozs7O3NEQUVkLDhEQUFDcUI7NENBQ0dDLFdBQVcsdUNBS1AvQixPQUpBQSxDQUFBQSxpQkFBQUEsNEJBQUFBLGtCQUFBQSxLQUFNbUMsU0FBUyxjQUFmbkMsc0NBQUFBLGdCQUFpQm5CLE1BQU0sTUFBSyxTQUN0QixVQUNBLElBQ1QsS0FRR21CLE9BUEFBLENBQUFBLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU1tQyxTQUFTLGNBQWZuQyx1Q0FBQUEsaUJBQWlCbkIsTUFBTSxNQUFLLFNBQzVCbUIsQ0FBQUEsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTW1DLFNBQVMsY0FBZm5DLHVDQUFBQSxpQkFBaUJuQixNQUFNLE1BQ25CLGNBQ0ptQixDQUFBQSxpQkFBQUEsNEJBQUFBLG1CQUFBQSxLQUFNbUMsU0FBUyxjQUFmbkMsdUNBQUFBLGlCQUFpQm5CLE1BQU0sTUFBSyxjQUN0QixZQUNBLElBQ1QsS0FNQSxPQUxHbUIsQ0FBQUEsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTW1DLFNBQVMsY0FBZm5DLHVDQUFBQSxpQkFBaUJuQixNQUFNLE1BQUssWUFDNUJtQixDQUFBQSxpQkFBQUEsNEJBQUFBLG1CQUFBQSxLQUFNbUMsU0FBUyxjQUFmbkMsdUNBQUFBLGlCQUFpQm9DLElBQUksTUFDakIsa0JBQ0Usa0NBQ0E7O2dEQUVUcEMsQ0FBQUEsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTW1DLFNBQVMsY0FBZm5DLHVDQUFBQSxpQkFBaUJuQixNQUFNLEtBQ3BCO29EQUFDO29EQUFRO29EQUFVO2lEQUFNLENBQUN1QixRQUFRLENBQzlCSixLQUFLbUMsU0FBUyxDQUFDdEQsTUFBTSxNQUV6Qm1CLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU1tQyxTQUFTLGNBQWZuQyx1Q0FBQUEsaUJBQWlCb0MsSUFBSTtnREFDeEJwQyxDQUFBQSxpQkFBQUEsNEJBQUFBLG1CQUFBQSxLQUFNbUMsU0FBUyxjQUFmbkMsdUNBQUFBLGlCQUFpQm5CLE1BQU0sTUFBSyxlQUN6Qm1CLENBQUFBLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU1tQyxTQUFTLGNBQWZuQyx1Q0FBQUEsaUJBQWlCb0MsSUFBSSxNQUNqQixvQkFDSnBDLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU1tQyxTQUFTLGNBQWZuQyx3Q0FBQUEsa0JBQWlCb0MsSUFBSTtnREFDeEJwQyxDQUFBQSxpQkFBQUEsNEJBQUFBLG9CQUFBQSxLQUFNbUMsU0FBUyxjQUFmbkMsd0NBQUFBLGtCQUFpQm5CLE1BQU0sTUFBSyxlQUN6Qm1CLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU1tQyxTQUFTLGNBQWZuQyx3Q0FBQUEsa0JBQWlCb0MsSUFBSTtnREFDeEJwQyxDQUFBQSxpQkFBQUEsNEJBQUFBLG9CQUFBQSxLQUFNbUMsU0FBUyxjQUFmbkMsd0NBQUFBLGtCQUFpQm5CLE1BQU0sTUFBSyxlQUN6QnJCLHFEQUFPQSxDQUFDd0MsaUJBQUFBLDRCQUFBQSxvQkFBQUEsS0FBTW1DLFNBQVMsY0FBZm5DLHdDQUFBQSxrQkFBaUJvQyxJQUFJLE1BQzdCcEMsaUJBQUFBLDRCQUFBQSxvQkFBQUEsS0FBTW1DLFNBQVMsY0FBZm5DLHdDQUFBQSxrQkFBaUJuQixNQUFNO2dEQUMxQm1CLENBQUFBLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU1tQyxTQUFTLGNBQWZuQyx3Q0FBQUEsa0JBQWlCbkIsTUFBTSxNQUFLLGVBQ3pCLENBQUNyQixxREFBT0EsQ0FBQ3dDLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU1tQyxTQUFTLGNBQWZuQyx3Q0FBQUEsa0JBQWlCb0MsSUFBSSxLQUM5QnBDLENBQUFBLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU1tQyxTQUFTLGNBQWZuQyx3Q0FBQUEsa0JBQWlCb0MsSUFBSSxNQUNqQixvQkFDSnBDLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU1tQyxTQUFTLGNBQWZuQyx3Q0FBQUEsa0JBQWlCb0MsSUFBSTs7Ozs7Ozs7Ozs7Ozs4Q0FHakMsOERBQUNOO29DQUFJQyxXQUFVOzhDQUNWL0IsS0FBS3FDLFFBQVEsS0FBSyxzQkFDZiw4REFBQ3RFLDJEQUFPQTs7MERBQ0osOERBQUNFLGtFQUFjQTtnREFBQ3FFLE9BQU87MERBQ25CLDRFQUFDeEUseURBQU1BO29EQUNIeUUsU0FBUTtvREFDUkMsTUFBSztvREFDTFQsV0FBVTs4REFDViw0RUFBQ3hFLHlIQUE4QkE7d0RBQUN3RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzBEQUdsRCw4REFBQy9ELGtFQUFjQTtnREFBQytELFdBQVU7MERBQ3RCLDRFQUFDRDtvREFBSUMsV0FBVTs4REFDVi9CLEtBQUthLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBT3JDYixLQUFLVyxXQUFXLGtCQUNiLDhEQUFDbUI7NEJBQUlDLFdBQVU7c0NBQ1YvQixLQUFLVyxXQUFXOzs7Ozs7Ozs7Ozs7WUFLckM7UUFDSjtRQUNBO1lBQ0lhLGFBQWE7WUFDYkMsUUFBUTtZQUNSQyxlQUFlO1lBQ2ZDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtvQkFJZjVCO2dCQUhULE1BQU1BLE9BQU80QixJQUFJQyxRQUFRO2dCQUN6QixxQkFDSSw4REFBQ0M7b0JBQUlDLFdBQVU7OEJBQ1YvQixDQUFBQSxpQkFBQUEsNEJBQUFBLHVCQUFBQSxLQUFNRSxjQUFjLGNBQXBCRiwyQ0FBQUEscUJBQXNCeUMsS0FBSyxLQUFJOzs7Ozs7WUFHNUM7UUFDSjtRQUNBO1lBQ0lqQixhQUFhO1lBQ2JDLFFBQVE7WUFDUkMsZUFBZTtZQUNmQyxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7b0JBSXBCNUIsaUJBQ0FBLHFDQUFBQTtnQkFKSixNQUFNQSxPQUFPNEIsSUFBSUMsUUFBUTtnQkFDekIscURBQXFEO2dCQUNyRCxNQUFNYSxnQkFDRjFDLENBQUFBLGlCQUFBQSw0QkFBQUEsa0JBQUFBLEtBQU0yQyxTQUFTLGNBQWYzQyxzQ0FBQUEsZ0JBQWlCSixJQUFJLE1BQ3JCSSxpQkFBQUEsNEJBQUFBLDRCQUFBQSxLQUFNNEMsbUJBQW1CLGNBQXpCNUMsaURBQUFBLHNDQUFBQSwwQkFBMkIyQyxTQUFTLGNBQXBDM0MsMERBQUFBLG9DQUFzQ0osSUFBSSxLQUMxQztnQkFFSixxQkFDSSw4REFBQ2tDO29CQUFJQyxXQUFVOzhCQUFlVyxpQkFBaUI7Ozs7OztZQUV2RDtRQUNKO1FBQ0E7WUFDSWxCLGFBQWE7WUFDYkMsUUFBUTtZQUNSQyxlQUFlO1lBQ2ZDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTTVCLE9BQU80QixJQUFJQyxRQUFRO2dCQUN6QixxQkFDSSw4REFBQ0M7b0JBQUlDLFdBQVU7O3NDQUNYLDhEQUFDRDs0QkFBSUMsV0FBVTs7Z0NBQXFDO2dDQUMxQ3BFLG1FQUFVQSxDQUFDcUMsS0FBSzZDLE9BQU87Ozs7Ozs7c0NBRWpDLDhEQUFDZjs0QkFDR0MsV0FBVyxrQ0FFVixPQURHL0IsS0FBS25CLE1BQU0sSUFBSSxjQUFjLFlBQVk7c0NBRTdDLDRFQUFDbUQ7MENBQU1oQyxLQUFLbkIsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJbEM7UUFDSjtLQUNIO0lBRUQscUJBQ0k7a0JBQ0ssQ0FBQ1IseUJBQ0UsOERBQUNULHVEQUFJQTs7OztzQ0FFTCw4REFBQ0MsZ0VBQVNBO1lBQ04wRCxTQUFTQTtZQUNURixNQUFNN0Msb0JBQW9CLEVBQUU7WUFDNUJzRSxhQUFhO1lBQ2JDLFVBQVU7WUFDVkMsc0JBQXNCO1lBQ3RCQyxVQUFVOUI7Ozs7Ozs7QUFLOUI7R0FuUk0vQzs7UUFDZVgsd0RBQVdBO1FBQ1BDLDREQUFlQTs7O0tBRmxDVTtBQXFSTiwrREFBZUEsa0JBQWtCQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdWkvY3Jldy9hbGxvY2F0ZWQtdGFza3MudHN4P2EyY2YiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXHJcbmltcG9ydCB7IENoYXRCdWJibGVCb3R0b21DZW50ZXJUZXh0SWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSdcclxuaW1wb3J0IHsgaXNFbXB0eSB9IGZyb20gJ2xvZGFzaCdcclxuaW1wb3J0IHsgdXNlUGF0aG5hbWUsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcclxuaW1wb3J0IHsgZm9ybWF0RGF0ZSB9IGZyb20gJ0AvYXBwL2hlbHBlcnMvZGF0ZUhlbHBlcidcclxuaW1wb3J0IHsgTGlzdCB9IGZyb20gJ0AvY29tcG9uZW50cy9za2VsZXRvbnMnXHJcbmltcG9ydCB7IERhdGFUYWJsZSwgRXh0ZW5kZWRDb2x1bW5EZWYgfSBmcm9tICdAL2NvbXBvbmVudHMvZmlsdGVyZWRUYWJsZSdcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcclxuaW1wb3J0IHtcclxuICAgIFBvcG92ZXIsXHJcbiAgICBQb3BvdmVyQ29udGVudCxcclxuICAgIFBvcG92ZXJUcmlnZ2VyLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9wb3BvdmVyJ1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXHJcblxyXG5jb25zdCBDcmV3QWxsb2NhdGVkVGFza3MgPSAoeyB0YXNrTGlzdCB9OiB7IHRhc2tMaXN0PzogYW55IH0pID0+IHtcclxuICAgIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxyXG4gICAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKClcclxuICAgIGNvbnN0IFtmaWx0ZXJlZFRhc2tMaXN0LCBzZXRGaWx0ZXJlZFRhc2tMaXN0XSA9IHVzZVN0YXRlKHRhc2tMaXN0KVxyXG4gICAgY29uc3QgW2ZpbHRlcnMsIHNldEZpbHRlcnNdID0gdXNlU3RhdGU8e1xyXG4gICAgICAgIHZlc3NlbDogYW55XHJcbiAgICAgICAgc3RhdHVzOiBhbnlcclxuICAgICAgICBrZXl3b3JkOiBhbnlcclxuICAgIH0+KHtcclxuICAgICAgICB2ZXNzZWw6IG51bGwsXHJcbiAgICAgICAgc3RhdHVzOiBudWxsLFxyXG4gICAgICAgIGtleXdvcmQ6IG51bGwsXHJcbiAgICB9KVxyXG5cclxuICAgIC8vIEdldCBjcmV3IG1lbWJlciBJRCBmcm9tIFVSTCBwYXJhbXMgZm9yIGRlYnVnZ2luZ1xyXG4gICAgY29uc3QgY3Jld0lkID0gc2VhcmNoUGFyYW1zLmdldCgnaWQnKVxyXG5cclxuICAgIC8vIERlYnVnIGNyZXcgbWVtYmVyIGZpbHRlcmluZ1xyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAodGFza0xpc3QgJiYgdGFza0xpc3QubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgQ3JldyBJRCBmcm9tIFVSTDogJHtjcmV3SWR9YClcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFRvdGFsIHRhc2tzIHJlY2VpdmVkOiAke3Rhc2tMaXN0Lmxlbmd0aH1gKVxyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnU2FtcGxlIHRhc2sgYXNzaWduZWRUb0lEOicsIHRhc2tMaXN0WzBdPy5hc3NpZ25lZFRvSUQpXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBbGwgdW5pcXVlIGFzc2lnbmVkVG9JRHM6JywgW1xyXG4gICAgICAgICAgICAgICAgLi4ubmV3IFNldCh0YXNrTGlzdC5tYXAoKHQ6IGFueSkgPT4gdC5hc3NpZ25lZFRvSUQpKSxcclxuICAgICAgICAgICAgXSlcclxuICAgICAgICB9XHJcbiAgICB9LCBbdGFza0xpc3QsIGNyZXdJZF0pXHJcblxyXG4gICAgLy8gVXBkYXRlIGZpbHRlcmVkIGxpc3Qgd2hlbiB0YXNrTGlzdCBjaGFuZ2VzXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIHNldEZpbHRlcmVkVGFza0xpc3QodGFza0xpc3QpXHJcbiAgICB9LCBbdGFza0xpc3RdKVxyXG5cclxuICAgIC8vIEFwcGx5IGFsbCBmaWx0ZXJzIHdoZW5ldmVyIGZpbHRlcnMgb3IgdGFza0xpc3QgY2hhbmdlc1xyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAoIXRhc2tMaXN0KSByZXR1cm5cclxuXHJcbiAgICAgICAgbGV0IGZpbHRlcmVkID0gWy4uLnRhc2tMaXN0XVxyXG5cclxuICAgICAgICAvLyBBcHBseSB2ZXNzZWwgZmlsdGVyIC0gZm9sbG93aW5nIHRoZSBzYW1lIHBhdHRlcm4gYXMgbWFpbnRlbmFuY2UgbGlzdFxyXG4gICAgICAgIGlmIChmaWx0ZXJzLnZlc3NlbCkge1xyXG4gICAgICAgICAgICAvLyBIYW5kbGUgYm90aCBzaW5nbGUgYW5kIG11bHRpLXNlbGVjdCB2ZXNzZWwgZmlsdGVyaW5nXHJcbiAgICAgICAgICAgIGxldCB2ZXNzZWxJZHM6IHN0cmluZ1tdID0gW11cclxuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZmlsdGVycy52ZXNzZWwpICYmIGZpbHRlcnMudmVzc2VsLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgICAgIHZlc3NlbElkcyA9IGZpbHRlcnMudmVzc2VsLm1hcCgoaXRlbTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgIFN0cmluZyhpdGVtLnZhbHVlKSxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChmaWx0ZXJzLnZlc3NlbCAmJiAhQXJyYXkuaXNBcnJheShmaWx0ZXJzLnZlc3NlbCkpIHtcclxuICAgICAgICAgICAgICAgIHZlc3NlbElkcyA9IFtTdHJpbmcoZmlsdGVycy52ZXNzZWwudmFsdWUpXVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBpZiAodmVzc2VsSWRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKCh0YXNrOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB0YXNrVmVzc2VsSWQgPSBTdHJpbmcodGFzaz8uYmFzaWNDb21wb25lbnQ/LmlkKVxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB2ZXNzZWxJZHMuaW5jbHVkZXModGFza1Zlc3NlbElkKVxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQXBwbHkgc3RhdHVzIGZpbHRlclxyXG4gICAgICAgIGlmIChmaWx0ZXJzLnN0YXR1cykge1xyXG4gICAgICAgICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihcclxuICAgICAgICAgICAgICAgICh0YXNrOiBhbnkpID0+IHRhc2s/LnN0YXR1cyA9PT0gZmlsdGVycy5zdGF0dXMudmFsdWUsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEFwcGx5IGtleXdvcmQgZmlsdGVyXHJcbiAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICBmaWx0ZXJzLmtleXdvcmQgJiZcclxuICAgICAgICAgICAgZmlsdGVycy5rZXl3b3JkLnZhbHVlICYmXHJcbiAgICAgICAgICAgIGZpbHRlcnMua2V5d29yZC52YWx1ZS50cmltKClcclxuICAgICAgICApIHtcclxuICAgICAgICAgICAgY29uc3Qga2V5d29yZCA9IGZpbHRlcnMua2V5d29yZC52YWx1ZS50b0xvd2VyQ2FzZSgpLnRyaW0oKVxyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgQXBwbHlpbmcga2V5d29yZCBmaWx0ZXI6IFwiJHtrZXl3b3JkfVwiYClcclxuICAgICAgICAgICAgY29uc3QgYmVmb3JlQ291bnQgPSBmaWx0ZXJlZC5sZW5ndGhcclxuICAgICAgICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoKHRhc2s6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgLy8gU2FmZWx5IGdldCB0ZXh0IGNvbnRlbnQsIGhhbmRsaW5nIG51bGwvdW5kZWZpbmVkIGFuZCBIVE1MXHJcbiAgICAgICAgICAgICAgICBjb25zdCBnZXROYW1lID0gKCkgPT4gKHRhc2s/Lm5hbWUgfHwgJycpLnRvTG93ZXJDYXNlKClcclxuICAgICAgICAgICAgICAgIGNvbnN0IGdldERlc2NyaXB0aW9uID0gKCkgPT5cclxuICAgICAgICAgICAgICAgICAgICAodGFzaz8uZGVzY3JpcHRpb24gfHwgJycpLnRvTG93ZXJDYXNlKClcclxuICAgICAgICAgICAgICAgIGNvbnN0IGdldENvbW1lbnRzID0gKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmICghdGFzaz8uY29tbWVudHMpIHJldHVybiAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIFN0cmlwIEhUTUwgdGFncyBpZiBwcmVzZW50IGFuZCBjb252ZXJ0IHRvIGxvd2VyY2FzZVxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB0YXNrLmNvbW1lbnRzLnJlcGxhY2UoLzxbXj5dKj4vZywgJycpLnRvTG93ZXJDYXNlKClcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICBjb25zdCBuYW1lTWF0Y2ggPSBnZXROYW1lKCkuaW5jbHVkZXMoa2V5d29yZClcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRlc2NNYXRjaCA9IGdldERlc2NyaXB0aW9uKCkuaW5jbHVkZXMoa2V5d29yZClcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNvbW1lbnRNYXRjaCA9IGdldENvbW1lbnRzKCkuaW5jbHVkZXMoa2V5d29yZClcclxuICAgICAgICAgICAgICAgIGNvbnN0IG1hdGNoID0gbmFtZU1hdGNoIHx8IGRlc2NNYXRjaCB8fCBjb21tZW50TWF0Y2hcclxuXHJcbiAgICAgICAgICAgICAgICBpZiAobWF0Y2gpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgYFRhc2sgXCIke3Rhc2submFtZX1cIiBtYXRjaGVzIGtleXdvcmQgXCIke2tleXdvcmR9XCJgLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHJldHVybiBtYXRjaFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgICAgICAgIGBLZXl3b3JkIGZpbHRlcjogJHtiZWZvcmVDb3VudH0gLT4gJHtmaWx0ZXJlZC5sZW5ndGh9IHRhc2tzYCxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgc2V0RmlsdGVyZWRUYXNrTGlzdChmaWx0ZXJlZClcclxuICAgIH0sIFt0YXNrTGlzdCwgZmlsdGVyc10pXHJcblxyXG4gICAgLy8gSGFuZGxlIGZpbHRlciBjaGFuZ2VzXHJcbiAgICBjb25zdCBoYW5kbGVGaWx0ZXJPbkNoYW5nZSA9ICh7IHR5cGUsIGRhdGEgfTogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5sb2coYEZpbHRlciBjaGFuZ2UgLSB0eXBlOiAke3R5cGV9LCBkYXRhOmAsIGRhdGEpXHJcbiAgICAgICAgaWYgKHR5cGUgPT09ICdrZXl3b3JkJykge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnS2V5d29yZCBmaWx0ZXIgZGF0YSBzdHJ1Y3R1cmU6JywgZGF0YSlcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0tleXdvcmQgdmFsdWU6JywgZGF0YT8udmFsdWUpXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNldEZpbHRlcnMoKHByZXYpID0+ICh7XHJcbiAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgIFt0eXBlXTogZGF0YSxcclxuICAgICAgICB9KSlcclxuICAgIH1cclxuXHJcbiAgICAvLyBEZWZpbmUgY29sdW1ucyBmb3IgdGhlIERhdGFUYWJsZVxyXG4gICAgY29uc3QgY29sdW1uczogRXh0ZW5kZWRDb2x1bW5EZWY8YW55LCBhbnk+W10gPSBbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3RpdGxlJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAnVGFzaycsXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdGFzayA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1mb3JlZ3JvdW5kIGZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17YC9tYWludGVuYW5jZT90YXNrSUQ9JHt0YXNrLmlkfSZyZWRpcmVjdF90bz0ke3BhdGhuYW1lfT8ke3NlYXJjaFBhcmFtcy50b1N0cmluZygpfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvY3VzOm91dGxpbmUtbm9uZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzay5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGlubGluZS1ibG9jayByb3VuZGVkIHB4LTMgcHktMSBtbC0zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrPy5pc092ZXJEdWU/LnN0YXR1cyA9PT0gJ0hpZ2gnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYWxlcnQnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrPy5pc092ZXJEdWU/LnN0YXR1cyA9PT0gJ0xvdycgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2s/LmlzT3ZlckR1ZT8uc3RhdHVzID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdVcGNvbWluZycgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2s/LmlzT3ZlckR1ZT8uc3RhdHVzID09PSAnQ29tcGxldGVkJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3N1Y2Nlc3MnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrPy5pc092ZXJEdWU/LnN0YXR1cyA9PT0gJ01lZGl1bScgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2s/LmlzT3ZlckR1ZT8uZGF5cyA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnU2F2ZSBBcyBEcmFmdCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICd0ZXh0LXllbGxvdy02MDAgYmcteWVsbG93LTEwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Rhc2s/LmlzT3ZlckR1ZT8uc3RhdHVzICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbJ0hpZ2gnLCAnTWVkaXVtJywgJ0xvdyddLmluY2x1ZGVzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2suaXNPdmVyRHVlLnN0YXR1cyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2s/LmlzT3ZlckR1ZT8uZGF5c31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Rhc2s/LmlzT3ZlckR1ZT8uc3RhdHVzID09PSAnQ29tcGxldGVkJyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzaz8uaXNPdmVyRHVlPy5kYXlzID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdTYXZlIEFzIERyYWZ0JyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzaz8uaXNPdmVyRHVlPy5kYXlzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzaz8uaXNPdmVyRHVlPy5zdGF0dXMgPT09ICdVcGNvbWluZycgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2s/LmlzT3ZlckR1ZT8uZGF5c31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Rhc2s/LmlzT3ZlckR1ZT8uc3RhdHVzID09PSAnQ29tcGxldGVkJyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNFbXB0eSh0YXNrPy5pc092ZXJEdWU/LmRheXMpICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrPy5pc092ZXJEdWU/LnN0YXR1c31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Rhc2s/LmlzT3ZlckR1ZT8uc3RhdHVzID09PSAnQ29tcGxldGVkJyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIWlzRW1wdHkodGFzaz8uaXNPdmVyRHVlPy5kYXlzKSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzaz8uaXNPdmVyRHVlPy5kYXlzICE9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdTYXZlIEFzIERyYWZ0JyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzaz8uaXNPdmVyRHVlPy5kYXlzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE0IGZsZXggaXRlbXMtY2VudGVyIHBsLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzay5Db21tZW50cyAhPT0gbnVsbCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb3BvdmVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvcG92ZXJUcmlnZ2VyIGFzQ2hpbGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm91dGxpbmUtbm9uZSBweC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGF0QnViYmxlQm90dG9tQ2VudGVyVGV4dEljb24gY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXJUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvcG92ZXJDb250ZW50IGNsYXNzTmFtZT1cInctNjRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxlYWRpbmctbG9vc2VcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Rhc2suY29tbWVudHN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXJDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3Rhc2suZGVzY3JpcHRpb24gJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Rhc2suZGVzY3JpcHRpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICd2ZXNzZWwnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdWZXNzZWwnLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnY2VudGVyJyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdGFzayA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHt0YXNrPy5iYXNpY0NvbXBvbmVudD8udGl0bGUgfHwgJ04vQSd9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAnaW52ZW50b3J5SXRlbScsXHJcbiAgICAgICAgICAgIGhlYWRlcjogJ0ludmVudG9yeSBJdGVtJyxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHRhc2sgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIC8vIENoZWNrIGZvciBpbnZlbnRvcnkgZnJvbSBtdWx0aXBsZSBwb3NzaWJsZSBzb3VyY2VzXHJcbiAgICAgICAgICAgICAgICBjb25zdCBpbnZlbnRvcnlJdGVtID1cclxuICAgICAgICAgICAgICAgICAgICB0YXNrPy5pbnZlbnRvcnk/Lml0ZW0gfHxcclxuICAgICAgICAgICAgICAgICAgICB0YXNrPy5tYWludGVuYW5jZVNjaGVkdWxlPy5pbnZlbnRvcnk/Lml0ZW0gfHxcclxuICAgICAgICAgICAgICAgICAgICBudWxsXHJcblxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+e2ludmVudG9yeUl0ZW0gfHwgJ04vQSd9PC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAnc3RhdHVzJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAnU3RhdHVzJyxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ3JpZ2h0JyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdGFzayA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtYi0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBEdWU6IHtmb3JtYXREYXRlKHRhc2suZXhwaXJlcyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BpbmxpbmUtYmxvY2sgcm91bmRlZCBweC0zIHB5LTEgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrLnN0YXR1cyA9PSAnQ29tcGxldGVkJyA/ICdzdWNjZXNzJyA6ICdhbGVydCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPnt0YXNrLnN0YXR1c308L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICBdXHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8PlxyXG4gICAgICAgICAgICB7IXRhc2tMaXN0ID8gKFxyXG4gICAgICAgICAgICAgICAgPExpc3QgLz5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVcclxuICAgICAgICAgICAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxyXG4gICAgICAgICAgICAgICAgICAgIGRhdGE9e2ZpbHRlcmVkVGFza0xpc3QgfHwgW119XHJcbiAgICAgICAgICAgICAgICAgICAgc2hvd1Rvb2xiYXI9e3RydWV9XHJcbiAgICAgICAgICAgICAgICAgICAgcGFnZVNpemU9ezUwfVxyXG4gICAgICAgICAgICAgICAgICAgIHNob3dQYWdlU2l6ZVNlbGVjdG9yPXtmYWxzZX1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlRmlsdGVyT25DaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgIDwvPlxyXG4gICAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBDcmV3QWxsb2NhdGVkVGFza3NcclxuIl0sIm5hbWVzIjpbIkxpbmsiLCJDaGF0QnViYmxlQm90dG9tQ2VudGVyVGV4dEljb24iLCJpc0VtcHR5IiwidXNlUGF0aG5hbWUiLCJ1c2VTZWFyY2hQYXJhbXMiLCJmb3JtYXREYXRlIiwiTGlzdCIsIkRhdGFUYWJsZSIsIkJ1dHRvbiIsIlBvcG92ZXIiLCJQb3BvdmVyQ29udGVudCIsIlBvcG92ZXJUcmlnZ2VyIiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDcmV3QWxsb2NhdGVkVGFza3MiLCJ0YXNrTGlzdCIsInBhdGhuYW1lIiwic2VhcmNoUGFyYW1zIiwiZmlsdGVyZWRUYXNrTGlzdCIsInNldEZpbHRlcmVkVGFza0xpc3QiLCJmaWx0ZXJzIiwic2V0RmlsdGVycyIsInZlc3NlbCIsInN0YXR1cyIsImtleXdvcmQiLCJjcmV3SWQiLCJnZXQiLCJsZW5ndGgiLCJjb25zb2xlIiwibG9nIiwiYXNzaWduZWRUb0lEIiwiU2V0IiwibWFwIiwidCIsImZpbHRlcmVkIiwidmVzc2VsSWRzIiwiQXJyYXkiLCJpc0FycmF5IiwiaXRlbSIsIlN0cmluZyIsInZhbHVlIiwiZmlsdGVyIiwidGFzayIsInRhc2tWZXNzZWxJZCIsImJhc2ljQ29tcG9uZW50IiwiaWQiLCJpbmNsdWRlcyIsInRyaW0iLCJ0b0xvd2VyQ2FzZSIsImJlZm9yZUNvdW50IiwiZ2V0TmFtZSIsIm5hbWUiLCJnZXREZXNjcmlwdGlvbiIsImRlc2NyaXB0aW9uIiwiZ2V0Q29tbWVudHMiLCJjb21tZW50cyIsInJlcGxhY2UiLCJuYW1lTWF0Y2giLCJkZXNjTWF0Y2giLCJjb21tZW50TWF0Y2giLCJtYXRjaCIsImhhbmRsZUZpbHRlck9uQ2hhbmdlIiwidHlwZSIsImRhdGEiLCJwcmV2IiwiY29sdW1ucyIsImFjY2Vzc29yS2V5IiwiaGVhZGVyIiwiY2VsbEFsaWdubWVudCIsImNlbGwiLCJyb3ciLCJvcmlnaW5hbCIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJocmVmIiwidG9TdHJpbmciLCJpc092ZXJEdWUiLCJkYXlzIiwiQ29tbWVudHMiLCJhc0NoaWxkIiwidmFyaWFudCIsInNpemUiLCJ0aXRsZSIsImludmVudG9yeUl0ZW0iLCJpbnZlbnRvcnkiLCJtYWludGVuYW5jZVNjaGVkdWxlIiwiZXhwaXJlcyIsInNob3dUb29sYmFyIiwicGFnZVNpemUiLCJzaG93UGFnZVNpemVTZWxlY3RvciIsIm9uQ2hhbmdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\n"));

/***/ })

});
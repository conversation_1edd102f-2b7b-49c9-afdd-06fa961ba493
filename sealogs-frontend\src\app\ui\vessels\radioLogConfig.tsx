'use client'
import { useLazyQuery, useMutation } from '@apollo/client'
import { GET_RADIO_LOGS } from '@/app/lib/graphQL/query'
import { CREATE_RADIO_LOG, UPDATE_RADIO_LOG } from '@/app/lib/graphQL/mutation'
import { useEffect, useState } from 'react'
import { Button, Heading } from 'react-aria-components'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import { AlertDialogNew } from '@/components/ui'

export default function RadioLogConfig({ vessel }: { vessel: any }) {
    const [radioLogs, setRadioLogs] = useState<any>([])
    const [displayLogAlert, setDisplayLogAlert] = useState(false)
    const [radioTitle, setRadioTitle] = useState('')
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentLog, setCurrentLog] = useState<any>(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [state, setState] = useState<any>([])

    const [getRadioLogs] = useLazyQuery(GET_RADIO_LOGS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readRadioLogs.nodes
            if (data) {
                setRadioLogs(data)
                setState(
                    data?.map((log: any) => ({
                        id: log?.id,
                        title: log?.title,
                    })),
                )
            }
        },
        onError: (error: any) => {
            console.error('readRadioLogs error', error)
        },
    })

    useEffect(() => {
        if (vessel.id > 0) {
            getRadioLogs({
                variables: {
                    filter: {
                        vesselID: { eq: +vessel.id },
                    },
                },
            })
        }
    }, [])

    const [createRadioLog] = useMutation(CREATE_RADIO_LOG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createRadioLog
            if (data) {
                getRadioLogs({
                    variables: {
                        filter: {
                            vesselID: { eq: +vessel.id },
                        },
                    },
                })
                setDisplayLogAlert(false)
            }
        },
        onError: (error: any) => {
            console.error('createRadioLog error', error)
        },
    })

    const [updateRadioLog] = useMutation(UPDATE_RADIO_LOG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateRadioLog
            if (data) {
                getRadioLogs({
                    variables: {
                        filter: {
                            vesselID: { eq: +vessel.id },
                        },
                    },
                })
                setDisplayLogAlert(false)
            }
        },
        onError: (error: any) => {
            console.error('updateRadioLog error', error)
        },
    })

    const handleAddRadioLog = () => {
        setDisplayLogAlert(false)
        if (currentLog) {
            updateRadioLog({
                variables: {
                    input: {
                        id: currentLog.id,
                        title: radioTitle,
                    },
                },
            })
        } else {
            createRadioLog({
                variables: {
                    input: {
                        vesselID: +vessel.id,
                        title: radioTitle,
                    },
                },
            })
        }
    }

    const handleSortRadioLogs = (newState: any) => {
        setState(newState)
        newState.forEach((item: any, index: number) => {
            const log = radioLogs.find((log: any) => log.id === item.id)
            if (log && log.order !== index + 1) {
                updateRadioLog({
                    variables: {
                        input: {
                            id: log.id,
                            order: index + 1,
                        },
                    },
                })
            }
        })
    }

    return (
        <div className="w-full">
            <div className="p-4">
                <div className="flex flex-col items-start">
                    {radioLogs && radioLogs.length > 0 ? (
                        <div className="w-full">
                            {radioLogs.map((log: any) => (
                                <div className="flex flex-row gap-2 mb-2 justify-between items-center">
                                    <span className="text-sm lg:text-base">
                                        <Button
                                            onPress={() => {
                                                setDisplayLogAlert(true)
                                                setCurrentLog(log)
                                            }}>
                                            {log.title}
                                        </Button>
                                    </span>
                                    <SeaLogsButton
                                        type="text"
                                        action={() => {
                                            updateRadioLog({
                                                variables: {
                                                    input: {
                                                        id: log.id,
                                                        vesselID: 0,
                                                    },
                                                },
                                            })
                                        }}
                                        text="Delete"
                                        className="text-red-500"
                                    />
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="flex justify-center items-start h-full">
                            <p className="text-gray-500">No Radio Logs</p>
                        </div>
                    )}
                    <SeaLogsButton
                        text="Add Radio Log"
                        type="text"
                        icon="plus"
                        className="ml-2 mt-4"
                        action={() => {
                            setDisplayLogAlert(true)
                            setCurrentLog(false)
                        }}
                    />
                </div>
            </div>
            <AlertDialogNew
                openDialog={displayLogAlert}
                setOpenDialog={setDisplayLogAlert}
                handleCreate={handleAddRadioLog}
                actionText={currentLog ? 'Update' : 'Create'}>
                <Heading
                    slot="title"
                    className="text-2xl font-light leading-6 my-2 ">
                    {currentLog ? 'Edit' : 'Create'} Radio Log
                </Heading>
                <div className="my-4 flex items-center">
                    <div className="flex flex-col w-full">
                        <Label htmlFor="radioLogTitle">Title</Label>
                        <Input
                            type="text"
                            id="radioLogTitle"
                            placeholder="Enter Location/Title"
                            defaultValue={currentLog?.title}
                            required
                            onChange={(e) => {
                                setRadioTitle(e.target.value)
                            }}
                        />
                    </div>
                </div>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={() => {
                    updateRadioLog({
                        variables: {
                            input: {
                                id: currentLog.id,
                                comment: currentComment,
                            },
                        },
                    })
                    setOpenCommentAlert(false)
                }}
                actionText="Update">
                <Heading
                    slot="title"
                    className="text-2xl font-light leading-6 my-2 ">
                    Comment
                </Heading>
                <div className="my-4 flex items-center">
                    <div className="flex flex-col w-full">
                        <Textarea
                            id="radioLogComment"
                            placeholder="Enter Comment"
                            defaultValue={currentComment}
                            required
                            onChange={(e) => {
                                setCurrentComment(e.target.value)
                            }}
                        />
                    </div>
                </div>
            </AlertDialogNew>
        </div>
    )
}

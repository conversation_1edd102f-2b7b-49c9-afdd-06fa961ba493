// src/components/BreadcrumbNavigation.tsx
'use client'

import React, { useMemo } from 'react'
import {
    Breadcrumb,
    BreadcrumbItem as UiCrumb,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { useVesselContext } from '../hooks/useVesselContext'
import { useCrewContext } from '../hooks/useCrewContext'

type Crumb = {
    label: string
    path: string
    href?: string
    dynamic?: boolean
    parent?: string
}

export function BreadcrumbNavigation() {
    const { vesselID, vesselNameParam, vesselName, pathname } =
        useVesselContext()
    const { crewMemberID, crewMemberName } = useCrewContext()
    const name = vesselNameParam

    const routes: Record<string, Crumb> = {
        '': { label: 'Port', path: '', href: '/' },
        vessel: { label: 'Vessel', path: 'vessel', parent: '' },
        'vessel/info': {
            label: name || 'Vessel Info',
            path: 'vessel/info',
            parent: 'vessel',
            dynamic: true,
        },
        crew: { label: 'Crew', path: 'crew', parent: '' },
        'crew/info': {
            label: crewMemberName || 'Crew Info',
            path: 'crew/info',
            parent: 'crew',
            dynamic: true,
        },
        training: { label: 'Crew Training', path: 'training', parent: '' },
        'training/info': {
            label: 'Info',
            path: 'training/info',
            parent: 'training',
        },
        evaluations: {
            label: 'Risk Evaluations',
            path: 'evaluations',
            parent: '',
        },
        strategies: {
            label: 'Risk Strategies',
            path: 'strategies',
            parent: '',
        },
        inventory: { label: 'Inventory', path: 'inventory', parent: '' },
        'inventory/view': {
            label: 'View',
            path: 'inventory/view',
            parent: 'inventory',
        },
        'inventory/suppliers': {
            label: 'Inventory Suppliers',
            path: 'inventory/suppliers',
            parent: '',
        },
        'inventory/suppliers/view': {
            label: 'View',
            path: 'inventory/suppliers/view',
            parent: 'inventory/suppliers',
        },
        maintenance: {
            label: 'Maintenance',
            path: 'maintenance',
            parent: 'vessel',
        },
        'maintenance/view': {
            label: 'View',
            path: 'maintenance/view',
            parent: 'maintenance',
        },
        'maintenance/add': {
            label: 'Add',
            path: 'maintenance/add',
            parent: 'maintenance',
        },
        'maintenance/edit': {
            label: 'Edit',
            path: 'maintenance/edit',
            parent: 'maintenance',
        },
        locker: { label: 'Document Locker', path: 'locker', parent: '' },
        'log-entries/view': {
            label: 'Logbook',
            path: 'log-entries/view',
            parent: 'vessel',
        },
    }

    // figure out which route key matches best
    const matchingPath = useMemo(() => {
        const current = pathname.substring(1)
        return (
            Object.keys(routes)
                .filter((p) => current === p || current.startsWith(p + '/'))
                .sort((a, b) => b.length - a.length)[0] || ''
        )
    }, [pathname, routes, crewMemberName])

    const breadcrumbs = useMemo<Crumb[]>(() => {
        // recursively build the parent chain
        const buildTrail = (key: string): Crumb[] => {
            const r = routes[key]
            if (!r) return []
            const parentTrail = r.parent ? buildTrail(r.parent) : []
            return [...parentTrail, r]
        }

        // always start with Home
        let trail: Crumb[] = [routes['']]

        if (matchingPath) {
            const pathTrail = buildTrail(matchingPath).filter(
                (c) => c.path !== '',
            )

            // insert dynamic vessel name where appropriate
            const needsVesselName =
                (matchingPath === 'log-entries/view' ||
                    matchingPath === 'maintenance' ||
                    matchingPath.startsWith('maintenance/')) &&
                vesselName &&
                vesselID > 0

            if (needsVesselName) {
                const idx = pathTrail.findIndex((c) => c.path === 'vessel')
                const vesselCrumb: Crumb = {
                    label: vesselName,
                    path: 'vessel-name',
                    href: `/vessel/info?id=${vesselID}&name=${encodeURIComponent(
                        vesselName,
                    )}`,
                }
                if (idx !== -1) {
                    pathTrail.splice(idx + 1, 0, vesselCrumb)
                } else {
                    trail.push(routes['vessel'])
                    trail.push(vesselCrumb)
                }
            }

            trail = [...trail, ...pathTrail]
        }

        // **NEW**: drop the static “vessel” crumb whenever we’re on a vessel page
        return trail.filter((c) => c.path !== 'vessel')
    }, [matchingPath, routes, vesselID, vesselName, crewMemberName])

    return (
        <Breadcrumb>
            <BreadcrumbList className="flex-nowrap">
                {breadcrumbs.map((item, i) => (
                    <React.Fragment key={item.path}>
                        {i > 0 && <BreadcrumbSeparator />}
                        <UiCrumb className="grid">
                            {i < breadcrumbs.length - 1 ? (
                                <BreadcrumbLink
                                    className="truncate min-w-7"
                                    href={item.href || `/${item.path}`}>
                                    {item.label}
                                </BreadcrumbLink>
                            ) : (
                                <BreadcrumbPage>{item.label}</BreadcrumbPage>
                            )}
                        </UiCrumb>
                    </React.Fragment>
                ))}
            </BreadcrumbList>
        </Breadcrumb>
    )
}

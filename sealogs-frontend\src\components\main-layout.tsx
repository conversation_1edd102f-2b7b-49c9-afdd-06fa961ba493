'use client'

import { usePathname, useSearchParams } from 'next/navigation'
import { ReactNode, useState } from 'react'
import { SidebarInset, SidebarProvider, SidebarTrigger } from './ui/sidebar'
import { AppSidebar } from './app-sidebar'
import { <PERSON>rollArea, Separator } from './ui'
import { BreadcrumbNavigation } from './ui/breadcrumb-navigation'
import RightSidebar from './right-sidebar'

interface IProps {
    children: ReactNode
}

export default function MainLayout({ children }: IProps) {
    const searchParams = useSearchParams()
    const [sidebarOption, setSidebarOption] = useState({
        sidebarClass: 'w-64 hidden lg:block',
        menuIconDisplay: true,
        notification: false,
    })
    const [navTab, setNavTab] = useState('dashboard')

    const pathname = usePathname()
    const path = pathname.substring(1)
    const lastPart = path.substring(path.lastIndexOf('-') + 1)
    return (
        <div className="flex flex-row bg-muted min-h-screen max-h-screen h-full p-0 md:px-2 md:pt-2 lg:px-3 lg:pt-3 overflow-hidden w-auto text-foreground">
            <SidebarProvider>
                <AppSidebar />
                <SidebarInset>
                    <header className="flex h-12 shrink-0 items-center gap-2 px-4 border-b border-border text-sm">
                        <SidebarTrigger className="-ml-1 text-primary" />
                        <Separator
                            orientation="vertical"
                            className="mr-2 h-4"
                        />
                        <BreadcrumbNavigation />
                    </header>
                    <ScrollArea>
                        <div className="flex-1 flex flex-col pt-0 p-3 small:p-4 phablet:p-5 lg:p-6 xl:p-8">
                            {children}
                        </div>
                    </ScrollArea>
                    <RightSidebar />
                </SidebarInset>
            </SidebarProvider>
        </div>
    )
}

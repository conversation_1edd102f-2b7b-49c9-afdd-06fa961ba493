import { useEffect, useState } from 'react'
import LocationField from '../logbook/components/location'
import dayjs from 'dayjs'
import { debounce } from 'lodash'
import TimeField from '../logbook/components/time'
import {
    CreateWeatherTide,
    DeleteWeatherTides,
    UpdateWeatherTide,
} from '@/app/lib/graphQL/mutation'
import { useMutation } from '@apollo/client'
import WeatherTideList from './tide-list'
import { formatDate } from '@/app/helpers/dateHelper'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
// Button is used by other components
import { Check, Plus, Trash, X, Calendar } from 'lucide-react'
import DatePicker from '@/components/DateRange'
import { Button } from '@/components/ui'
const Tide = ({
    logBookEntryID,
    locked,
}: {
    logBookEntryID: number
    locked: boolean
}) => {
    const [isWriteMode, setIsWriteMode] = useState(false)
    const [isTideDataLoading, setIsTideDataLoading] = useState(false)
    const [tide, setTide] = useState({} as any)
    const [tideDate, setTideDate] = useState(new Date())
    const [dateRange, setDateRange] = useState<{
        startDate?: Date
        endDate?: Date
    }>({ startDate: new Date(), endDate: undefined })
    const [refreshList, setRefreshList] = useState(false)
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: null,
        longitude: null,
    })
    const [selectedCoordinates, setSelectedCoordinates] = useState<any>({
        latitude: null,
        longitude: null,
    })
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const initTide = () => {
        setTideDate(new Date())
        setTide({
            id: 0,
            tideDate: dayjs().format('YYYY-MM-DD'),
            geoLocationID: 0,
            lat: 0,
            long: 0,
            logBookEntryID: logBookEntryID,
        })
    }
    const createTide = () => {
        initTide()
        setIsWriteMode(true)
    }
    const processTideData = (data: any) => {
        let tidalStation = data.station
        let todayTimestamp = new Date(tideDate).setHours(0, 0, 0, 0)
        let i = 0
        let firsthighdone = false,
            firstlowdone = false

        data.extremes.sort((a: any, b: any) => {
            return a.dt - b.dt
        })

        let firstHighTideTime,
            firstHighTideHeight,
            secondHighTideTime,
            secondHighTideHeight,
            firstLowTideTime,
            firstLowTideHeight,
            secondLowTideTime,
            secondLowTideHeight

        data.extremes.forEach((tide: any) => {
            let tideTimestamp = tide.dt * 1000
            // Use height value directly in the time string
            let tideTime = new Date(tide.date).toLocaleTimeString([], {
                timeZone: tide.timezone,
                hour: '2-digit',
                minute: '2-digit',
                hourCycle: 'h23',
            })

            if (tideTimestamp >= todayTimestamp && i <= 4) {
                if (tide.type == 'High') {
                    if (firsthighdone) {
                        secondHighTideTime = tideTime
                        secondHighTideHeight = tide.height
                    } else {
                        firstHighTideTime = tideTime
                        firstHighTideHeight = tide.height
                        firsthighdone = true
                    }
                } else {
                    if (firstlowdone) {
                        secondLowTideTime = tideTime
                        secondLowTideHeight = tide.height
                    } else {
                        firstLowTideTime = tideTime
                        firstLowTideHeight = tide.height
                        firstlowdone = true
                    }
                    i++
                }
            }
        })

        setTide({
            ...tide,
            firstHighTideTime: firstHighTideTime,
            firstHighTideHeight: firstHighTideHeight,
            secondHighTideTime: secondHighTideTime,
            secondHighTideHeight: secondHighTideHeight,
            firstLowTideTime: firstLowTideTime,
            firstLowTideHeight: firstLowTideHeight,
            secondLowTideTime: secondLowTideTime,
            secondLowTideHeight: secondLowTideHeight,
            tidalStation: tidalStation,
            //    tidalStationDistance: tidalStationDistance,
        })

        setIsTideDataLoading(false)
    }
    const IsTideButtonEnabled = () => {
        let IsTideButtonEnabled = false
        if (+tide.geoLocationID > 0) {
            IsTideButtonEnabled = true
        } else if (+tide.lat > 0 || +tide.long > 0) {
            IsTideButtonEnabled = true
        }
        return IsTideButtonEnabled
    }
    const getTideData = () => {
        setIsTideDataLoading(false)
        if ('geolocation' in navigator) {
            setIsTideDataLoading(true)
            return new Promise((resolve, reject) => {
                let startDate = dayjs(new Date(tideDate).toString()).format(
                    'YYYY-MM-DD',
                )

                let request = fetch(
                    `https://www.worldtides.info/api/v3?extremes&key=${process.env.WORLDTIDES_API_KEY}&datum=LAT&lat=${selectedCoordinates.latitude}&lon=${selectedCoordinates.longitude}&date=${startDate}&stationDistance=${100}&days=1&localtime`,
                    {
                        method: 'GET',
                    },
                )
                request
                    .then((response) => response.json())
                    .then((jsonData) => {
                        processTideData(jsonData)
                        resolve(jsonData)
                    })
                    .catch((error) => {
                        setIsTideDataLoading(false)
                        console.error('Error', error)
                        reject(error)
                    })

                return request
            })
        } else {
            console.error('Geolocation is not supported by your browser')
        }
    }
    const handleOnChangeDate = (dateValue: any) => {
        if (dateValue && dateValue.startDate) {
            const date = dateValue.startDate
            setTideDate(new Date(date))
            setDateRange(dateValue)
            setTide({
                ...tide,
                tideDate: dayjs(date).format('YYYY-MM-DD'),
            })
        }
    }
    const handleSetCurrentLocation = (value: any) => {
        setTide({
            ...tide,
            geoLocationID: 0,
            lat: value.latitude,
            long: value.longitude,
        })
        setSelectedCoordinates({
            latitude: value.latitude,
            longitude: value.longitude,
        })
    }
    const handleLocationChange = (value: any) => {
        // If value is null or undefined, return early
        if (!value) return

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setTide({
                ...tide,
                geoLocationID: +value.value,
                lat: null,
                long: null,
            })

            // If the value object has latitude and longitude, use them directly
            if (value.latitude !== undefined && value.longitude !== undefined) {
                setSelectedCoordinates({
                    latitude: value.latitude,
                    longitude: value.longitude,
                })
            }
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setTide({
                ...tide,
                geoLocationID: 0, // Reset geoLocationID when using direct coordinates
                lat: value.latitude,
                long: value.longitude,
            })

            // Update selected coordinates
            setSelectedCoordinates({
                latitude: value.latitude,
                longitude: value.longitude,
            })
        }
    }
    const handleSetComment = debounce((item: any) => {
        setTide({
            ...tide,
            comment: item,
        })
    }, 600)
    const handleCancel = () => {
        setIsWriteMode(false)
        initTide()
    }
    const [createWeatherTide, { loading: createWeatherTideLoading }] =
        useMutation(CreateWeatherTide, {
            onCompleted: () => {
                setIsWriteMode(false)
                setRefreshList(true)
            },
            onError: (error) => {
                console.error('CreateWeatherTide Error', error)
            },
        })
    const [updateWeatherTide, { loading: updateWeatherTideLoading }] =
        useMutation(UpdateWeatherTide, {
            onCompleted: () => {
                setIsWriteMode(false)
                setRefreshList(true)
            },
            onError: (error) => {
                console.error('UpdateWeatherTide Error', error)
            },
        })
    const handleSave = async () => {
        if (+tide.id === 0) {
            await createWeatherTide({
                variables: {
                    input: {
                        ...tide,
                    },
                },
            })
        } else {
            if (tide.geoLocation) delete tide.geoLocation
            if (tide.__typename) delete tide.__typename
            await updateWeatherTide({
                variables: {
                    input: {
                        ...tide,
                    },
                },
            })
        }
    }
    const handleFirstHighTideTimeChange = (value: any) => {
        const time = dayjs(value).format('HH:mm')
        setTide({
            ...tide,
            firstHighTideTime: time,
        })
    }
    const handleFirstHighTideHeightChange = (value: number) => {
        setTide({
            ...tide,
            firstHighTideHeight: value,
        })
    }
    const handleSecondHighTideTimeChange = (value: any) => {
        const time = dayjs(value).format('HH:mm')
        setTide({
            ...tide,
            secondHighTideTime: time,
        })
    }
    const handleSecondHighTideHeightChange = (value: number) => {
        setTide({
            ...tide,
            secondHighTideHeight: value,
        })
    }
    const handleFirstLowTideTimeChange = (value: any) => {
        const time = dayjs(value).format('HH:mm')
        setTide({
            ...tide,
            firstLowTideTime: time,
        })
    }
    const handleFirstLowTideHeightChange = (value: number) => {
        setTide({
            ...tide,
            firstLowTideHeight: value,
        })
    }
    const handleSecondLowTideTimeChange = (value: any) => {
        const time = dayjs(value).format('HH:mm')
        setTide({
            ...tide,
            secondLowTideTime: time,
        })
    }
    const handleSecondLowTideHeightChange = (value: number) => {
        setTide({
            ...tide,
            secondLowTideHeight: value,
        })
    }
    const formatTime = (time: string) => {
        return dayjs(`${dayjs().format('YYYY-MM-DD')} ${time}`).format('HH:mm')
    }
    const handleTideClick = (tide: any) => {
        setTideDate(new Date(tide.tideDate))
        const newTide = {
            ...tide,
            firstHighTideTime: formatTime(tide.firstHighTideTime),
            firstLowTideTime: formatTime(tide.firstLowTideTime),
            secondHighTideTime: formatTime(tide.secondHighTideTime),
            secondLowTideTime: formatTime(tide.secondLowTideTime),
        }
        setTide(newTide)
        setIsWriteMode(true)
    }
    const [deleteWeatherTides, { loading: deleteWeatherTidesLoading }] =
        useMutation(DeleteWeatherTides, {
            onCompleted: () => {
                setIsWriteMode(false)
                setRefreshList(true)
            },
            onError: (error) => {
                console.error('DeleteWeatherTides Error', error)
            },
        })
    const handleDeleteTide = async () => {
        await deleteWeatherTides({
            variables: {
                ids: [tide.id],
            },
        })
    }
    useEffect(() => {
        if (logBookEntryID > 0) {
            setTide({
                ...tide,
                logBookEntryID: logBookEntryID,
            })
        }
    }, [logBookEntryID])

    return (
        <div>
            {!isWriteMode && (
                <div className="flex justify-end">
                    <Button
                        iconLeft={Plus}
                        className={locked ? 'pointer-events-none' : ''}
                        onClick={() => createTide()}
                        disabled={locked}>
                        Add Tide
                    </Button>
                </div>
            )}

            {isWriteMode && (
                <div>
                    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 items-center  ">
                        <div>
                            <Label>Date</Label>
                        </div>
                        <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                            <div className="flex md:items-center flex-col md:flex-row gap-4">
                                <div className="w-full">
                                    <DatePicker
                                        mode="single"
                                        type="date"
                                        value={tideDate}
                                        onChange={handleOnChangeDate}
                                        dateFormat="MMM dd, yyyy"
                                        placeholder="Select date"
                                        clearable={true}
                                        icon={Calendar}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="mt-4">
                            <Label>Location</Label>
                        </div>
                        <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3 mt-4">
                            <div className="flex md:items-center flex-col md:flex-row gap-4">
                                <LocationField
                                    currentTrip={{}}
                                    updateTripReport={{}}
                                    tripReport={{}}
                                    setCurrentLocation={
                                        handleSetCurrentLocation
                                    }
                                    handleLocationChange={handleLocationChange}
                                    currentLocation={currentLocation}
                                    currentEvent={{
                                        geoLocationID: tide.geoLocationID,
                                        lat: tide.lat,
                                        long: tide.long,
                                    }}
                                />
                                {isWriteMode && IsTideButtonEnabled() && (
                                    <div className="flex justify-end">
                                        <Button
                                            className="w-full"
                                            onClick={() => getTideData()}
                                            disabled={
                                                isTideDataLoading ||
                                                createWeatherTideLoading ||
                                                updateWeatherTideLoading ||
                                                deleteWeatherTidesLoading
                                            }>
                                            {isTideDataLoading
                                                ? 'Retrieving Tide Data...'
                                                : 'Retrieve Tide Data'}
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="mt-4">
                            <Label>1st High Tide</Label>
                        </div>
                        <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3 mt-4">
                            <div className="flex md:items-center flex-col md:flex-row gap-4 w-full">
                                <div>
                                    <TimeField
                                        time={tide.firstHighTideTime || false}
                                        handleTimeChange={
                                            handleFirstHighTideTimeChange
                                        }
                                        timeID="firstHighTideTime"
                                        fieldName="Time"
                                        hideButton
                                    />
                                </div>
                                <div className="flex items-center">
                                    <Input
                                        type="number"
                                        value={tide.firstHighTideHeight}
                                        className={'w-full'}
                                        onChange={(event: any) => {
                                            handleFirstHighTideHeightChange(
                                                +event.target.value,
                                            )
                                        }}
                                        step={0.01}
                                    />
                                    <div className="ml-1">meters</div>
                                </div>
                            </div>
                        </div>
                        <div className="mt-4">
                            <Label>1st Low Tide</Label>
                        </div>
                        <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3 mt-4">
                            <div className="flex md:items-center flex-col md:flex-row gap-4 w-full">
                                <div>
                                    <TimeField
                                        time={tide.firstLowTideTime || false}
                                        handleTimeChange={
                                            handleFirstLowTideTimeChange
                                        }
                                        timeID="firstLowTideTime"
                                        fieldName="Time"
                                        hideButton
                                    />
                                </div>
                                <div className="flex items-center">
                                    <Input
                                        type="number"
                                        value={tide.firstLowTideHeight}
                                        className={'w-full'}
                                        onChange={(event: any) => {
                                            handleFirstLowTideHeightChange(
                                                +event.target.value,
                                            )
                                        }}
                                        step={0.01}
                                    />
                                    <div className="ml-1">meters</div>
                                </div>
                            </div>
                        </div>
                        <div className="mt-4">
                            <Label>2nd High Tide</Label>
                        </div>
                        <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3 mt-4">
                            <div className="flex md:items-center flex-col md:flex-row gap-4 w-full">
                                <div>
                                    <TimeField
                                        time={tide.secondHighTideTime || false}
                                        handleTimeChange={
                                            handleSecondHighTideTimeChange
                                        }
                                        timeID="secondHighTideTime"
                                        fieldName="Time"
                                        hideButton
                                    />
                                </div>
                                <div className="flex items-center">
                                    <Input
                                        type="number"
                                        value={tide.secondHighTideHeight}
                                        className={'w-full'}
                                        onChange={(event: any) => {
                                            handleSecondHighTideHeightChange(
                                                +event.target.value,
                                            )
                                        }}
                                        step={0.01}
                                    />
                                    <div className="ml-1">meters</div>
                                </div>
                            </div>
                        </div>
                        <div className="mt-4">
                            <Label>2nd Low Tide</Label>
                        </div>
                        <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3 mt-4">
                            <div className="flex md:items-center flex-col md:flex-row gap-4 w-full">
                                <div>
                                    <TimeField
                                        time={tide.secondLowTideTime || false}
                                        handleTimeChange={
                                            handleSecondLowTideTimeChange
                                        }
                                        timeID="secondLowTideTime"
                                        fieldName="Time"
                                        hideButton
                                    />
                                </div>
                                <div className="flex items-center">
                                    <Input
                                        type="number"
                                        value={tide.secondLowTideHeight}
                                        className={'w-full'}
                                        onChange={(event: any) => {
                                            handleSecondLowTideHeightChange(
                                                +event.target.value,
                                            )
                                        }}
                                        step={0.01}
                                    />
                                    <span className="ml-1">meters</span>
                                </div>
                            </div>
                        </div>
                        <div className="mt-4">
                            <Label>Comment</Label>
                        </div>
                        <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3 mt-4">
                            <div className="flex md:items-center flex-col md:flex-row gap-4">
                                <textarea
                                    id={`tide-comment`}
                                    rows={4}
                                    className={''}
                                    placeholder="Comments ..."
                                    defaultValue={tide.comment || ''}
                                    onChange={(e: any) =>
                                        handleSetComment(e.target.value)
                                    }></textarea>
                            </div>
                        </div>
                    </div>
                    <div className="flex my-4 justify-end">
                        <Button
                            variant="text"
                            iconLeft={X}
                            className="mr-4"
                            onClick={handleCancel}
                            disabled={
                                isTideDataLoading ||
                                createWeatherTideLoading ||
                                updateWeatherTideLoading ||
                                deleteWeatherTidesLoading
                            }>
                            Cancel
                        </Button>
                        {+tide.id > 0 && (
                            <>
                                <Button
                                    variant="destructive"
                                    iconLeft={Trash}
                                    disabled={
                                        isTideDataLoading ||
                                        createWeatherTideLoading ||
                                        updateWeatherTideLoading ||
                                        deleteWeatherTidesLoading
                                    }
                                    onClick={() => {
                                        // Use the AlertDialogNew component directly
                                        setDeleteDialogOpen(true)
                                    }}>
                                    Delete
                                </Button>

                                <AlertDialogNew
                                    openDialog={deleteDialogOpen}
                                    setOpenDialog={setDeleteDialogOpen}
                                    title="Delete Tide Data"
                                    description={`Are you sure you want to delete the tide data for ${formatDate(tide.tideDate)}?`}
                                    handleCreate={handleDeleteTide}
                                    actionText="Delete"
                                    cancelText="Cancel"
                                    variant="danger"
                                />
                            </>
                        )}
                        <Button
                            iconLeft={Check}
                            onClick={handleSave}
                            disabled={
                                isTideDataLoading ||
                                createWeatherTideLoading ||
                                updateWeatherTideLoading ||
                                deleteWeatherTidesLoading
                            }>
                            {`${+tide.id === 0 ? 'Create' : 'Update'} Tide`}
                        </Button>
                    </div>
                </div>
            )}
            {!isWriteMode && (
                <div className="mt-4">
                    <WeatherTideList
                        logBookEntryID={logBookEntryID}
                        refreshList={refreshList}
                        onClick={handleTideClick}
                    />
                </div>
            )}
        </div>
    )
}

export default Tide

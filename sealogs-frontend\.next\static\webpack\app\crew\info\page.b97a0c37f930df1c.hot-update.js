"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/crew/allocated-tasks.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleBottomCenterTextIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.3.1/node_modules/@heroicons/react/24/outline/esm/ChatBubbleBottomCenterTextIcon.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst CrewAllocatedTasks = (param)=>{\n    let { taskList } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [filteredTaskList, setFilteredTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(taskList);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        vessel: null,\n        status: null,\n        keyword: null\n    });\n    // Update filtered list when taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        setFilteredTaskList(taskList);\n    }, [\n        taskList\n    ]);\n    // Apply all filters whenever filters or taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (!taskList) return;\n        let filtered = [\n            ...taskList\n        ];\n        // Apply vessel filter - following the same pattern as maintenance list\n        if (filters.vessel) {\n            // Handle both single and multi-select vessel filtering\n            let vesselIds = [];\n            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {\n                vesselIds = filters.vessel.map((item)=>String(item.value));\n            } else if (filters.vessel && !Array.isArray(filters.vessel)) {\n                vesselIds = [\n                    String(filters.vessel.value)\n                ];\n            }\n            if (vesselIds.length > 0) {\n                filtered = filtered.filter((task)=>{\n                    var _task_basicComponent;\n                    const taskVesselId = String(task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.id);\n                    return vesselIds.includes(taskVesselId);\n                });\n            }\n        }\n        // Apply status filter\n        if (filters.status) {\n            filtered = filtered.filter((task)=>(task === null || task === void 0 ? void 0 : task.status) === filters.status.value);\n        }\n        // Apply keyword filter\n        if (filters.keyword && filters.keyword.value && filters.keyword.value.trim()) {\n            const keyword = filters.keyword.value.toLowerCase().trim();\n            console.log('Applying keyword filter: \"'.concat(keyword, '\"'));\n            const beforeCount = filtered.length;\n            filtered = filtered.filter((task)=>{\n                // Safely get text content, handling null/undefined and HTML\n                const getName = ()=>((task === null || task === void 0 ? void 0 : task.name) || \"\").toLowerCase();\n                const getDescription = ()=>((task === null || task === void 0 ? void 0 : task.description) || \"\").toLowerCase();\n                const getComments = ()=>{\n                    if (!(task === null || task === void 0 ? void 0 : task.comments)) return \"\";\n                    // Strip HTML tags if present and convert to lowercase\n                    return task.comments.replace(/<[^>]*>/g, \"\").toLowerCase();\n                };\n                const nameMatch = getName().includes(keyword);\n                const descMatch = getDescription().includes(keyword);\n                const commentMatch = getComments().includes(keyword);\n                const match = nameMatch || descMatch || commentMatch;\n                if (match) {\n                    console.log('Task \"'.concat(task.name, '\" matches keyword \"').concat(keyword, '\"'));\n                }\n                return match;\n            });\n            console.log(\"Keyword filter: \".concat(beforeCount, \" -> \").concat(filtered.length, \" tasks\"));\n        }\n        setFilteredTaskList(filtered);\n    }, [\n        taskList,\n        filters\n    ]);\n    // Handle filter changes\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        console.log(\"Filter change - type: \".concat(type, \", data:\"), data);\n        if (type === \"keyword\") {\n            console.log(\"Keyword filter data structure:\", data);\n            console.log(\"Keyword value:\", data === null || data === void 0 ? void 0 : data.value);\n        }\n        setFilters((prev)=>({\n                ...prev,\n                [type]: data\n            }));\n    };\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: \"Task\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2, _task_isOverDue3, _task_isOverDue4, _task_isOverDue5, _task_isOverDue6, _task_isOverDue7, _task_isOverDue8, _task_isOverDue9, _task_isOverDue10, _task_isOverDue11, _task_isOverDue12, _task_isOverDue13, _task_isOverDue14, _task_isOverDue15, _task_isOverDue16, _task_isOverDue17, _task_isOverDue18, _task_isOverDue19;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-foreground flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(task.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: \"focus:outline-none\",\n                                            children: task.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block rounded px-3 py-1 ml-3 \".concat((task === null || task === void 0 ? void 0 : (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\" ? \"alert\" : \"\", \" \").concat((task === null || task === void 0 ? void 0 : (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) === \"Low\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.status) === \"Upcoming\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status) === \"Completed\" ? \"success\" : \"\", \" \").concat((task === null || task === void 0 ? void 0 : (_task_isOverDue4 = task.isOverDue) === null || _task_isOverDue4 === void 0 ? void 0 : _task_isOverDue4.status) === \"Medium\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue5 = task.isOverDue) === null || _task_isOverDue5 === void 0 ? void 0 : _task_isOverDue5.days) === \"Save As Draft\" ? \"text-yellow-600 bg-yellow-100\" : \"\"),\n                                            children: [\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue6 = task.isOverDue) === null || _task_isOverDue6 === void 0 ? void 0 : _task_isOverDue6.status) && [\n                                                    \"High\",\n                                                    \"Medium\",\n                                                    \"Low\"\n                                                ].includes(task.isOverDue.status) && (task === null || task === void 0 ? void 0 : (_task_isOverDue7 = task.isOverDue) === null || _task_isOverDue7 === void 0 ? void 0 : _task_isOverDue7.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue8 = task.isOverDue) === null || _task_isOverDue8 === void 0 ? void 0 : _task_isOverDue8.status) === \"Completed\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue9 = task.isOverDue) === null || _task_isOverDue9 === void 0 ? void 0 : _task_isOverDue9.days) === \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue10 = task.isOverDue) === null || _task_isOverDue10 === void 0 ? void 0 : _task_isOverDue10.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue11 = task.isOverDue) === null || _task_isOverDue11 === void 0 ? void 0 : _task_isOverDue11.status) === \"Upcoming\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue12 = task.isOverDue) === null || _task_isOverDue12 === void 0 ? void 0 : _task_isOverDue12.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue13 = task.isOverDue) === null || _task_isOverDue13 === void 0 ? void 0 : _task_isOverDue13.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue14 = task.isOverDue) === null || _task_isOverDue14 === void 0 ? void 0 : _task_isOverDue14.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue15 = task.isOverDue) === null || _task_isOverDue15 === void 0 ? void 0 : _task_isOverDue15.status),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue16 = task.isOverDue) === null || _task_isOverDue16 === void 0 ? void 0 : _task_isOverDue16.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue17 = task.isOverDue) === null || _task_isOverDue17 === void 0 ? void 0 : _task_isOverDue17.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue18 = task.isOverDue) === null || _task_isOverDue18 === void 0 ? void 0 : _task_isOverDue18.days) !== \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue19 = task.isOverDue) === null || _task_isOverDue19 === void 0 ? void 0 : _task_isOverDue19.days)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-14 flex items-center pl-1\",\n                                    children: task.Comments !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"outline-none px-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                                                className: \"w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"leading-loose\",\n                                                    children: task.comments\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 25\n                        }, undefined),\n                        task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1 text-sm text-muted-foreground\",\n                            children: task.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: \"Vessel\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_basicComponent;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: (task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.title) || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"inventoryItem\",\n            header: \"Inventory Item\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_inventory, _task_maintenanceSchedule_inventory, _task_maintenanceSchedule;\n                const task = row.original;\n                // Check for inventory from multiple possible sources\n                const inventoryItem = (task === null || task === void 0 ? void 0 : (_task_inventory = task.inventory) === null || _task_inventory === void 0 ? void 0 : _task_inventory.item) || (task === null || task === void 0 ? void 0 : (_task_maintenanceSchedule = task.maintenanceSchedule) === null || _task_maintenanceSchedule === void 0 ? void 0 : (_task_maintenanceSchedule_inventory = _task_maintenanceSchedule.inventory) === null || _task_maintenanceSchedule_inventory === void 0 ? void 0 : _task_maintenanceSchedule_inventory.item) || null;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: inventoryItem || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-right\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground mb-1\",\n                            children: [\n                                \"Due: \",\n                                (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(task.expires)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block rounded px-3 py-1 \".concat(task.status == \"Completed\" ? \"success\" : \"alert\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: task.status\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: !taskList ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 265,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n            columns: columns,\n            data: filteredTaskList || [],\n            showToolbar: true,\n            pageSize: 50,\n            showPageSizeSelector: false,\n            onChange: handleFilterOnChange\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 267,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s(CrewAllocatedTasks, \"ma6jPe/tw3b936HXYaWPMLLamsk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams\n    ];\n});\n_c = CrewAllocatedTasks;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewAllocatedTasks);\nvar _c;\n$RefreshReg$(_c, \"CrewAllocatedTasks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\n"));

/***/ })

});
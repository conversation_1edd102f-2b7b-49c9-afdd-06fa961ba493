"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/view.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/view.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CrewView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _crew_training_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../crew-training/list */ \"(app-pages-browser)/./src/app/ui/crew-training/list.tsx\");\n/* harmony import */ var _crew_allocated_tasks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../crew/allocated-tasks */ \"(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\");\n/* harmony import */ var _crew_voyages__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../crew/voyages */ \"(app-pages-browser)/./src/app/ui/crew/voyages.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_helpers_maintenanceHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/maintenanceHelper */ \"(app-pages-browser)/./src/app/helpers/maintenanceHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* ---------- shadcn/ui replacements ------------------------------------ */ \n\n\n/* ---------------------------------------------------------------------- */ function CrewView(param) {\n    let { crewId } = param;\n    var _crewInfo_status, _crewInfo_status1, _crewInfo_vehicles, _crewInfo_departments;\n    _s();\n    /* ---------------- state / helpers ----------------------------------- */ const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [tab, setTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"training\");\n    const [taskCounter, setTaskCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dueTrainingCounter, setDueTrainingCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [taskList, setTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [voyages, setVoyages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSelf, setIsSelf] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [archiveOpen, setArchiveOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    /* ---------------- data fetch ---------------------------------------- */ (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getCrewMembersLogBookEntrySections)(crewId, setVoyages);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.getPermissions);\n    }, []);\n    /* vessels ------------------------------------------------------------ */ const handleSetVessels = (vsls)=>{\n        const activeVessels = vsls.filter((v)=>!v.archived);\n        setVessels(activeVessels.map((v)=>({\n                label: v.title,\n                value: v.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getVesselList)(handleSetVessels);\n    /* tasks -------------------------------------------------------------- */ const handleSetTaskList = (tasks)=>{\n        const active = tasks.filter((t)=>!t.archived).map((t)=>({\n                ...t,\n                isOverDue: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.isOverDueTask)(t)\n            }));\n        const list = (0,_app_helpers_maintenanceHelper__WEBPACK_IMPORTED_MODULE_10__.sortMaintenanceChecks)(active.map((mc)=>({\n                id: mc.id,\n                name: mc.name,\n                basicComponentID: mc.basicComponentID,\n                comments: mc.comments,\n                description: mc.description,\n                assignedToID: mc.assignedToID,\n                expires: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.upcomingScheduleDate)(mc),\n                status: mc.status,\n                startDate: mc.startDate,\n                isOverDue: mc.isOverDue,\n                basicComponent: mc.basicComponent,\n                isCompleted: mc.status === \"Completed\" ? \"1\" : \"2\"\n            })));\n        setTaskList(list);\n        setTaskCounter(active.filter((t)=>![\n                \"Completed\",\n                \"Save_As_Draft\"\n            ].includes(t.status) && ![\n                \"Completed\",\n                \"Upcoming\"\n            ].includes(t.isOverDue.status)).length);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getComponentMaintenanceCheckByMemberId)(crewId, handleSetTaskList);\n    /* crew info ---------------------------------------------------------- */ const handleSetCrewInfo = (info)=>{\n        var _withTraining_trainingSessionsDue;\n        setCrewInfo(info);\n        const [withTraining] = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n            info\n        ], vessels);\n        var _withTraining_trainingSessionsDue_nodes_filter;\n        const dues = (_withTraining_trainingSessionsDue_nodes_filter = withTraining === null || withTraining === void 0 ? void 0 : (_withTraining_trainingSessionsDue = withTraining.trainingSessionsDue) === null || _withTraining_trainingSessionsDue === void 0 ? void 0 : _withTraining_trainingSessionsDue.nodes.filter((n)=>n.status.isOverdue || n.status.dueWithinSevenDays)) !== null && _withTraining_trainingSessionsDue_nodes_filter !== void 0 ? _withTraining_trainingSessionsDue_nodes_filter : [];\n        setDueTrainingCounter(dues.length);\n        if (localStorage.getItem(\"userId\") === info.id) setIsSelf(true);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getCrewByID)(crewId, handleSetCrewInfo);\n    /* archive / retrieve user ------------------------------------------- */ const [mutationUpdateUser] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_USER, {\n        onCompleted: ()=>router.back(),\n        onError: (err)=>console.error(\"mutationUpdateUser error\", err)\n    });\n    const handleArchiveUser = async (info)=>{\n        if (!(info && info.id > 0)) return;\n        await mutationUpdateUser({\n            variables: {\n                input: {\n                    id: info.id,\n                    isArchived: !info.isArchived\n                }\n            }\n        });\n    };\n    /* permission helpers ------------------------------------------------- */ const noPerm = (perm)=>!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(perm, permissions);\n    const BadgeCounter = (param)=>{\n        let { count } = param;\n        return count ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"ml-2 flex h-5 w-5 items-center justify-center rounded-full border border-rose-600 bg-rose-100 text-xs font-medium text-rose-600\",\n            children: count\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 162,\n            columnNumber: 13\n        }, this) : null;\n    };\n    /* early exit if no access ------------------------------------------- */ if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_MEMBER\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_MEMBER_CONTACT\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 174,\n            columnNumber: 13\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops! You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 176,\n            columnNumber: 13\n        }, this);\n    }\n    /* active log-book ---------------------------------------------------- */ const activeLog = voyages && voyages.length > 0 && !voyages[0].punchOut ? voyages[0] : null;\n    /* ----------------------- render ------------------------------------ */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col justify-between md:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.H2, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2 font-medium\",\n                                children: \"Crew:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1\",\n                                children: !crewInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_12__.Skeleton, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 29\n                                }, this) : \"\".concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.firstName) || \"\", \" \").concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.surname) || \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                variant: (crewInfo === null || crewInfo === void 0 ? void 0 : (_crewInfo_status = crewInfo.status) === null || _crewInfo_status === void 0 ? void 0 : _crewInfo_status.state) === \"Active\" ? \"primary\" : \"warning\",\n                                className: \"hidden min-w-fit rounded py-0.5 px-1.5 text-sm font-normal lg:inline ms-2\",\n                                children: crewInfo.isArchived ? \"Archived\" : activeLog ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/log-entries/view?&vesselID=\".concat(activeLog.logBookEntry.vehicle.id, \"&logentryID=\").concat(activeLog.logBookEntry.id),\n                                    children: [\n                                        \"Active log book at\",\n                                        \" \",\n                                        activeLog.logBookEntry.vehicle.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 29\n                                }, this) : \"No active log books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                variant: (crewInfo === null || crewInfo === void 0 ? void 0 : (_crewInfo_status1 = crewInfo.status) === null || _crewInfo_status1 === void 0 ? void 0 : _crewInfo_status1.state) === \"Active\" ? \"primary\" : \"warning\",\n                                className: \"block w-max rounded py-0.5 px-1.5 text-sm font-normal lg:hidden mb-2 ms-2\",\n                                children: crewInfo.isArchived ? \"Archived\" : activeLog ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/log-entries/view?&vesselID=\".concat(activeLog.logBookEntry.vehicle.id, \"&logentryID=\").concat(activeLog.logBookEntry.id),\n                                    children: [\n                                        \"Active log book at\",\n                                        \" \",\n                                        activeLog.logBookEntry.vehicle.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 29\n                                }, this) : \"No active log books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center justify-end gap-2\",\n                        children: [\n                            permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                                open: archiveOpen,\n                                onOpenChange: setArchiveOpen,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            children: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                                        className: \"sm:max-w-md\",\n                                        children: (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.DELETE_MEMBER || \"DELETE_MEMBER\", permissions) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                                            className: \"text-2xl\",\n                                                            children: [\n                                                                (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\",\n                                                                \" \",\n                                                                \"User\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogDescription, {\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"retrieve\" : \"archive\",\n                                                                \" \",\n                                                                \"\".concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.firstName) || \"this user\", \" \").concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.surname) || \"\"),\n                                                                \" \",\n                                                                \"?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                                                    className: \"flex justify-end gap-2 pt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                            variant: \"outline\",\n                                                            onClick: ()=>setArchiveOpen(false),\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                            onClick: ()=>{\n                                                                handleArchiveUser(crewInfo);\n                                                                setArchiveOpen(false);\n                                                            },\n                                                            children: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                                        children: \"Warning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-3 text-slate-500\",\n                                                    children: \"You do not have permission to archive user.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                                                    className: \"flex justify-end pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setArchiveOpen(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 29\n                            }, this),\n                            (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) || isSelf) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/user/edit?id=\".concat(crewId)),\n                                children: \"Edit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 25\n                            }, this),\n                            (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) || isSelf) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/user/create\"),\n                                className: \"\".concat(tab === \"training\" ? \"hidden\" : \"\", \" \").concat(tab === \"qualification\" ? \"!mr-0\" : \"\"),\n                                children: \"Add Qualification\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 25\n                            }, this),\n                            permissions && tab !== \"qualification\" && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/crew-training/create?memberId=\".concat(crewId)),\n                                children: \"Record Training\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 190,\n                columnNumber: 13\n            }, this),\n            ((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.email) || (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.vehicles) || (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.phoneNumber)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-[1px] mt-2 mb-3 border-t border-b border-border px-4 pb-4 pt-4\",\n                children: [\n                    (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.primaryDuty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Primary Duty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ms-2\",\n                                children: crewInfo.primaryDuty.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 25\n                    }, this),\n                    [\n                        \"email\",\n                        \"phoneNumber\"\n                    ].map((field)=>(permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.VIEW_MEMBER_CONTACT || \"VIEW_MEMBER_CONTACT\", permissions) || isSelf) && (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo[field]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-4 w-32\",\n                                    children: field === \"email\" ? \"Email:\" : \"Phone:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ms-2\",\n                                    children: crewInfo[field]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, field, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 33\n                        }, this)),\n                    ((_crewInfo_vehicles = crewInfo.vehicles) === null || _crewInfo_vehicles === void 0 ? void 0 : _crewInfo_vehicles.nodes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 mt-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Vessels:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap md:flex-nowrap\",\n                                children: crewInfo.vehicles.nodes.map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(v.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ms-2 my-1 rounded border py-1 px-2 md:my-0\",\n                                            children: v.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, v.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 25\n                    }, this),\n                    ((_crewInfo_departments = crewInfo.departments) === null || _crewInfo_departments === void 0 ? void 0 : _crewInfo_departments.nodes) && localStorage.getItem(\"useDepartment\") === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 mt-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Departments:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap md:flex-nowrap\",\n                                children: crewInfo.departments.nodes.map((d)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/department/info?id=\".concat(d.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ms-2 my-1 rounded border py-1 px-2 md:my-0\",\n                                            children: d.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, d.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 45\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 29\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 381,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.Tabs, {\n                    value: tab,\n                    onValueChange: (v)=>setTab(v),\n                    className: \"pt-2 pb-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsList, {\n                            className: \"gap-2\",\n                            children: [\n                                (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"training\",\n                                    children: [\n                                        \"Training\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeCounter, {\n                                            count: dueTrainingCounter\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                                value: \"qualification\",\n                                                disabled: true,\n                                                children: \"Qualifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                                            side: \"bottom\",\n                                            children: \"Coming soon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"allocatedTasks\",\n                                    children: [\n                                        \"Allocated Tasks\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeCounter, {\n                                            count: taskCounter\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"voyages\",\n                                    children: \"Voyages\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"training\",\n                            children: noPerm(\"VIEW_MEMBER_TRAINING\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_training_list__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                memberId: crewId,\n                                excludeFilters: [\n                                    \"crew\",\n                                    \"overdueToggle\"\n                                ]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"qualification\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"allocatedTasks\",\n                            children: noPerm(\"VIEW_MEMBER_TASKS\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_allocated_tasks__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                taskList: taskList\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"voyages\",\n                            children: noPerm(\"VIEW_MEMBER_VOYAGES\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_voyages__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                voyages: voyages\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 459,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n        lineNumber: 188,\n        columnNumber: 9\n    }, this);\n}\n_s(CrewView, \"bvPAsFmKLc/JaLZuoP8Cy7CjHD4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation\n    ];\n});\n_c = CrewView;\nvar _c;\n$RefreshReg$(_c, \"CrewView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/view.tsx\n"));

/***/ })

});
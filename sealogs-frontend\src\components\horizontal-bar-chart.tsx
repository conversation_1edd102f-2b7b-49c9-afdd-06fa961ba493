"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>A<PERSON><PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

export function HorizontalBarChartComponent({
    chartConfig,
    chartData,
    cardTitle,
    cardInfo,
} : {
    chartConfig: any
    chartData: any
    cardTitle?: string
    cardInfo?: any
}) {
  return (
    <div className="flex flex-col border-0 shadow-none p-2">
      <div>
          <p>{cardTitle}</p>
          {cardInfo}
      </div>
      <div className="w-2/3">
        <ChartContainer config={chartConfig}>
          <BarChart
            accessibilityLayer
            data={chartData}
            layout="vertical"
            margin={{
              left: 33,
            }}
          >
            <YAxis
              dataKey="title"
              type="category"
              tick={{ fontSize: 14, fill: '#71717A' }}
              tickLine={false}
              tickMargin={5}
              axisLine={{ stroke: '#CBD3D6', strokeDasharray: '4 2' }}
              tickFormatter={(value) =>
                chartConfig[value as keyof typeof chartConfig]?.label
              }
            />
            <XAxis dataKey="amount" type="number" hide domain={[-1, 'dataMax']}/>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel hideIndicator className="chartToolTip" nameKey="title"/>}
            />
            <Bar dataKey="amount" layout="vertical" radius={5} />
          </BarChart>
        </ChartContainer>
      </div>
    </div>
  )
}

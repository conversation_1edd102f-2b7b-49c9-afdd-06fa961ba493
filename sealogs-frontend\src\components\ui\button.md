# Button Component

The Button component is a versatile and enhanced button that supports icons, loading states, tooltips, and responsive behavior.

## Features

-   Multiple variants and sizes
-   Icon support (left, right, or both)
-   Loading state with spinner
-   Tooltip integration
-   Responsive behavior (hides text when space is limited)
-   Icon-only mode with tooltip

## Usage

```tsx
import { Button } from "@/components/ui/button"
import { Save, ArrowRight, Plus, ArrowLeft } from "lucide-react"

// Basic button
<Button>Click me</Button>

// Button with left icon (component reference)
<Button iconLeft={Save}>Save</Button>

// Button with left icon (JSX element)
<Button iconLeft={<Save />}>Save</Button>

// Button with right icon
<Button iconRight={ArrowRight}>Next</Button>

// Button with both left and right icons
<Button iconLeft={ArrowLeft} iconRight={ArrowRight}>Navigate</Button>

// Button with custom React node as icon
<Button iconRight={<ChevronDown size={11.67} />}>Custom Dropdown</Button>

// Icon-only button with tooltip
<Button iconLeft={Plus} iconOnly tooltip="Add new item" />

// Loading state
<Button isLoading>Processing</Button>

// Responsive button (hides text when space is limited)
<Button iconLeft={Save} responsive={true}>Save</Button>

// Different variants
<Button >Default</Button>
<Button >Primary</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="destructive">Destructive</Button>
<Button variant="outline">Outline</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="link">Link</Button>
<Button variant="text">Text</Button>
<Button variant="back" iconLeft={ArrowLeft}>Back</Button>

// Different sizes
<Button size="sm">Small</Button>
<Button size="default">Default</Button>
<Button size="lg">Large</Button>
<Button size="icon" iconLeft={Plus} iconOnly />
```

## Props

| Prop         | Type                                                                                                                                 | Default     | Description                                                                                                                     |
| ------------ | ------------------------------------------------------------------------------------------------------------------------------------ | ----------- | ------------------------------------------------------------------------------------------------------------------------------- |
| `variant`    | `'default' \| 'primary' \| 'destructive' \| 'delete' \| 'outline' \| 'secondary' \| 'ghost' \| 'link' \| 'text' \| 'info' \| 'back'` | `'default'` | The visual style of the button                                                                                                  |
| `size`       | `'default' \| 'sm' \| 'md' \| 'lg' \| 'icon'`                                                                                        | `'default'` | The size of the button                                                                                                          |
| `asChild`    | `boolean`                                                                                                                            | `false`     | Whether to render as a child component (using Radix UI's Slot)                                                                  |
| `isLoading`  | `boolean`                                                                                                                            | `false`     | Whether the button is in a loading state                                                                                        |
| `iconLeft`   | `React.ReactNode \| LucideIcon`                                                                                                      | -           | Icon to display on the left side of the button text. Can be a Lucide icon component (FileText) or a JSX element (<FileText />)  |
| `iconRight`  | `React.ReactNode \| LucideIcon`                                                                                                      | -           | Icon to display on the right side of the button text. Can be a Lucide icon component (FileText) or a JSX element (<FileText />) |
| `iconSize`   | `number`                                                                                                                             | `20`        | The size of the icon                                                                                                            |
| `tooltip`    | `string`                                                                                                                             | -           | Tooltip text for the button                                                                                                     |
| `iconOnly`   | `boolean`                                                                                                                            | `false`     | Whether the button is an icon-only button                                                                                       |
| `responsive` | `boolean`                                                                                                                            | `false`     | Whether the button should hide text when space is limited                                                                       |
| `extraSpace` | `number`                                                                                                                             | `16`        | Extra space to reserve for the button content (in pixels)                                                                       |

## Special Features

### Responsive Mode

When `responsive={true}`, the button will automatically hide its text content when there isn't enough space to display it, showing only the icon. This is useful for buttons in containers that might resize or on mobile devices.

The button will:

-   Measure the available space in its container
-   Compare it with the space needed for the full content
-   Hide the text and show only the icon when space is limited
-   Display a tooltip with the button text when hovered

Example:

```tsx
<div className="w-20">
    <Button iconLeft={Save} responsive={true}>
        Save Document
    </Button>
</div>
```

### Icon-Only Mode

When `iconOnly={true}`, the button will only display the icon, regardless of available space. This is useful for toolbar buttons or actions where the icon is self-explanatory.

The button will:

-   Hide the text content completely
-   Apply the `ghost` variant styling automatically
-   Display a tooltip with the provided tooltip text or button text when hovered

Example:

```tsx
<Button iconLeft={Plus} iconOnly tooltip="Add new item" />
```

## Variant Behaviors

### Back Button Variant

The `back` variant has special styling and behavior:

-   Transparent background
-   Text color of `text-accent` that changes to `text-light-blue-vivid-900` on hover
-   Default gap of 5px between icon and text
-   Left icon moves 5px to the left on hover with a smooth transition animation
-   No shadow or border

Example:

```tsx
<Button variant="back" iconLeft={ArrowLeft}>
    Back to Dashboard
</Button>
```

## Migration from AdaptiveButton

The `AdaptiveButton` component is now deprecated. Use the `Button` component directly instead.

The `Button` component includes all the functionality of `AdaptiveButton` with improved props:

-   Replace `icon` with either `iconLeft` or `iconRight`
-   Replace `iconPosition` with explicit `iconLeft` or `iconRight` props

After:

```tsx
<Button iconLeft={Save}>Save</Button>
<Button iconRight={ArrowRight}>Next</Button>
```

## Examples

See the `button-examples.tsx` file for more examples of how to use the Button component.

'use client'

import { useState } from 'react'
import { Button, Input, Label } from './ui'
import {
    ChatBubbleBottomCenterIcon,
    ChatBubbleBottomCenterTextIcon,
} from '@heroicons/react/24/outline'

const DailyCheckGroupField = (props: any) => {
    const [yes, setYes] = useState(props.defaultYesChecked)
    const [No, setNo] = useState(props.defaultNoChecked)

    function callyes() {
        props.handleYesChange()
        setYes(true)
        setNo(false)
    }
    function callNo() {
        props.handleNoChange()
        setNo(true)
        setYes(false)
    }
    const classes = {
        fieldWrapper:
            'flex flex-row gap-2 text-left items-center justify-between',
        inputWrapper: 'flex flex-row gap-2 justify-start items-center',
        radio: 'flex flex-row gap-1 items-center ',
        radioInput: 'w-6 h-6   ',
        radioLabel: '',
        textarea: 'block p-2.5 w-full mt-4    rounded-lg border   ',
        label: `  uppercase  `,
    }
    return (
        <div className={`dailyCheckGroup  ${props.displayField ? '' : ''} `}>
            <div className={`gap-2 flex`}>
                <div
                    className={` ${props.locked ? 'pointer-events-none opacity-60' : ''}`}>
                    <div className="md:w-8 md:h-8 w-6 h-6">
                        {No ? (
                            <img
                                className=" ring-1  p-0.5 rounded-full"
                                src="/sealogs-not-ok-check1.svg"
                                alt=""
                            />
                        ) : (
                            <img
                                className=" ring-1  p-0.5 rounded-full"
                                src="/sealogs-empty-check.svg"
                                alt=""
                                onClick={() => callNo()}
                            />
                        )}
                    </div>
                    <Input
                        id={`${props.inputId}-no_radio`}
                        type="radio"
                        name={`${props.inputId}-radio`}
                        onChange={() => callNo()}
                        className={' ' + 'hidden'}
                        defaultChecked={props.defaultNoChecked}
                    />
                    <Label
                        htmlFor={`${props.inputId}-no_radio`}
                        className={'cursor-pointer'}>
                        No
                    </Label>
                </div>
                <div
                    className={` ${props.locked ? 'pointer-events-none opacity-60' : ''}`}>
                    <div className="md:w-8 md:h-8 w-6 h-6">
                        {yes ? (
                            <img
                                className=" ring-1  p-0.5 rounded-full"
                                src="/sealogs-ok-check.svg"
                                alt=""
                            />
                        ) : (
                            <img
                                className=" ring-1  p-0.5 rounded-full"
                                src="/sealogs-empty-check.svg"
                                alt=""
                                onClick={() => callyes()}
                            />
                        )}
                    </div>
                    <Input
                        id={`${props.inputId}-yes_radio`}
                        type="radio"
                        name={`${props.inputId}-radio`}
                        onChange={() => callyes()}
                        className={' ' + 'hidden'}
                        defaultChecked={props.defaultYesChecked}
                    />
                    <Label
                        htmlFor={`${props.inputId}-yes_radio`}
                        className={'cursor-pointer'}>
                        Yes
                    </Label>
                </div>
                <Button className=" -mt-4" onClick={props.commentAction}>
                    {props.comment ? (
                        <ChatBubbleBottomCenterTextIcon className="w-6 h-6" />
                    ) : (
                        <ChatBubbleBottomCenterIcon className="w-6 h-6  " />
                    )}
                </Button>
            </div>
        </div>
    )
}

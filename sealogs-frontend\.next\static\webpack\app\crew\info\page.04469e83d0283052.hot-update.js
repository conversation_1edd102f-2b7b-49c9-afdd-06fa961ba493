"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/crew/allocated-tasks.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleBottomCenterTextIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/.pnpm/@heroicons+react@2.2.0_react@18.3.1/node_modules/@heroicons/react/24/outline/esm/ChatBubbleBottomCenterTextIcon.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst CrewAllocatedTasks = (param)=>{\n    let { taskList } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [filteredTaskList, setFilteredTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(taskList);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        vessel: null,\n        status: null,\n        keyword: null\n    });\n    // Update filtered list when taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        setFilteredTaskList(taskList);\n    }, [\n        taskList\n    ]);\n    // Apply all filters whenever filters or taskList changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (!taskList) return;\n        let filtered = [\n            ...taskList\n        ];\n        // Apply vessel filter - following the same pattern as maintenance list\n        if (filters.vessel) {\n            console.log(\"Applying vessel filter. filters.vessel:\", filters.vessel);\n            console.log(\"Is array?\", Array.isArray(filters.vessel));\n            // Handle both single and multi-select vessel filtering\n            let vesselIds = [];\n            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {\n                vesselIds = filters.vessel.map((item)=>String(item.value));\n                console.log(\"Multi-select vessel IDs:\", vesselIds);\n            } else if (filters.vessel && !Array.isArray(filters.vessel)) {\n                vesselIds = [\n                    String(filters.vessel.value)\n                ];\n                console.log(\"Single-select vessel ID:\", vesselIds);\n            }\n            if (vesselIds.length > 0) {\n                var _taskList_;\n                console.log(\"Sample task basicComponent:\", (_taskList_ = taskList[0]) === null || _taskList_ === void 0 ? void 0 : _taskList_.basicComponent);\n                filtered = filtered.filter((task)=>{\n                    var _task_basicComponent;\n                    const taskVesselId = String(task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.id);\n                    const match = vesselIds.includes(taskVesselId);\n                    console.log(\"Task \".concat(task.name, ' - basicComponent.id: \"').concat(taskVesselId, '\" (').concat(typeof taskVesselId, \"), vesselIds: [\").concat(vesselIds.join(\", \"), \"], match: \").concat(match));\n                    return match;\n                });\n                console.log(\"Filtered \".concat(filtered.length, \" tasks from \").concat(taskList.length, \" total\"));\n            }\n        }\n        // Apply status filter\n        if (filters.status) {\n            filtered = filtered.filter((task)=>(task === null || task === void 0 ? void 0 : task.status) === filters.status.value);\n        }\n        // Apply keyword filter\n        if (filters.keyword && filters.keyword.value) {\n            const keyword = filters.keyword.value.toLowerCase();\n            filtered = filtered.filter((task)=>{\n                var _task_name, _task_description, _task_comments;\n                return (task === null || task === void 0 ? void 0 : (_task_name = task.name) === null || _task_name === void 0 ? void 0 : _task_name.toLowerCase().includes(keyword)) || (task === null || task === void 0 ? void 0 : (_task_description = task.description) === null || _task_description === void 0 ? void 0 : _task_description.toLowerCase().includes(keyword)) || (task === null || task === void 0 ? void 0 : (_task_comments = task.comments) === null || _task_comments === void 0 ? void 0 : _task_comments.toLowerCase().includes(keyword));\n            });\n        }\n        setFilteredTaskList(filtered);\n    }, [\n        taskList,\n        filters\n    ]);\n    // Handle filter changes\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        console.log(\"Filter change - type: \".concat(type, \", data:\"), data);\n        setFilters((prev)=>({\n                ...prev,\n                [type]: data\n            }));\n    };\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: \"Task\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2, _task_isOverDue3, _task_isOverDue4, _task_isOverDue5, _task_isOverDue6, _task_isOverDue7, _task_isOverDue8, _task_isOverDue9, _task_isOverDue10, _task_isOverDue11, _task_isOverDue12, _task_isOverDue13, _task_isOverDue14, _task_isOverDue15, _task_isOverDue16, _task_isOverDue17, _task_isOverDue18, _task_isOverDue19;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-foreground flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/maintenance?taskID=\".concat(task.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                            className: \"focus:outline-none\",\n                                            children: task.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block rounded px-3 py-1 ml-3 \".concat((task === null || task === void 0 ? void 0 : (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\" ? \"alert\" : \"\", \" \").concat((task === null || task === void 0 ? void 0 : (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) === \"Low\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.status) === \"Upcoming\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status) === \"Completed\" ? \"success\" : \"\", \" \").concat((task === null || task === void 0 ? void 0 : (_task_isOverDue4 = task.isOverDue) === null || _task_isOverDue4 === void 0 ? void 0 : _task_isOverDue4.status) === \"Medium\" || (task === null || task === void 0 ? void 0 : (_task_isOverDue5 = task.isOverDue) === null || _task_isOverDue5 === void 0 ? void 0 : _task_isOverDue5.days) === \"Save As Draft\" ? \"text-yellow-600 bg-yellow-100\" : \"\"),\n                                            children: [\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue6 = task.isOverDue) === null || _task_isOverDue6 === void 0 ? void 0 : _task_isOverDue6.status) && [\n                                                    \"High\",\n                                                    \"Medium\",\n                                                    \"Low\"\n                                                ].includes(task.isOverDue.status) && (task === null || task === void 0 ? void 0 : (_task_isOverDue7 = task.isOverDue) === null || _task_isOverDue7 === void 0 ? void 0 : _task_isOverDue7.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue8 = task.isOverDue) === null || _task_isOverDue8 === void 0 ? void 0 : _task_isOverDue8.status) === \"Completed\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue9 = task.isOverDue) === null || _task_isOverDue9 === void 0 ? void 0 : _task_isOverDue9.days) === \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue10 = task.isOverDue) === null || _task_isOverDue10 === void 0 ? void 0 : _task_isOverDue10.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue11 = task.isOverDue) === null || _task_isOverDue11 === void 0 ? void 0 : _task_isOverDue11.status) === \"Upcoming\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue12 = task.isOverDue) === null || _task_isOverDue12 === void 0 ? void 0 : _task_isOverDue12.days),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue13 = task.isOverDue) === null || _task_isOverDue13 === void 0 ? void 0 : _task_isOverDue13.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue14 = task.isOverDue) === null || _task_isOverDue14 === void 0 ? void 0 : _task_isOverDue14.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue15 = task.isOverDue) === null || _task_isOverDue15 === void 0 ? void 0 : _task_isOverDue15.status),\n                                                (task === null || task === void 0 ? void 0 : (_task_isOverDue16 = task.isOverDue) === null || _task_isOverDue16 === void 0 ? void 0 : _task_isOverDue16.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(task === null || task === void 0 ? void 0 : (_task_isOverDue17 = task.isOverDue) === null || _task_isOverDue17 === void 0 ? void 0 : _task_isOverDue17.days) && (task === null || task === void 0 ? void 0 : (_task_isOverDue18 = task.isOverDue) === null || _task_isOverDue18 === void 0 ? void 0 : _task_isOverDue18.days) !== \"Save As Draft\" && (task === null || task === void 0 ? void 0 : (_task_isOverDue19 = task.isOverDue) === null || _task_isOverDue19 === void 0 ? void 0 : _task_isOverDue19.days)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-14 flex items-center pl-1\",\n                                    children: task.Comments !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"outline-none px-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleBottomCenterTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                                                className: \"w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"leading-loose\",\n                                                    children: task.comments\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 25\n                        }, undefined),\n                        task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1 text-sm text-muted-foreground\",\n                            children: task.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: \"Vessel\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_basicComponent;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: (task === null || task === void 0 ? void 0 : (_task_basicComponent = task.basicComponent) === null || _task_basicComponent === void 0 ? void 0 : _task_basicComponent.title) || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"inventoryItem\",\n            header: \"Inventory Item\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                var _task_inventory, _task_maintenanceSchedule_inventory, _task_maintenanceSchedule;\n                const task = row.original;\n                // Check for inventory from multiple possible sources\n                const inventoryItem = (task === null || task === void 0 ? void 0 : (_task_inventory = task.inventory) === null || _task_inventory === void 0 ? void 0 : _task_inventory.item) || (task === null || task === void 0 ? void 0 : (_task_maintenanceSchedule = task.maintenanceSchedule) === null || _task_maintenanceSchedule === void 0 ? void 0 : (_task_maintenanceSchedule_inventory = _task_maintenanceSchedule.inventory) === null || _task_maintenanceSchedule_inventory === void 0 ? void 0 : _task_maintenanceSchedule_inventory.item) || null;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: inventoryItem || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const task = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-right\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground mb-1\",\n                            children: [\n                                \"Due: \",\n                                (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(task.expires)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block rounded px-3 py-1 \".concat(task.status == \"Completed\" ? \"success\" : \"alert\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: task.status\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: !taskList ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 254,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n            columns: columns,\n            data: filteredTaskList || [],\n            showToolbar: true,\n            pageSize: 50,\n            showPageSizeSelector: false,\n            onChange: handleFilterOnChange\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\allocated-tasks.tsx\",\n            lineNumber: 256,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s(CrewAllocatedTasks, \"ma6jPe/tw3b936HXYaWPMLLamsk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams\n    ];\n});\n_c = CrewAllocatedTasks;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewAllocatedTasks);\nvar _c;\n$RefreshReg$(_c, \"CrewAllocatedTasks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy9hbGxvY2F0ZWQtdGFza3MudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEI7QUFDZ0Q7QUFDNUM7QUFDOEI7QUFDVDtBQUNSO0FBQzRCO0FBQzFCO0FBS2Y7QUFDVztBQUUzQyxNQUFNYyxxQkFBcUI7UUFBQyxFQUFFQyxRQUFRLEVBQXNCOztJQUN4RCxNQUFNQyxXQUFXYiw0REFBV0E7SUFDNUIsTUFBTWMsZUFBZWIsZ0VBQWVBO0lBQ3BDLE1BQU0sQ0FBQ2Msa0JBQWtCQyxvQkFBb0IsR0FBR1AsK0NBQVFBLENBQUNHO0lBQ3pELE1BQU0sQ0FBQ0ssU0FBU0MsV0FBVyxHQUFHVCwrQ0FBUUEsQ0FJbkM7UUFDQ1UsUUFBUTtRQUNSQyxRQUFRO1FBQ1JDLFNBQVM7SUFDYjtJQUVBLDZDQUE2QztJQUM3Q1gsZ0RBQVNBLENBQUM7UUFDTk0sb0JBQW9CSjtJQUN4QixHQUFHO1FBQUNBO0tBQVM7SUFFYix5REFBeUQ7SUFDekRGLGdEQUFTQSxDQUFDO1FBQ04sSUFBSSxDQUFDRSxVQUFVO1FBRWYsSUFBSVUsV0FBVztlQUFJVjtTQUFTO1FBRTVCLHVFQUF1RTtRQUN2RSxJQUFJSyxRQUFRRSxNQUFNLEVBQUU7WUFDaEJJLFFBQVFDLEdBQUcsQ0FDUCwyQ0FDQVAsUUFBUUUsTUFBTTtZQUVsQkksUUFBUUMsR0FBRyxDQUFDLGFBQWFDLE1BQU1DLE9BQU8sQ0FBQ1QsUUFBUUUsTUFBTTtZQUVyRCx1REFBdUQ7WUFDdkQsSUFBSVEsWUFBc0IsRUFBRTtZQUM1QixJQUFJRixNQUFNQyxPQUFPLENBQUNULFFBQVFFLE1BQU0sS0FBS0YsUUFBUUUsTUFBTSxDQUFDUyxNQUFNLEdBQUcsR0FBRztnQkFDNURELFlBQVlWLFFBQVFFLE1BQU0sQ0FBQ1UsR0FBRyxDQUFDLENBQUNDLE9BQzVCQyxPQUFPRCxLQUFLRSxLQUFLO2dCQUVyQlQsUUFBUUMsR0FBRyxDQUFDLDRCQUE0Qkc7WUFDNUMsT0FBTyxJQUFJVixRQUFRRSxNQUFNLElBQUksQ0FBQ00sTUFBTUMsT0FBTyxDQUFDVCxRQUFRRSxNQUFNLEdBQUc7Z0JBQ3pEUSxZQUFZO29CQUFDSSxPQUFPZCxRQUFRRSxNQUFNLENBQUNhLEtBQUs7aUJBQUU7Z0JBQzFDVCxRQUFRQyxHQUFHLENBQUMsNEJBQTRCRztZQUM1QztZQUVBLElBQUlBLFVBQVVDLE1BQU0sR0FBRyxHQUFHO29CQUdsQmhCO2dCQUZKVyxRQUFRQyxHQUFHLENBQ1AsZ0NBQ0FaLGFBQUFBLFFBQVEsQ0FBQyxFQUFFLGNBQVhBLGlDQUFBQSxXQUFhcUIsY0FBYztnQkFFL0JYLFdBQVdBLFNBQVNZLE1BQU0sQ0FBQyxDQUFDQzt3QkFDSUE7b0JBQTVCLE1BQU1DLGVBQWVMLE9BQU9JLGlCQUFBQSw0QkFBQUEsdUJBQUFBLEtBQU1GLGNBQWMsY0FBcEJFLDJDQUFBQSxxQkFBc0JFLEVBQUU7b0JBQ3BELE1BQU1DLFFBQVFYLFVBQVVZLFFBQVEsQ0FBQ0g7b0JBQ2pDYixRQUFRQyxHQUFHLENBQ1AsUUFBMkNZLE9BQW5DRCxLQUFLSyxJQUFJLEVBQUMsMkJBQTJDLE9BQWxCSixjQUFhLE9BQTBDVCxPQUFyQyxPQUFPUyxjQUFhLG1CQUFrREUsT0FBakNYLFVBQVVjLElBQUksQ0FBQyxPQUFNLGNBQWtCLE9BQU5IO29CQUV2SSxPQUFPQTtnQkFDWDtnQkFDQWYsUUFBUUMsR0FBRyxDQUNQLFlBQTBDWixPQUE5QlUsU0FBU00sTUFBTSxFQUFDLGdCQUE4QixPQUFoQmhCLFNBQVNnQixNQUFNLEVBQUM7WUFFbEU7UUFDSjtRQUVBLHNCQUFzQjtRQUN0QixJQUFJWCxRQUFRRyxNQUFNLEVBQUU7WUFDaEJFLFdBQVdBLFNBQVNZLE1BQU0sQ0FDdEIsQ0FBQ0MsT0FBY0EsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNZixNQUFNLE1BQUtILFFBQVFHLE1BQU0sQ0FBQ1ksS0FBSztRQUU1RDtRQUVBLHVCQUF1QjtRQUN2QixJQUFJZixRQUFRSSxPQUFPLElBQUlKLFFBQVFJLE9BQU8sQ0FBQ1csS0FBSyxFQUFFO1lBQzFDLE1BQU1YLFVBQVVKLFFBQVFJLE9BQU8sQ0FBQ1csS0FBSyxDQUFDVSxXQUFXO1lBQ2pEcEIsV0FBV0EsU0FBU1ksTUFBTSxDQUN0QixDQUFDQztvQkFDR0EsWUFDQUEsbUJBQ0FBO3VCQUZBQSxDQUFBQSxpQkFBQUEsNEJBQUFBLGFBQUFBLEtBQU1LLElBQUksY0FBVkwsaUNBQUFBLFdBQVlPLFdBQVcsR0FBR0gsUUFBUSxDQUFDbEIsY0FDbkNjLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU1RLFdBQVcsY0FBakJSLHdDQUFBQSxrQkFBbUJPLFdBQVcsR0FBR0gsUUFBUSxDQUFDbEIsY0FDMUNjLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1TLFFBQVEsY0FBZFQscUNBQUFBLGVBQWdCTyxXQUFXLEdBQUdILFFBQVEsQ0FBQ2xCOztRQUVuRDtRQUVBTCxvQkFBb0JNO0lBQ3hCLEdBQUc7UUFBQ1Y7UUFBVUs7S0FBUTtJQUV0Qix3QkFBd0I7SUFDeEIsTUFBTTRCLHVCQUF1QjtZQUFDLEVBQUVDLElBQUksRUFBRUMsSUFBSSxFQUFPO1FBQzdDeEIsUUFBUUMsR0FBRyxDQUFDLHlCQUE4QixPQUFMc0IsTUFBSyxZQUFVQztRQUNwRDdCLFdBQVcsQ0FBQzhCLE9BQVU7Z0JBQ2xCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0YsS0FBSyxFQUFFQztZQUNaO0lBQ0o7SUFFQSxtQ0FBbUM7SUFDbkMsTUFBTUUsVUFBeUM7UUFDM0M7WUFDSUMsYUFBYTtZQUNiQyxRQUFRO1lBQ1JDLGVBQWU7WUFDZkMsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO29CQWFBbkIsaUJBSUFBLGtCQUNBQSxrQkFFQUEsa0JBSUFBLGtCQUNBQSxrQkFLSEEsa0JBSUdBLGtCQUNIQSxrQkFDR0Esa0JBRUFBLG1CQUNIQSxtQkFDR0EsbUJBQ0hBLG1CQUNXQSxtQkFDUkEsbUJBQ0hBLG1CQUNZQSxtQkFDVEEsbUJBRUFBO2dCQS9DeEIsTUFBTUEsT0FBT21CLElBQUlDLFFBQVE7Z0JBQ3pCLHFCQUNJLDhEQUFDQzs7c0NBQ0csOERBQUNBOzRCQUFJQyxXQUFVOzs4Q0FDWCw4REFBQ0M7b0NBQUtELFdBQVU7O3NEQUNaLDhEQUFDNUQsaURBQUlBOzRDQUNEOEQsTUFBTSx1QkFBOEM5QyxPQUF2QnNCLEtBQUtFLEVBQUUsRUFBQyxpQkFBMkJ2QixPQUFaRCxVQUFTLEtBQTJCLE9BQXhCQyxhQUFhOEMsUUFBUTs0Q0FDckZILFdBQVU7c0RBQ1R0QixLQUFLSyxJQUFJOzs7Ozs7c0RBRWQsOERBQUNnQjs0Q0FDR0MsV0FBVyx1Q0FLUHRCLE9BSkFBLENBQUFBLGlCQUFBQSw0QkFBQUEsa0JBQUFBLEtBQU0wQixTQUFTLGNBQWYxQixzQ0FBQUEsZ0JBQWlCZixNQUFNLE1BQUssU0FDdEIsVUFDQSxJQUNULEtBUUdlLE9BUEFBLENBQUFBLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU0wQixTQUFTLGNBQWYxQix1Q0FBQUEsaUJBQWlCZixNQUFNLE1BQUssU0FDNUJlLENBQUFBLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU0wQixTQUFTLGNBQWYxQix1Q0FBQUEsaUJBQWlCZixNQUFNLE1BQ25CLGNBQ0plLENBQUFBLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU0wQixTQUFTLGNBQWYxQix1Q0FBQUEsaUJBQWlCZixNQUFNLE1BQUssY0FDdEIsWUFDQSxJQUNULEtBTUEsT0FMR2UsQ0FBQUEsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTTBCLFNBQVMsY0FBZjFCLHVDQUFBQSxpQkFBaUJmLE1BQU0sTUFBSyxZQUM1QmUsQ0FBQUEsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTTBCLFNBQVMsY0FBZjFCLHVDQUFBQSxpQkFBaUIyQixJQUFJLE1BQ2pCLGtCQUNFLGtDQUNBOztnREFFVDNCLENBQUFBLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU0wQixTQUFTLGNBQWYxQix1Q0FBQUEsaUJBQWlCZixNQUFNLEtBQ3BCO29EQUFDO29EQUFRO29EQUFVO2lEQUFNLENBQUNtQixRQUFRLENBQzlCSixLQUFLMEIsU0FBUyxDQUFDekMsTUFBTSxNQUV6QmUsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTTBCLFNBQVMsY0FBZjFCLHVDQUFBQSxpQkFBaUIyQixJQUFJO2dEQUN4QjNCLENBQUFBLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU0wQixTQUFTLGNBQWYxQix1Q0FBQUEsaUJBQWlCZixNQUFNLE1BQUssZUFDekJlLENBQUFBLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU0wQixTQUFTLGNBQWYxQix1Q0FBQUEsaUJBQWlCMkIsSUFBSSxNQUNqQixvQkFDSjNCLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU0wQixTQUFTLGNBQWYxQix3Q0FBQUEsa0JBQWlCMkIsSUFBSTtnREFDeEIzQixDQUFBQSxpQkFBQUEsNEJBQUFBLG9CQUFBQSxLQUFNMEIsU0FBUyxjQUFmMUIsd0NBQUFBLGtCQUFpQmYsTUFBTSxNQUFLLGVBQ3pCZSxpQkFBQUEsNEJBQUFBLG9CQUFBQSxLQUFNMEIsU0FBUyxjQUFmMUIsd0NBQUFBLGtCQUFpQjJCLElBQUk7Z0RBQ3hCM0IsQ0FBQUEsaUJBQUFBLDRCQUFBQSxvQkFBQUEsS0FBTTBCLFNBQVMsY0FBZjFCLHdDQUFBQSxrQkFBaUJmLE1BQU0sTUFBSyxlQUN6QnJCLHFEQUFPQSxDQUFDb0MsaUJBQUFBLDRCQUFBQSxvQkFBQUEsS0FBTTBCLFNBQVMsY0FBZjFCLHdDQUFBQSxrQkFBaUIyQixJQUFJLE1BQzdCM0IsaUJBQUFBLDRCQUFBQSxvQkFBQUEsS0FBTTBCLFNBQVMsY0FBZjFCLHdDQUFBQSxrQkFBaUJmLE1BQU07Z0RBQzFCZSxDQUFBQSxpQkFBQUEsNEJBQUFBLG9CQUFBQSxLQUFNMEIsU0FBUyxjQUFmMUIsd0NBQUFBLGtCQUFpQmYsTUFBTSxNQUFLLGVBQ3pCLENBQUNyQixxREFBT0EsQ0FBQ29DLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU0wQixTQUFTLGNBQWYxQix3Q0FBQUEsa0JBQWlCMkIsSUFBSSxLQUM5QjNCLENBQUFBLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU0wQixTQUFTLGNBQWYxQix3Q0FBQUEsa0JBQWlCMkIsSUFBSSxNQUNqQixvQkFDSjNCLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU0wQixTQUFTLGNBQWYxQix3Q0FBQUEsa0JBQWlCMkIsSUFBSTs7Ozs7Ozs7Ozs7Ozs4Q0FHakMsOERBQUNOO29DQUFJQyxXQUFVOzhDQUNWdEIsS0FBSzRCLFFBQVEsS0FBSyxzQkFDZiw4REFBQ3pELDJEQUFPQTs7MERBQ0osOERBQUNFLGtFQUFjQTtnREFBQ3dELE9BQU87MERBQ25CLDRFQUFDM0QseURBQU1BO29EQUNINEQsU0FBUTtvREFDUkMsTUFBSztvREFDTFQsV0FBVTs4REFDViw0RUFBQzNELHlIQUE4QkE7d0RBQUMyRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzBEQUdsRCw4REFBQ2xELGtFQUFjQTtnREFBQ2tELFdBQVU7MERBQ3RCLDRFQUFDRDtvREFBSUMsV0FBVTs4REFDVnRCLEtBQUtTLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBT3JDVCxLQUFLUSxXQUFXLGtCQUNiLDhEQUFDYTs0QkFBSUMsV0FBVTtzQ0FDVnRCLEtBQUtRLFdBQVc7Ozs7Ozs7Ozs7OztZQUtyQztRQUNKO1FBQ0E7WUFDSU8sYUFBYTtZQUNiQyxRQUFRO1lBQ1JDLGVBQWU7WUFDZkMsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO29CQUlmbkI7Z0JBSFQsTUFBTUEsT0FBT21CLElBQUlDLFFBQVE7Z0JBQ3pCLHFCQUNJLDhEQUFDQztvQkFBSUMsV0FBVTs4QkFDVnRCLENBQUFBLGlCQUFBQSw0QkFBQUEsdUJBQUFBLEtBQU1GLGNBQWMsY0FBcEJFLDJDQUFBQSxxQkFBc0JnQyxLQUFLLEtBQUk7Ozs7OztZQUc1QztRQUNKO1FBQ0E7WUFDSWpCLGFBQWE7WUFDYkMsUUFBUTtZQUNSQyxlQUFlO1lBQ2ZDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtvQkFJcEJuQixpQkFDQUEscUNBQUFBO2dCQUpKLE1BQU1BLE9BQU9tQixJQUFJQyxRQUFRO2dCQUN6QixxREFBcUQ7Z0JBQ3JELE1BQU1hLGdCQUNGakMsQ0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTWtDLFNBQVMsY0FBZmxDLHNDQUFBQSxnQkFBaUJMLElBQUksTUFDckJLLGlCQUFBQSw0QkFBQUEsNEJBQUFBLEtBQU1tQyxtQkFBbUIsY0FBekJuQyxpREFBQUEsc0NBQUFBLDBCQUEyQmtDLFNBQVMsY0FBcENsQywwREFBQUEsb0NBQXNDTCxJQUFJLEtBQzFDO2dCQUVKLHFCQUNJLDhEQUFDMEI7b0JBQUlDLFdBQVU7OEJBQWVXLGlCQUFpQjs7Ozs7O1lBRXZEO1FBQ0o7UUFDQTtZQUNJbEIsYUFBYTtZQUNiQyxRQUFRO1lBQ1JDLGVBQWU7WUFDZkMsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNbkIsT0FBT21CLElBQUlDLFFBQVE7Z0JBQ3pCLHFCQUNJLDhEQUFDQztvQkFBSUMsV0FBVTs7c0NBQ1gsOERBQUNEOzRCQUFJQyxXQUFVOztnQ0FBcUM7Z0NBQzFDdkQsbUVBQVVBLENBQUNpQyxLQUFLb0MsT0FBTzs7Ozs7OztzQ0FFakMsOERBQUNmOzRCQUNHQyxXQUFXLGtDQUVWLE9BREd0QixLQUFLZixNQUFNLElBQUksY0FBYyxZQUFZO3NDQUU3Qyw0RUFBQ3NDOzBDQUFNdkIsS0FBS2YsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJbEM7UUFDSjtLQUNIO0lBRUQscUJBQ0k7a0JBQ0ssQ0FBQ1IseUJBQ0UsOERBQUNULHVEQUFJQTs7OztzQ0FFTCw4REFBQ0MsZ0VBQVNBO1lBQ042QyxTQUFTQTtZQUNURixNQUFNaEMsb0JBQW9CLEVBQUU7WUFDNUJ5RCxhQUFhO1lBQ2JDLFVBQVU7WUFDVkMsc0JBQXNCO1lBQ3RCQyxVQUFVOUI7Ozs7Ozs7QUFLOUI7R0F6UE1sQzs7UUFDZVgsd0RBQVdBO1FBQ1BDLDREQUFlQTs7O0tBRmxDVTtBQTJQTiwrREFBZUEsa0JBQWtCQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdWkvY3Jldy9hbGxvY2F0ZWQtdGFza3MudHN4P2EyY2YiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXHJcbmltcG9ydCB7IENoYXRCdWJibGVCb3R0b21DZW50ZXJUZXh0SWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSdcclxuaW1wb3J0IHsgaXNFbXB0eSB9IGZyb20gJ2xvZGFzaCdcclxuaW1wb3J0IHsgdXNlUGF0aG5hbWUsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcclxuaW1wb3J0IHsgZm9ybWF0RGF0ZSB9IGZyb20gJ0AvYXBwL2hlbHBlcnMvZGF0ZUhlbHBlcidcclxuaW1wb3J0IHsgTGlzdCB9IGZyb20gJ0AvY29tcG9uZW50cy9za2VsZXRvbnMnXHJcbmltcG9ydCB7IERhdGFUYWJsZSwgRXh0ZW5kZWRDb2x1bW5EZWYgfSBmcm9tICdAL2NvbXBvbmVudHMvZmlsdGVyZWRUYWJsZSdcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcclxuaW1wb3J0IHtcclxuICAgIFBvcG92ZXIsXHJcbiAgICBQb3BvdmVyQ29udGVudCxcclxuICAgIFBvcG92ZXJUcmlnZ2VyLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9wb3BvdmVyJ1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXHJcblxyXG5jb25zdCBDcmV3QWxsb2NhdGVkVGFza3MgPSAoeyB0YXNrTGlzdCB9OiB7IHRhc2tMaXN0PzogYW55IH0pID0+IHtcclxuICAgIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxyXG4gICAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKClcclxuICAgIGNvbnN0IFtmaWx0ZXJlZFRhc2tMaXN0LCBzZXRGaWx0ZXJlZFRhc2tMaXN0XSA9IHVzZVN0YXRlKHRhc2tMaXN0KVxyXG4gICAgY29uc3QgW2ZpbHRlcnMsIHNldEZpbHRlcnNdID0gdXNlU3RhdGU8e1xyXG4gICAgICAgIHZlc3NlbDogYW55XHJcbiAgICAgICAgc3RhdHVzOiBhbnlcclxuICAgICAgICBrZXl3b3JkOiBhbnlcclxuICAgIH0+KHtcclxuICAgICAgICB2ZXNzZWw6IG51bGwsXHJcbiAgICAgICAgc3RhdHVzOiBudWxsLFxyXG4gICAgICAgIGtleXdvcmQ6IG51bGwsXHJcbiAgICB9KVxyXG5cclxuICAgIC8vIFVwZGF0ZSBmaWx0ZXJlZCBsaXN0IHdoZW4gdGFza0xpc3QgY2hhbmdlc1xyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBzZXRGaWx0ZXJlZFRhc2tMaXN0KHRhc2tMaXN0KVxyXG4gICAgfSwgW3Rhc2tMaXN0XSlcclxuXHJcbiAgICAvLyBBcHBseSBhbGwgZmlsdGVycyB3aGVuZXZlciBmaWx0ZXJzIG9yIHRhc2tMaXN0IGNoYW5nZXNcclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKCF0YXNrTGlzdCkgcmV0dXJuXHJcblxyXG4gICAgICAgIGxldCBmaWx0ZXJlZCA9IFsuLi50YXNrTGlzdF1cclxuXHJcbiAgICAgICAgLy8gQXBwbHkgdmVzc2VsIGZpbHRlciAtIGZvbGxvd2luZyB0aGUgc2FtZSBwYXR0ZXJuIGFzIG1haW50ZW5hbmNlIGxpc3RcclxuICAgICAgICBpZiAoZmlsdGVycy52ZXNzZWwpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICAgICAgICAnQXBwbHlpbmcgdmVzc2VsIGZpbHRlci4gZmlsdGVycy52ZXNzZWw6JyxcclxuICAgICAgICAgICAgICAgIGZpbHRlcnMudmVzc2VsLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdJcyBhcnJheT8nLCBBcnJheS5pc0FycmF5KGZpbHRlcnMudmVzc2VsKSlcclxuXHJcbiAgICAgICAgICAgIC8vIEhhbmRsZSBib3RoIHNpbmdsZSBhbmQgbXVsdGktc2VsZWN0IHZlc3NlbCBmaWx0ZXJpbmdcclxuICAgICAgICAgICAgbGV0IHZlc3NlbElkczogc3RyaW5nW10gPSBbXVxyXG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmaWx0ZXJzLnZlc3NlbCkgJiYgZmlsdGVycy52ZXNzZWwubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICAgICAgdmVzc2VsSWRzID0gZmlsdGVycy52ZXNzZWwubWFwKChpdGVtOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgU3RyaW5nKGl0ZW0udmFsdWUpLFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ011bHRpLXNlbGVjdCB2ZXNzZWwgSURzOicsIHZlc3NlbElkcylcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChmaWx0ZXJzLnZlc3NlbCAmJiAhQXJyYXkuaXNBcnJheShmaWx0ZXJzLnZlc3NlbCkpIHtcclxuICAgICAgICAgICAgICAgIHZlc3NlbElkcyA9IFtTdHJpbmcoZmlsdGVycy52ZXNzZWwudmFsdWUpXVxyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1NpbmdsZS1zZWxlY3QgdmVzc2VsIElEOicsIHZlc3NlbElkcylcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgaWYgKHZlc3NlbElkcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgICAgICAgICAgICAnU2FtcGxlIHRhc2sgYmFzaWNDb21wb25lbnQ6JyxcclxuICAgICAgICAgICAgICAgICAgICB0YXNrTGlzdFswXT8uYmFzaWNDb21wb25lbnQsXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcigodGFzazogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdGFza1Zlc3NlbElkID0gU3RyaW5nKHRhc2s/LmJhc2ljQ29tcG9uZW50Py5pZClcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBtYXRjaCA9IHZlc3NlbElkcy5pbmNsdWRlcyh0YXNrVmVzc2VsSWQpXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGBUYXNrICR7dGFzay5uYW1lfSAtIGJhc2ljQ29tcG9uZW50LmlkOiBcIiR7dGFza1Zlc3NlbElkfVwiICgke3R5cGVvZiB0YXNrVmVzc2VsSWR9KSwgdmVzc2VsSWRzOiBbJHt2ZXNzZWxJZHMuam9pbignLCAnKX1dLCBtYXRjaDogJHttYXRjaH1gLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbWF0Y2hcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgICAgICAgICAgICBgRmlsdGVyZWQgJHtmaWx0ZXJlZC5sZW5ndGh9IHRhc2tzIGZyb20gJHt0YXNrTGlzdC5sZW5ndGh9IHRvdGFsYCxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQXBwbHkgc3RhdHVzIGZpbHRlclxyXG4gICAgICAgIGlmIChmaWx0ZXJzLnN0YXR1cykge1xyXG4gICAgICAgICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihcclxuICAgICAgICAgICAgICAgICh0YXNrOiBhbnkpID0+IHRhc2s/LnN0YXR1cyA9PT0gZmlsdGVycy5zdGF0dXMudmFsdWUsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEFwcGx5IGtleXdvcmQgZmlsdGVyXHJcbiAgICAgICAgaWYgKGZpbHRlcnMua2V5d29yZCAmJiBmaWx0ZXJzLmtleXdvcmQudmFsdWUpIHtcclxuICAgICAgICAgICAgY29uc3Qga2V5d29yZCA9IGZpbHRlcnMua2V5d29yZC52YWx1ZS50b0xvd2VyQ2FzZSgpXHJcbiAgICAgICAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgKHRhc2s6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICB0YXNrPy5uYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGtleXdvcmQpIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgdGFzaz8uZGVzY3JpcHRpb24/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoa2V5d29yZCkgfHxcclxuICAgICAgICAgICAgICAgICAgICB0YXNrPy5jb21tZW50cz8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhrZXl3b3JkKSxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgc2V0RmlsdGVyZWRUYXNrTGlzdChmaWx0ZXJlZClcclxuICAgIH0sIFt0YXNrTGlzdCwgZmlsdGVyc10pXHJcblxyXG4gICAgLy8gSGFuZGxlIGZpbHRlciBjaGFuZ2VzXHJcbiAgICBjb25zdCBoYW5kbGVGaWx0ZXJPbkNoYW5nZSA9ICh7IHR5cGUsIGRhdGEgfTogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5sb2coYEZpbHRlciBjaGFuZ2UgLSB0eXBlOiAke3R5cGV9LCBkYXRhOmAsIGRhdGEpXHJcbiAgICAgICAgc2V0RmlsdGVycygocHJldikgPT4gKHtcclxuICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgW3R5cGVdOiBkYXRhLFxyXG4gICAgICAgIH0pKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIERlZmluZSBjb2x1bW5zIGZvciB0aGUgRGF0YVRhYmxlXHJcbiAgICBjb25zdCBjb2x1bW5zOiBFeHRlbmRlZENvbHVtbkRlZjxhbnksIGFueT5bXSA9IFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndGl0bGUnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdUYXNrJyxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0YXNrID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVncm91bmQgZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL21haW50ZW5hbmNlP3Rhc2tJRD0ke3Rhc2suaWR9JnJlZGlyZWN0X3RvPSR7cGF0aG5hbWV9PyR7c2VhcmNoUGFyYW1zLnRvU3RyaW5nKCl9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9jdXM6b3V0bGluZS1ub25lXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0YXNrLm5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaW5saW5lLWJsb2NrIHJvdW5kZWQgcHgtMyBweS0xIG1sLTMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2s/LmlzT3ZlckR1ZT8uc3RhdHVzID09PSAnSGlnaCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdhbGVydCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2s/LmlzT3ZlckR1ZT8uc3RhdHVzID09PSAnTG93JyB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzaz8uaXNPdmVyRHVlPy5zdGF0dXMgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1VwY29taW5nJyB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzaz8uaXNPdmVyRHVlPy5zdGF0dXMgPT09ICdDb21wbGV0ZWQnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnc3VjY2VzcydcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2s/LmlzT3ZlckR1ZT8uc3RhdHVzID09PSAnTWVkaXVtJyB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzaz8uaXNPdmVyRHVlPy5kYXlzID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdTYXZlIEFzIERyYWZ0J1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3RleHQteWVsbG93LTYwMCBiZy15ZWxsb3ctMTAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJydcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzaz8uaXNPdmVyRHVlPy5zdGF0dXMgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsnSGlnaCcsICdNZWRpdW0nLCAnTG93J10uaW5jbHVkZXMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzay5pc092ZXJEdWUuc3RhdHVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzaz8uaXNPdmVyRHVlPy5kYXlzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzaz8uaXNPdmVyRHVlPy5zdGF0dXMgPT09ICdDb21wbGV0ZWQnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrPy5pc092ZXJEdWU/LmRheXMgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1NhdmUgQXMgRHJhZnQnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrPy5pc092ZXJEdWU/LmRheXN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0YXNrPy5pc092ZXJEdWU/LnN0YXR1cyA9PT0gJ1VwY29taW5nJyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzaz8uaXNPdmVyRHVlPy5kYXlzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzaz8uaXNPdmVyRHVlPy5zdGF0dXMgPT09ICdDb21wbGV0ZWQnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0VtcHR5KHRhc2s/LmlzT3ZlckR1ZT8uZGF5cykgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2s/LmlzT3ZlckR1ZT8uc3RhdHVzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzaz8uaXNPdmVyRHVlPy5zdGF0dXMgPT09ICdDb21wbGV0ZWQnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAhaXNFbXB0eSh0YXNrPy5pc092ZXJEdWU/LmRheXMpICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrPy5pc092ZXJEdWU/LmRheXMgIT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1NhdmUgQXMgRHJhZnQnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrPy5pc092ZXJEdWU/LmRheXN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTQgZmxleCBpdGVtcy1jZW50ZXIgcGwtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0YXNrLkNvbW1lbnRzICE9PSBudWxsICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvcG92ZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UG9wb3ZlclRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib3V0bGluZS1ub25lIHB4LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoYXRCdWJibGVCb3R0b21DZW50ZXJUZXh0SWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvUG9wb3ZlclRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UG9wb3ZlckNvbnRlbnQgY2xhc3NOYW1lPVwidy02NFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGVhZGluZy1sb29zZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzay5jb21tZW50c31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvUG9wb3ZlckNvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvUG9wb3Zlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7dGFzay5kZXNjcmlwdGlvbiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzay5kZXNjcmlwdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3Zlc3NlbCcsXHJcbiAgICAgICAgICAgIGhlYWRlcjogJ1Zlc3NlbCcsXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdjZW50ZXInLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0YXNrID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3Rhc2s/LmJhc2ljQ29tcG9uZW50Py50aXRsZSB8fCAnTi9BJ31cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdpbnZlbnRvcnlJdGVtJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAnSW52ZW50b3J5IEl0ZW0nLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnY2VudGVyJyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdGFzayA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgLy8gQ2hlY2sgZm9yIGludmVudG9yeSBmcm9tIG11bHRpcGxlIHBvc3NpYmxlIHNvdXJjZXNcclxuICAgICAgICAgICAgICAgIGNvbnN0IGludmVudG9yeUl0ZW0gPVxyXG4gICAgICAgICAgICAgICAgICAgIHRhc2s/LmludmVudG9yeT8uaXRlbSB8fFxyXG4gICAgICAgICAgICAgICAgICAgIHRhc2s/Lm1haW50ZW5hbmNlU2NoZWR1bGU/LmludmVudG9yeT8uaXRlbSB8fFxyXG4gICAgICAgICAgICAgICAgICAgIG51bGxcclxuXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj57aW52ZW50b3J5SXRlbSB8fCAnTi9BJ308L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdzdGF0dXMnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdTdGF0dXMnLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAncmlnaHQnLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0YXNrID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIER1ZToge2Zvcm1hdERhdGUodGFzay5leHBpcmVzKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGlubGluZS1ibG9jayByb3VuZGVkIHB4LTMgcHktMSAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2suc3RhdHVzID09ICdDb21wbGV0ZWQnID8gJ3N1Y2Nlc3MnIDogJ2FsZXJ0J1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3Rhc2suc3RhdHVzfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgIF1cclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDw+XHJcbiAgICAgICAgICAgIHshdGFza0xpc3QgPyAoXHJcbiAgICAgICAgICAgICAgICA8TGlzdCAvPlxyXG4gICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPERhdGFUYWJsZVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgICAgICAgICAgICAgZGF0YT17ZmlsdGVyZWRUYXNrTGlzdCB8fCBbXX1cclxuICAgICAgICAgICAgICAgICAgICBzaG93VG9vbGJhcj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgICBwYWdlU2l6ZT17NTB9XHJcbiAgICAgICAgICAgICAgICAgICAgc2hvd1BhZ2VTaXplU2VsZWN0b3I9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWx0ZXJPbkNoYW5nZX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgPC8+XHJcbiAgICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IENyZXdBbGxvY2F0ZWRUYXNrc1xyXG4iXSwibmFtZXMiOlsiTGluayIsIkNoYXRCdWJibGVCb3R0b21DZW50ZXJUZXh0SWNvbiIsImlzRW1wdHkiLCJ1c2VQYXRobmFtZSIsInVzZVNlYXJjaFBhcmFtcyIsImZvcm1hdERhdGUiLCJMaXN0IiwiRGF0YVRhYmxlIiwiQnV0dG9uIiwiUG9wb3ZlciIsIlBvcG92ZXJDb250ZW50IiwiUG9wb3ZlclRyaWdnZXIiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNyZXdBbGxvY2F0ZWRUYXNrcyIsInRhc2tMaXN0IiwicGF0aG5hbWUiLCJzZWFyY2hQYXJhbXMiLCJmaWx0ZXJlZFRhc2tMaXN0Iiwic2V0RmlsdGVyZWRUYXNrTGlzdCIsImZpbHRlcnMiLCJzZXRGaWx0ZXJzIiwidmVzc2VsIiwic3RhdHVzIiwia2V5d29yZCIsImZpbHRlcmVkIiwiY29uc29sZSIsImxvZyIsIkFycmF5IiwiaXNBcnJheSIsInZlc3NlbElkcyIsImxlbmd0aCIsIm1hcCIsIml0ZW0iLCJTdHJpbmciLCJ2YWx1ZSIsImJhc2ljQ29tcG9uZW50IiwiZmlsdGVyIiwidGFzayIsInRhc2tWZXNzZWxJZCIsImlkIiwibWF0Y2giLCJpbmNsdWRlcyIsIm5hbWUiLCJqb2luIiwidG9Mb3dlckNhc2UiLCJkZXNjcmlwdGlvbiIsImNvbW1lbnRzIiwiaGFuZGxlRmlsdGVyT25DaGFuZ2UiLCJ0eXBlIiwiZGF0YSIsInByZXYiLCJjb2x1bW5zIiwiYWNjZXNzb3JLZXkiLCJoZWFkZXIiLCJjZWxsQWxpZ25tZW50IiwiY2VsbCIsInJvdyIsIm9yaWdpbmFsIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsImhyZWYiLCJ0b1N0cmluZyIsImlzT3ZlckR1ZSIsImRheXMiLCJDb21tZW50cyIsImFzQ2hpbGQiLCJ2YXJpYW50Iiwic2l6ZSIsInRpdGxlIiwiaW52ZW50b3J5SXRlbSIsImludmVudG9yeSIsIm1haW50ZW5hbmNlU2NoZWR1bGUiLCJleHBpcmVzIiwic2hvd1Rvb2xiYXIiLCJwYWdlU2l6ZSIsInNob3dQYWdlU2l6ZVNlbGVjdG9yIiwib25DaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\n"));

/***/ })

});